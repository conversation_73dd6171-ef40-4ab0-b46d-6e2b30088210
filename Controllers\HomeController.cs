using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using NewsSite.Models;
using NewsSite.Services;

namespace NewsSite.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly IContentService _contentService;
        private readonly ICategoryService _categoryService;

        public HomeController(
            ILogger<HomeController> logger,
            IContentService contentService,
            ICategoryService categoryService)
        {
            _logger = logger;
            _contentService = contentService;
            _categoryService = categoryService;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                // Fetch all data in parallel for better performance
                var categoriesTask = _categoryService.GetActiveCategoriesWithStatsAsync();
                var featuredTask = _contentService.GetFeaturedArticlesAsync(6);
                var latestTask = _contentService.GetLatestArticlesAsync(12);
                var trendingTask = _contentService.GetTrendingArticlesAsync(8);

                await Task.WhenAll(categoriesTask, featuredTask, latestTask, trendingTask);

                var viewModel = new HomeViewModel
                {
                    Categories = MapToDto(categoriesTask.Result),
                    FeaturedArticles = MapToDto(featuredTask.Result),
                    LatestArticles = MapToDto(latestTask.Result),
                    TrendingArticles = MapToDto(trendingTask.Result)
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading home page data");
                
                // Return empty view model on error to prevent page crash
                var emptyViewModel = new HomeViewModel();
                ViewBag.HasError = true;
                ViewBag.ErrorMessage = "Unable to load latest articles. Please try refreshing the page.";
                
                return View(emptyViewModel);
            }
        }

        private static IEnumerable<CategoryDto> MapToDto(IEnumerable<CategoryWithStats> categories)
        {
            return categories.Select(c => new CategoryDto
            {
                Id = c.Category.Id,
                Name = c.Category.Name,
                Slug = c.Category.Slug,
                Description = c.Category.Description,
                Color = c.Category.Color,
                Icon = c.Category.Icon,
                ArticleCount = c.ArticleCount,
                PublishedArticleCount = c.PublishedArticleCount,
                IsActive = c.Category.IsActive,
                DisplayOrder = c.Category.DisplayOrder
            });
        }

        private static IEnumerable<ArticleDto> MapToDto(IEnumerable<Article> articles)
        {
            return articles.Select(a => new ArticleDto
            {
                Id = a.Id,
                Title = a.Title,
                Slug = a.Slug,
                Summary = a.Summary,
                ImageUrl = a.ImageUrl,
                ImageAlt = a.ImageAlt,
                Author = a.Author,
                PublishedDate = a.PublishedDate,
                IsFeatured = a.IsFeatured,
                IsBreaking = a.IsBreaking,
                IsTrending = a.IsTrending,
                CategoryName = a.Category?.Name ?? "Uncategorized",
                CategorySlug = a.Category?.Slug ?? "uncategorized",
                CategoryColor = a.Category?.Color,
                CategoryIcon = a.Category?.Icon,
                SourceName = a.Source?.Name ?? "Unknown Source",
                ViewCount = a.ViewCount,
                ReadingTimeMinutes = a.Metadata?.ReadingTimeMinutes ?? CalculateReadingTime(a.Summary),
                TimeAgo = GetTimeAgo(a.PublishedDate)
            });
        }

        private static int CalculateReadingTime(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return 1;

            var wordCount = text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
            const int wordsPerMinute = 200; // Average reading speed
            return Math.Max(1, (int)Math.Ceiling((double)wordCount / wordsPerMinute));
        }

        private static string GetTimeAgo(DateTime publishedDate)
        {
            var timeSpan = DateTime.UtcNow - publishedDate;

            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} minutes ago";
            
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} hours ago";
            
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} days ago";
            
            if (timeSpan.TotalDays < 30)
                return $"{(int)(timeSpan.TotalDays / 7)} weeks ago";
            
            if (timeSpan.TotalDays < 365)
                return $"{(int)(timeSpan.TotalDays / 30)} months ago";
            
            return $"{(int)(timeSpan.TotalDays / 365)} years ago";
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
