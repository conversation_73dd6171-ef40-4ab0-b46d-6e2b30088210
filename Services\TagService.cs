using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NewsSite.Data;
using NewsSite.Models;

namespace NewsSite.Services
{
    public class TagService : ITagService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<TagService> _logger;
        private readonly IMemoryCache _cache;
        private const int DefaultCacheExpirationMinutes = 20;

        public TagService(ApplicationDbContext context, ILogger<TagService> logger, IMemoryCache cache)
        {
            _context = context;
            _logger = logger;
            _cache = cache;
        }

        #region Basic CRUD Operations

        public async Task<Tag?> GetByIdAsync(int id)
        {
            try
            {
                var cacheKey = $"tag_{id}";
                if (_cache.TryGetValue(cacheKey, out Tag? cachedTag))
                {
                    return cachedTag;
                }

                var tag = await _context.Tags.FirstOrDefaultAsync(t => t.Id == id);

                if (tag != null)
                {
                    _cache.Set(cacheKey, tag, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return tag;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tag with ID {TagId}", id);
                throw;
            }
        }

        public async Task<Tag?> GetBySlugAsync(string slug)
        {
            try
            {
                var cacheKey = $"tag_slug_{slug}";
                if (_cache.TryGetValue(cacheKey, out Tag? cachedTag))
                {
                    return cachedTag;
                }

                var tag = await _context.Tags.FirstOrDefaultAsync(t => t.Slug == slug);

                if (tag != null)
                {
                    _cache.Set(cacheKey, tag, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return tag;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tag with slug {Slug}", slug);
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetAllTagsAsync()
        {
            try
            {
                const string cacheKey = "all_tags";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Tag>? cachedTags))
                {
                    return cachedTags!;
                }

                var tags = await _context.Tags
                    .OrderByDescending(t => t.UsageCount)
                    .ThenBy(t => t.Name)
                    .ToListAsync();

                _cache.Set(cacheKey, tags, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return tags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all tags");
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetActiveTagsAsync()
        {
            try
            {
                const string cacheKey = "active_tags";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Tag>? cachedTags))
                {
                    return cachedTags!;
                }

                var tags = await _context.Tags
                    .Where(t => t.IsActive)
                    .OrderByDescending(t => t.UsageCount)
                    .ThenBy(t => t.Name)
                    .ToListAsync();

                _cache.Set(cacheKey, tags, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return tags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active tags");
                throw;
            }
        }

        public async Task<Tag> CreateTagAsync(Tag tag)
        {
            try
            {
                // Generate slug if not provided
                if (string.IsNullOrEmpty(tag.Slug))
                {
                    tag.Slug = await GenerateUniqueSlugAsync(tag.Name);
                }

                tag.CreatedDate = DateTime.UtcNow;

                _context.Tags.Add(tag);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created new tag with ID {TagId}", tag.Id);
                InvalidateTagCache();

                return tag;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tag");
                throw;
            }
        }

        public async Task<Tag> UpdateTagAsync(Tag tag)
        {
            try
            {
                var existingTag = await _context.Tags.FindAsync(tag.Id);
                if (existingTag == null)
                {
                    throw new ArgumentException("Tag not found", nameof(tag));
                }

                // Update properties
                existingTag.Name = tag.Name;
                existingTag.Slug = tag.Slug;
                existingTag.Description = tag.Description;
                existingTag.Color = tag.Color;
                existingTag.IsActive = tag.IsActive;
                existingTag.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated tag with ID {TagId}", tag.Id);
                InvalidateTagCache();

                return existingTag;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tag with ID {TagId}", tag.Id);
                throw;
            }
        }

        public async Task<bool> DeleteTagAsync(int id)
        {
            try
            {
                var tag = await _context.Tags.FindAsync(id);
                if (tag == null)
                {
                    return false;
                }

                // Check if tag is being used
                var isInUse = await _context.ArticleTags.AnyAsync(at => at.TagId == id);
                if (isInUse)
                {
                    throw new InvalidOperationException("Cannot delete tag that is currently being used by articles. Remove tag from articles first.");
                }

                _context.Tags.Remove(tag);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted tag with ID {TagId}", id);
                InvalidateTagCache();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting tag with ID {TagId}", id);
                throw;
            }
        }

        #endregion

        #region Tag with Statistics

        public async Task<TagWithStats?> GetTagWithStatsAsync(int id)
        {
            try
            {
                var cacheKey = $"tag_stats_{id}";
                if (_cache.TryGetValue(cacheKey, out TagWithStats? cached))
                {
                    return cached;
                }

                var tag = await GetByIdAsync(id);
                if (tag == null) return null;

                var stats = await CalculateTagStatsAsync(id);
                var result = new TagWithStats
                {
                    Tag = tag,
                    ArticleCount = stats.ArticleCount,
                    PublishedArticleCount = stats.PublishedArticleCount,
                    LastUsedDate = stats.LastUsedDate,
                    IsPopular = stats.ArticleCount >= 10 // Consider tags with 10+ articles as popular
                };

                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(10));
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tag with stats for ID {TagId}", id);
                throw;
            }
        }

        public async Task<TagWithStats?> GetTagWithStatsBySlugAsync(string slug)
        {
            try
            {
                var tag = await GetBySlugAsync(slug);
                if (tag == null) return null;

                return await GetTagWithStatsAsync(tag.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tag with stats for slug {Slug}", slug);
                throw;
            }
        }

        public async Task<IEnumerable<TagWithStats>> GetTagsWithStatsAsync()
        {
            try
            {
                const string cacheKey = "tags_with_stats";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<TagWithStats>? cached))
                {
                    return cached!;
                }

                var tags = await GetAllTagsAsync();
                var result = new List<TagWithStats>();

                foreach (var tag in tags)
                {
                    var stats = await CalculateTagStatsAsync(tag.Id);
                    result.Add(new TagWithStats
                    {
                        Tag = tag,
                        ArticleCount = stats.ArticleCount,
                        PublishedArticleCount = stats.PublishedArticleCount,
                        LastUsedDate = stats.LastUsedDate,
                        IsPopular = stats.ArticleCount >= 10
                    });
                }

                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(10));
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tags with stats");
                throw;
            }
        }

        public async Task<IEnumerable<TagWithStats>> GetActiveTagsWithStatsAsync()
        {
            try
            {
                const string cacheKey = "active_tags_with_stats";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<TagWithStats>? cached))
                {
                    return cached!;
                }

                var tags = await GetActiveTagsAsync();
                var result = new List<TagWithStats>();

                foreach (var tag in tags)
                {
                    var stats = await CalculateTagStatsAsync(tag.Id);
                    result.Add(new TagWithStats
                    {
                        Tag = tag,
                        ArticleCount = stats.ArticleCount,
                        PublishedArticleCount = stats.PublishedArticleCount,
                        LastUsedDate = stats.LastUsedDate,
                        IsPopular = stats.ArticleCount >= 10
                    });
                }

                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(10));
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active tags with stats");
                throw;
            }
        }

        #endregion

        #region Popular and Trending Tags

        public async Task<IEnumerable<Tag>> GetPopularTagsAsync(int count = 20)
        {
            try
            {
                var cacheKey = $"popular_tags_{count}";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Tag>? cached))
                {
                    return cached!;
                }

                var tags = await _context.Tags
                    .Where(t => t.IsActive && t.UsageCount > 0)
                    .OrderByDescending(t => t.UsageCount)
                    .Take(count)
                    .ToListAsync();

                _cache.Set(cacheKey, tags, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return tags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving popular tags");
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetTrendingTagsAsync(int days = 7, int count = 15)
        {
            try
            {
                var cacheKey = $"trending_tags_{days}_{count}";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Tag>? cached))
                {
                    return cached!;
                }

                var fromDate = DateTime.UtcNow.AddDays(-days);

                var trendingTagIds = await _context.ArticleTags
                    .Include(at => at.Article)
                    .Where(at => at.Article.PublishedDate >= fromDate && 
                                at.Article.Status == ArticleStatus.Published)
                    .GroupBy(at => at.TagId)
                    .Select(g => new { TagId = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(count)
                    .Select(x => x.TagId)
                    .ToListAsync();

                var tags = await _context.Tags
                    .Where(t => t.IsActive && trendingTagIds.Contains(t.Id))
                    .ToListAsync();

                // Maintain the order from the trending query
                var orderedTags = trendingTagIds
                    .Select(id => tags.FirstOrDefault(t => t.Id == id))
                    .Where(t => t != null)
                    .ToList();

                _cache.Set(cacheKey, orderedTags, TimeSpan.FromMinutes(10));
                return orderedTags!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving trending tags");
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetMostUsedTagsAsync(int count = 50)
        {
            try
            {
                return await _context.Tags
                    .Where(t => t.IsActive)
                    .OrderByDescending(t => t.UsageCount)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving most used tags");
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetRecentlyUsedTagsAsync(int days = 30, int count = 25)
        {
            try
            {
                var fromDate = DateTime.UtcNow.AddDays(-days);

                var recentTagIds = await _context.ArticleTags
                    .Where(at => at.CreatedDate >= fromDate)
                    .GroupBy(at => at.TagId)
                    .Select(g => new { TagId = g.Key, LastUsed = g.Max(at => at.CreatedDate) })
                    .OrderByDescending(x => x.LastUsed)
                    .Take(count)
                    .Select(x => x.TagId)
                    .ToListAsync();

                return await _context.Tags
                    .Where(t => t.IsActive && recentTagIds.Contains(t.Id))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving recently used tags");
                throw;
            }
        }

        #endregion

        #region Tag Usage Tracking

        public async Task<bool> IncrementTagUsageAsync(int tagId)
        {
            try
            {
                var tag = await _context.Tags.FindAsync(tagId);
                if (tag == null) return false;

                tag.UsageCount++;
                tag.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateTagCache();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing usage count for tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<bool> DecrementTagUsageAsync(int tagId)
        {
            try
            {
                var tag = await _context.Tags.FindAsync(tagId);
                if (tag == null) return false;

                tag.UsageCount = Math.Max(0, tag.UsageCount - 1);
                tag.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateTagCache();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error decrementing usage count for tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<bool> UpdateTagUsageCountAsync(int tagId, int newCount)
        {
            try
            {
                var tag = await _context.Tags.FindAsync(tagId);
                if (tag == null) return false;

                tag.UsageCount = Math.Max(0, newCount);
                tag.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateTagCache();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating usage count for tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<bool> RecalculateTagUsageCountAsync(int tagId)
        {
            try
            {
                var actualCount = await _context.ArticleTags.CountAsync(at => at.TagId == tagId);
                return await UpdateTagUsageCountAsync(tagId, actualCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recalculating usage count for tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<int> RecalculateAllTagUsageCountsAsync()
        {
            try
            {
                var tags = await _context.Tags.ToListAsync();
                var updated = 0;

                foreach (var tag in tags)
                {
                    var actualCount = await _context.ArticleTags.CountAsync(at => at.TagId == tag.Id);
                    if (tag.UsageCount != actualCount)
                    {
                        tag.UsageCount = actualCount;
                        tag.ModifiedDate = DateTime.UtcNow;
                        updated++;
                    }
                }

                await _context.SaveChangesAsync();
                InvalidateTagCache();

                _logger.LogInformation("Recalculated usage counts for {UpdatedCount} tags", updated);
                return updated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recalculating all tag usage counts");
                throw;
            }
        }

        #endregion

        #region Article-Tag Relationships

        public async Task<IEnumerable<Tag>> GetTagsForArticleAsync(int articleId)
        {
            try
            {
                return await _context.ArticleTags
                    .Include(at => at.Tag)
                    .Where(at => at.ArticleId == articleId)
                    .Select(at => at.Tag)
                    .OrderBy(t => t.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tags for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<IEnumerable<ArticleTag>> GetArticleTagsAsync(int articleId)
        {
            try
            {
                return await _context.ArticleTags
                    .Include(at => at.Tag)
                    .Where(at => at.ArticleId == articleId)
                    .OrderBy(at => at.Tag.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving article tags for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<bool> AddTagToArticleAsync(int articleId, int tagId, bool isSystemGenerated = false)
        {
            try
            {
                // Check if relationship already exists
                var existingRelation = await _context.ArticleTags
                    .FirstOrDefaultAsync(at => at.ArticleId == articleId && at.TagId == tagId);

                if (existingRelation != null)
                {
                    return false; // Already exists
                }

                var articleTag = new ArticleTag
                {
                    ArticleId = articleId,
                    TagId = tagId,
                    IsSystemGenerated = isSystemGenerated,
                    CreatedDate = DateTime.UtcNow
                };

                _context.ArticleTags.Add(articleTag);
                await IncrementTagUsageAsync(tagId);

                _logger.LogInformation("Added tag {TagId} to article {ArticleId}", tagId, articleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding tag {TagId} to article {ArticleId}", tagId, articleId);
                throw;
            }
        }

        public async Task<bool> RemoveTagFromArticleAsync(int articleId, int tagId)
        {
            try
            {
                var articleTag = await _context.ArticleTags
                    .FirstOrDefaultAsync(at => at.ArticleId == articleId && at.TagId == tagId);

                if (articleTag == null)
                {
                    return false;
                }

                _context.ArticleTags.Remove(articleTag);
                await DecrementTagUsageAsync(tagId);

                _logger.LogInformation("Removed tag {TagId} from article {ArticleId}", tagId, articleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing tag {TagId} from article {ArticleId}", tagId, articleId);
                throw;
            }
        }

        public async Task<bool> UpdateArticleTagsAsync(int articleId, IEnumerable<int> tagIds, bool isSystemGenerated = false)
        {
            try
            {
                // Get current tags
                var currentTags = await _context.ArticleTags
                    .Where(at => at.ArticleId == articleId)
                    .ToListAsync();

                var currentTagIds = currentTags.Select(at => at.TagId).ToHashSet();
                var newTagIds = tagIds.ToHashSet();

                // Remove tags that are no longer needed
                var tagsToRemove = currentTags.Where(at => !newTagIds.Contains(at.TagId)).ToList();
                foreach (var tagToRemove in tagsToRemove)
                {
                    _context.ArticleTags.Remove(tagToRemove);
                    await DecrementTagUsageAsync(tagToRemove.TagId);
                }

                // Add new tags
                var tagsToAdd = newTagIds.Where(tagId => !currentTagIds.Contains(tagId)).ToList();
                foreach (var tagId in tagsToAdd)
                {
                    var articleTag = new ArticleTag
                    {
                        ArticleId = articleId,
                        TagId = tagId,
                        IsSystemGenerated = isSystemGenerated,
                        CreatedDate = DateTime.UtcNow
                    };
                    _context.ArticleTags.Add(articleTag);
                    await IncrementTagUsageAsync(tagId);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated tags for article {ArticleId}: Removed {RemovedCount}, Added {AddedCount}", 
                                     articleId, tagsToRemove.Count, tagsToAdd.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tags for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<bool> ClearArticleTagsAsync(int articleId)
        {
            try
            {
                var articleTags = await _context.ArticleTags
                    .Where(at => at.ArticleId == articleId)
                    .ToListAsync();

                foreach (var articleTag in articleTags)
                {
                    _context.ArticleTags.Remove(articleTag);
                    await DecrementTagUsageAsync(articleTag.TagId);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Cleared {Count} tags from article {ArticleId}", articleTags.Count, articleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing tags for article {ArticleId}", articleId);
                throw;
            }
        }

        #endregion

        #region Tag-based Article Retrieval

        public async Task<PaginatedResult<Article>> GetArticlesWithTagAsync(int tagId, int page = 1, int pageSize = 20, ArticleStatus? status = null)
        {
            try
            {
                var query = _context.ArticleTags
                    .Include(at => at.Article)
                        .ThenInclude(a => a.Category)
                    .Include(at => at.Article)
                        .ThenInclude(a => a.Source)
                    .Where(at => at.TagId == tagId);

                if (status.HasValue)
                {
                    query = query.Where(at => at.Article.Status == status.Value);
                }

                var totalCount = await query.CountAsync();
                var skip = (page - 1) * pageSize;

                var articles = await query
                    .Select(at => at.Article)
                    .OrderByDescending(a => a.PublishedDate)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                return new PaginatedResult<Article>
                {
                    Items = articles,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<PaginatedResult<Article>> GetArticlesWithTagBySlugAsync(string slug, int page = 1, int pageSize = 20, ArticleStatus? status = null)
        {
            try
            {
                var tag = await GetBySlugAsync(slug);
                if (tag == null)
                {
                    return new PaginatedResult<Article>
                    {
                        Items = new List<Article>(),
                        TotalCount = 0,
                        Page = page,
                        PageSize = pageSize
                    };
                }

                return await GetArticlesWithTagAsync(tag.Id, page, pageSize, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for tag slug {Slug}", slug);
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetLatestArticlesWithTagAsync(int tagId, int count = 10)
        {
            try
            {
                return await _context.ArticleTags
                    .Include(at => at.Article)
                        .ThenInclude(a => a.Category)
                    .Include(at => at.Article)
                        .ThenInclude(a => a.Source)
                    .Where(at => at.TagId == tagId && at.Article.Status == ArticleStatus.Published)
                    .Select(at => at.Article)
                    .OrderByDescending(a => a.PublishedDate)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving latest articles for tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<PaginatedResult<Article>> GetArticlesWithAnyTagsAsync(IEnumerable<int> tagIds, int page = 1, int pageSize = 20, ArticleStatus? status = null)
        {
            try
            {
                var tagIdList = tagIds.ToList();
                var query = _context.ArticleTags
                    .Include(at => at.Article)
                        .ThenInclude(a => a.Category)
                    .Include(at => at.Article)
                        .ThenInclude(a => a.Source)
                    .Where(at => tagIdList.Contains(at.TagId));

                if (status.HasValue)
                {
                    query = query.Where(at => at.Article.Status == status.Value);
                }

                var totalCount = await query.Select(at => at.Article).Distinct().CountAsync();
                var skip = (page - 1) * pageSize;

                var articles = await query
                    .Select(at => at.Article)
                    .Distinct()
                    .OrderByDescending(a => a.PublishedDate)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                return new PaginatedResult<Article>
                {
                    Items = articles,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles with any of the specified tags");
                throw;
            }
        }

        public async Task<PaginatedResult<Article>> GetArticlesWithAllTagsAsync(IEnumerable<int> tagIds, int page = 1, int pageSize = 20, ArticleStatus? status = null)
        {
            try
            {
                var tagIdList = tagIds.ToList();
                var tagCount = tagIdList.Count;

                if (tagCount == 0)
                {
                    return new PaginatedResult<Article>
                    {
                        Items = new List<Article>(),
                        TotalCount = 0,
                        Page = page,
                        PageSize = pageSize
                    };
                }

                // Find articles that have ALL the specified tags
                var articleIdsWithAllTags = await _context.ArticleTags
                    .Where(at => tagIdList.Contains(at.TagId))
                    .GroupBy(at => at.ArticleId)
                    .Where(g => g.Count() == tagCount)
                    .Select(g => g.Key)
                    .ToListAsync();

                var query = _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => articleIdsWithAllTags.Contains(a.Id));

                if (status.HasValue)
                {
                    query = query.Where(a => a.Status == status.Value);
                }

                var totalCount = await query.CountAsync();
                var skip = (page - 1) * pageSize;

                var articles = await query
                    .OrderByDescending(a => a.PublishedDate)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                return new PaginatedResult<Article>
                {
                    Items = articles,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles with all specified tags");
                throw;
            }
        }

        #endregion

        #region Auto-tagging and Suggestions

        public async Task<IEnumerable<TagSuggestion>> SuggestTagsForContentAsync(string title, string content, int maxSuggestions = 10)
        {
            try
            {
                var suggestions = new List<TagSuggestion>();
                var combinedText = $"{title} {content}".ToLowerInvariant();

                // Get existing tags to check against
                var existingTags = await GetActiveTagsAsync();

                // Simple keyword matching (in a real implementation, you'd use NLP/ML)
                var keywords = ExtractKeywords(combinedText);

                foreach (var keyword in keywords.Take(maxSuggestions))
                {
                    var existingTag = existingTags.FirstOrDefault(t => 
                        t.Name.ToLowerInvariant().Contains(keyword) || 
                        keyword.Contains(t.Name.ToLowerInvariant()));

                    suggestions.Add(new TagSuggestion
                    {
                        TagName = keyword,
                        Slug = GenerateSlug(keyword),
                        Confidence = CalculateConfidence(keyword, combinedText),
                        AlreadyExists = existingTag != null,
                        ExistingTagId = existingTag?.Id
                    });
                }

                return suggestions.OrderByDescending(s => s.Confidence).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error suggesting tags for content");
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetRelatedTagsAsync(int tagId, int count = 10)
        {
            try
            {
                // Find tags that frequently appear together with the specified tag
                var relatedTagIds = await _context.ArticleTags
                    .Include(at => at.Article)
                    .Where(at => at.Article.ArticleTags.Any(at2 => at2.TagId == tagId) && at.TagId != tagId)
                    .GroupBy(at => at.TagId)
                    .Select(g => new { TagId = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(count)
                    .Select(x => x.TagId)
                    .ToListAsync();

                return await _context.Tags
                    .Where(t => t.IsActive && relatedTagIds.Contains(t.Id))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving related tags for tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetSimilarTagsAsync(string tagName, int count = 5)
        {
            try
            {
                var normalizedName = tagName.ToLowerInvariant();

                return await _context.Tags
                    .Where(t => t.IsActive && 
                               (t.Name.ToLower().Contains(normalizedName) || 
                                normalizedName.Contains(t.Name.ToLower())))
                    .OrderByDescending(t => t.UsageCount)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving similar tags for name {TagName}", tagName);
                throw;
            }
        }

        public async Task<bool> AutoTagArticleAsync(int articleId, string title, string content)
        {
            try
            {
                var suggestions = await SuggestTagsForContentAsync(title, content, 5);
                var tagIds = new List<int>();

                foreach (var suggestion in suggestions.Where(s => s.Confidence > 0.5m))
                {
                    if (suggestion.AlreadyExists && suggestion.ExistingTagId.HasValue)
                    {
                        tagIds.Add(suggestion.ExistingTagId.Value);
                    }
                    else
                    {
                        // Create new tag
                        var newTag = new Tag
                        {
                            Name = suggestion.TagName,
                            Slug = suggestion.Slug,
                            IsActive = true,
                            IsSystemGenerated = true
                        };

                        var createdTag = await CreateTagAsync(newTag);
                        tagIds.Add(createdTag.Id);
                    }
                }

                if (tagIds.Any())
                {
                    await UpdateArticleTagsAsync(articleId, tagIds, true);
                    _logger.LogInformation("Auto-tagged article {ArticleId} with {TagCount} tags", articleId, tagIds.Count);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error auto-tagging article {ArticleId}", articleId);
                throw;
            }
        }

        #endregion

        #region Tag Management

        public async Task<bool> SetTagActiveStatusAsync(int tagId, bool isActive)
        {
            try
            {
                var tag = await _context.Tags.FindAsync(tagId);
                if (tag == null) return false;

                tag.IsActive = isActive;
                tag.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateTagCache();

                _logger.LogInformation("Updated active status for tag {TagId} to {IsActive}", tagId, isActive);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating active status for tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<bool> MergeTagsAsync(int sourceTagId, int targetTagId)
        {
            try
            {
                if (sourceTagId == targetTagId)
                {
                    return false;
                }

                // Move all article relationships from source to target tag
                var sourceArticleTags = await _context.ArticleTags
                    .Where(at => at.TagId == sourceTagId)
                    .ToListAsync();

                foreach (var articleTag in sourceArticleTags)
                {
                    // Check if target tag already exists for this article
                    var existingRelation = await _context.ArticleTags
                        .FirstOrDefaultAsync(at => at.ArticleId == articleTag.ArticleId && at.TagId == targetTagId);

                    if (existingRelation == null)
                    {
                        articleTag.TagId = targetTagId;
                    }
                    else
                    {
                        _context.ArticleTags.Remove(articleTag);
                    }
                }

                // Update usage counts
                await RecalculateTagUsageCountAsync(targetTagId);

                // Delete the source tag
                var sourceTag = await _context.Tags.FindAsync(sourceTagId);
                if (sourceTag != null)
                {
                    _context.Tags.Remove(sourceTag);
                }

                await _context.SaveChangesAsync();
                InvalidateTagCache();

                _logger.LogInformation("Merged tag {SourceTagId} into tag {TargetTagId}", sourceTagId, targetTagId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error merging tag {SourceTagId} into tag {TargetTagId}", sourceTagId, targetTagId);
                throw;
            }
        }

        public async Task<bool> SplitTagAsync(int tagId, IEnumerable<string> newTagNames)
        {
            try
            {
                var newTagList = newTagNames.ToList();
                if (!newTagList.Any())
                {
                    return false;
                }

                // Create new tags
                var createdTags = new List<Tag>();
                foreach (var tagName in newTagList)
                {
                    var newTag = new Tag
                    {
                        Name = tagName,
                        Slug = await GenerateUniqueSlugAsync(tagName),
                        IsActive = true,
                        IsSystemGenerated = false
                    };

                    createdTags.Add(await CreateTagAsync(newTag));
                }

                // For simplicity, we'll just create the new tags and leave the original tag intact
                // In a more sophisticated implementation, you might want to redistribute the articles

                _logger.LogInformation("Split tag {TagId} into {NewTagCount} new tags", tagId, createdTags.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error splitting tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<int> CleanupUnusedTagsAsync()
        {
            try
            {
                var unusedTags = await _context.Tags
                    .Where(t => t.UsageCount == 0)
                    .ToListAsync();

                _context.Tags.RemoveRange(unusedTags);
                await _context.SaveChangesAsync();
                InvalidateTagCache();

                _logger.LogInformation("Cleaned up {Count} unused tags", unusedTags.Count);
                return unusedTags.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up unused tags");
                throw;
            }
        }

        public async Task<int> CleanupDuplicateTagsAsync()
        {
            try
            {
                var duplicateGroups = await _context.Tags
                    .GroupBy(t => t.Name.ToLower())
                    .Where(g => g.Count() > 1)
                    .ToListAsync();

                var mergedCount = 0;

                foreach (var group in duplicateGroups)
                {
                    var tags = group.OrderByDescending(t => t.UsageCount).ToList();
                    var primaryTag = tags.First();

                    for (int i = 1; i < tags.Count; i++)
                    {
                        await MergeTagsAsync(tags[i].Id, primaryTag.Id);
                        mergedCount++;
                    }
                }

                _logger.LogInformation("Cleaned up {Count} duplicate tags", mergedCount);
                return mergedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up duplicate tags");
                throw;
            }
        }

        #endregion

        #region Search and Filtering

        public async Task<IEnumerable<Tag>> SearchTagsAsync(string query)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(query))
                {
                    return await GetAllTagsAsync();
                }

                return await _context.Tags
                    .Where(t => t.Name.Contains(query) || 
                               (t.Description != null && t.Description.Contains(query)))
                    .OrderByDescending(t => t.UsageCount)
                    .ThenBy(t => t.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching tags with query: {Query}", query);
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetTagsByColorAsync(string color)
        {
            try
            {
                return await _context.Tags
                    .Where(t => t.Color == color)
                    .OrderByDescending(t => t.UsageCount)
                    .ThenBy(t => t.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tags by color {Color}", color);
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetSystemGeneratedTagsAsync()
        {
            try
            {
                return await _context.Tags
                    .Where(t => t.IsSystemGenerated)
                    .OrderByDescending(t => t.UsageCount)
                    .ThenBy(t => t.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving system-generated tags");
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetManuallyCreatedTagsAsync()
        {
            try
            {
                return await _context.Tags
                    .Where(t => !t.IsSystemGenerated)
                    .OrderByDescending(t => t.UsageCount)
                    .ThenBy(t => t.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving manually created tags");
                throw;
            }
        }

        public async Task<IEnumerable<int>> GetTagIdsByNamesAsync(IEnumerable<string> tagNames)
        {
            try
            {
                if (!tagNames?.Any() == true)
                    return new List<int>();

                var normalizedNames = tagNames.Select(name => name.ToLowerInvariant().Trim()).ToList();

                return await _context.Tags
                    .Where(t => normalizedNames.Contains(t.Name.ToLower()) && t.IsActive)
                    .Select(t => t.Id)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tag IDs by names");
                throw;
            }
        }

        #endregion

        #region Tag Analytics

        public async Task<Dictionary<int, int>> GetTagUsageCountsAsync(IEnumerable<int> tagIds)
        {
            try
            {
                return await _context.Tags
                    .Where(t => tagIds.Contains(t.Id))
                    .ToDictionaryAsync(t => t.Id, t => t.UsageCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tag usage counts");
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetTagUsageByDateRangeAsync(int tagId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var usage = await _context.ArticleTags
                    .Include(at => at.Article)
                    .Where(at => at.TagId == tagId && 
                                at.Article.PublishedDate >= fromDate && 
                                at.Article.PublishedDate <= toDate)
                    .GroupBy(at => at.Article.PublishedDate.Date)
                    .Select(g => new { Date = g.Key, Count = g.Count() })
                    .ToListAsync();

                return usage.ToDictionary(x => x.Date.ToString("yyyy-MM-dd"), x => x.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tag usage by date range for tag {TagId}", tagId);
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetTagsUsedInCategoryAsync(int categoryId, int count = 20)
        {
            try
            {
                var tagIds = await _context.ArticleTags
                    .Include(at => at.Article)
                    .Where(at => at.Article.CategoryId == categoryId)
                    .GroupBy(at => at.TagId)
                    .Select(g => new { TagId = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(count)
                    .Select(x => x.TagId)
                    .ToListAsync();

                return await _context.Tags
                    .Where(t => tagIds.Contains(t.Id))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tags used in category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<IEnumerable<Tag>> GetTagsUsedBySourceAsync(int sourceId, int count = 20)
        {
            try
            {
                var tagIds = await _context.ArticleTags
                    .Include(at => at.Article)
                    .Where(at => at.Article.SourceId == sourceId)
                    .GroupBy(at => at.TagId)
                    .Select(g => new { TagId = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(count)
                    .Select(x => x.TagId)
                    .ToListAsync();

                return await _context.Tags
                    .Where(t => tagIds.Contains(t.Id))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving tags used by source {SourceId}", sourceId);
                throw;
            }
        }

        #endregion

        #region Validation

        public async Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null)
        {
            try
            {
                var query = _context.Tags.Where(t => t.Slug == slug);

                if (excludeId.HasValue)
                {
                    query = query.Where(t => t.Id != excludeId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking slug uniqueness for {Slug}", slug);
                throw;
            }
        }

        public async Task<bool> ValidateTagAsync(Tag tag)
        {
            try
            {
                // Check required fields
                if (string.IsNullOrWhiteSpace(tag.Name) || string.IsNullOrWhiteSpace(tag.Slug))
                {
                    return false;
                }

                // Check slug uniqueness
                if (!await IsSlugUniqueAsync(tag.Slug, tag.Id))
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating tag");
                throw;
            }
        }

        public async Task<bool> CanDeleteTagAsync(int tagId)
        {
            try
            {
                var isInUse = await _context.ArticleTags.AnyAsync(at => at.TagId == tagId);
                return !isInUse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if tag {TagId} can be deleted", tagId);
                throw;
            }
        }

        #endregion

        #region Bulk Operations

        public async Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> tagIds, bool isActive)
        {
            try
            {
                var tags = await _context.Tags
                    .Where(t => tagIds.Contains(t.Id))
                    .ToListAsync();

                foreach (var tag in tags)
                {
                    tag.IsActive = isActive;
                    tag.ModifiedDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                InvalidateTagCache();

                _logger.LogInformation("Bulk updated active status to {IsActive} for {Count} tags", isActive, tags.Count);
                return tags.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating tag active status");
                throw;
            }
        }

        public async Task<int> BulkDeleteUnusedTagsAsync(int minUsageCount = 0)
        {
            try
            {
                var unusedTags = await _context.Tags
                    .Where(t => t.UsageCount <= minUsageCount)
                    .ToListAsync();

                _context.Tags.RemoveRange(unusedTags);
                await _context.SaveChangesAsync();
                InvalidateTagCache();

                _logger.LogInformation("Bulk deleted {Count} unused tags", unusedTags.Count);
                return unusedTags.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk deleting unused tags");
                throw;
            }
        }

        public async Task<int> BulkUpdateTagColorsAsync(Dictionary<int, string> tagColorMap)
        {
            try
            {
                var tagIds = tagColorMap.Keys.ToList();
                var tags = await _context.Tags
                    .Where(t => tagIds.Contains(t.Id))
                    .ToListAsync();

                foreach (var tag in tags)
                {
                    if (tagColorMap.TryGetValue(tag.Id, out var color))
                    {
                        tag.Color = color;
                        tag.ModifiedDate = DateTime.UtcNow;
                    }
                }

                await _context.SaveChangesAsync();
                InvalidateTagCache();

                _logger.LogInformation("Bulk updated colors for {Count} tags", tags.Count);
                return tags.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating tag colors");
                throw;
            }
        }

        public async Task<bool> BulkAddTagsToArticleAsync(int articleId, IEnumerable<string> tagNames, bool isSystemGenerated = false)
        {
            try
            {
                var tagIds = new List<int>();

                foreach (var tagName in tagNames)
                {
                    var existingTag = await _context.Tags.FirstOrDefaultAsync(t => t.Name == tagName);
                    
                    if (existingTag != null)
                    {
                        tagIds.Add(existingTag.Id);
                    }
                    else
                    {
                        var newTag = new Tag
                        {
                            Name = tagName,
                            Slug = await GenerateUniqueSlugAsync(tagName),
                            IsActive = true,
                            IsSystemGenerated = isSystemGenerated
                        };

                        var createdTag = await CreateTagAsync(newTag);
                        tagIds.Add(createdTag.Id);
                    }
                }

                // Get existing tags for the article
                var existingTagIds = await _context.ArticleTags
                    .Where(at => at.ArticleId == articleId)
                    .Select(at => at.TagId)
                    .ToListAsync();

                // Combine with new tags
                var allTagIds = existingTagIds.Union(tagIds).ToList();
                
                return await UpdateArticleTagsAsync(articleId, allTagIds, isSystemGenerated);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk adding tags to article {ArticleId}", articleId);
                throw;
            }
        }

        #endregion

        #region Import/Export

        public async Task<IEnumerable<Tag>> ImportTagsAsync(IEnumerable<string> tagNames, bool isSystemGenerated = false)
        {
            try
            {
                var createdTags = new List<Tag>();

                foreach (var tagName in tagNames)
                {
                    var existingTag = await _context.Tags.FirstOrDefaultAsync(t => t.Name == tagName);
                    
                    if (existingTag == null)
                    {
                        var newTag = new Tag
                        {
                            Name = tagName,
                            Slug = await GenerateUniqueSlugAsync(tagName),
                            IsActive = true,
                            IsSystemGenerated = isSystemGenerated
                        };

                        createdTags.Add(await CreateTagAsync(newTag));
                    }
                }

                _logger.LogInformation("Imported {Count} new tags", createdTags.Count);
                return createdTags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing tags");
                throw;
            }
        }

        public async Task<Dictionary<string, object>> ExportTagDataAsync()
        {
            try
            {
                var tags = await GetAllTagsAsync();
                var tagStats = await GetTagsWithStatsAsync();

                return new Dictionary<string, object>
                {
                    {"tags", tags},
                    {"tagStats", tagStats},
                    {"exportDate", DateTime.UtcNow},
                    {"totalTags", tags.Count()}
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting tag data");
                throw;
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task<(int ArticleCount, int PublishedArticleCount, DateTime? LastUsedDate)> CalculateTagStatsAsync(int tagId)
        {
            var stats = await _context.ArticleTags
                .Include(at => at.Article)
                .Where(at => at.TagId == tagId)
                .GroupBy(at => at.TagId)
                .Select(g => new
                {
                    ArticleCount = g.Count(),
                    PublishedArticleCount = g.Count(at => at.Article.Status == ArticleStatus.Published),
                    LastUsedDate = g.Max(at => (DateTime?)at.CreatedDate)
                })
                .FirstOrDefaultAsync();

            return (
                stats?.ArticleCount ?? 0,
                stats?.PublishedArticleCount ?? 0,
                stats?.LastUsedDate
            );
        }

        private async Task<string> GenerateUniqueSlugAsync(string name)
        {
            var baseSlug = GenerateSlug(name);
            var slug = baseSlug;
            var counter = 1;

            while (!await IsSlugUniqueAsync(slug))
            {
                slug = $"{baseSlug}-{counter}";
                counter++;
            }

            return slug;
        }

        private static string GenerateSlug(string name)
        {
            if (string.IsNullOrEmpty(name))
                return string.Empty;

            return name
                .ToLowerInvariant()
                .Replace(" ", "-")
                .Replace("&", "and")
                .RegexReplace(@"[^a-z0-9\-]", "")
                .RegexReplace(@"-+", "-")
                .Trim('-');
        }

        private static IEnumerable<string> ExtractKeywords(string text)
        {
            // Simple keyword extraction (in production, use proper NLP)
            var commonWords = new HashSet<string> { "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "a", "an", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "can", "this", "that", "these", "those", "i", "you", "he", "she", "it", "we", "they", "me", "him", "her", "us", "them" };

            return text
                .Split(new[] { ' ', '\t', '\n', '\r', '.', ',', ';', ':', '!', '?' }, StringSplitOptions.RemoveEmptyEntries)
                .Where(word => word.Length > 3 && !commonWords.Contains(word.ToLowerInvariant()))
                .GroupBy(word => word.ToLowerInvariant())
                .OrderByDescending(g => g.Count())
                .Select(g => g.Key)
                .Take(20);
        }

        private static decimal CalculateConfidence(string keyword, string text)
        {
            var occurrences = text.Split(' ').Count(word => word.ToLowerInvariant().Contains(keyword));
            var totalWords = text.Split(' ').Length;
            
            return Math.Min(1.0m, (decimal)occurrences / totalWords * 100);
        }

        private void InvalidateTagCache()
        {
            var cacheKeys = new[]
            {
                "all_tags",
                "active_tags",
                "tags_with_stats",
                "active_tags_with_stats"
            };

            foreach (var key in cacheKeys)
            {
                _cache.Remove(key);
            }
        }

        #endregion
    }
}