<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-NewsSite-2fab4969-655f-47ca-bd8e-9cdce7d8ac54</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.20" />
    <PackageReference Include="Hangfire.SqlServer" Version="1.8.20" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.2" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.0.18" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.18" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.18" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.8" />
    <PackageReference Include="System.ServiceModel.Syndication" Version="9.0.8" />
    
    <!-- Azure Dependencies -->
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="8.0.0" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
    <PackageReference Include="Azure.Identity" Version="1.12.1" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.AzureStorage" Version="8.0.18" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.AzureKeyVault" Version="8.0.18" />
    
    <!-- Performance and Security -->
    <PackageReference Include="Microsoft.AspNetCore.ResponseCompression" Version="8.0.18" />
    <PackageReference Include="Microsoft.AspNetCore.ResponseCaching" Version="8.0.18" />
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    
    <!-- Health Checks -->
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.18" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.18" />
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="8.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.Hangfire" Version="8.0.1" />
    
    <!-- Email Services -->
    <PackageReference Include="MailKit" Version="4.8.0" />
    <PackageReference Include="MimeKit" Version="4.8.0" />
  </ItemGroup>

</Project>
