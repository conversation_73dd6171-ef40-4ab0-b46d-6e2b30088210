using System.ComponentModel.DataAnnotations;

namespace NewsSite.Areas.Admin.Models
{
    public class SystemSettingsViewModel
    {
        [Display(Name = "Global Settings")]
        public GlobalSettings Global { get; set; } = new();

        [Display(Name = "Content Aggregation Settings")]
        public ContentAggregationSettings ContentAggregation { get; set; } = new();

        [Display(Name = "Quality Control Settings")]
        public QualityControlSettings QualityControl { get; set; } = new();

        [Display(Name = "Notification Settings")]
        public NotificationSettings Notifications { get; set; } = new();

        [Display(Name = "Performance Settings")]
        public PerformanceSettings Performance { get; set; } = new();

        [Display(Name = "Security Settings")]
        public SecuritySettings Security { get; set; } = new();
    }

    public class GlobalSettings
    {
        [Display(Name = "Site Name")]
        [MaxLength(100)]
        public string SiteName { get; set; } = "AI Frontiers";

        [Display(Name = "Site Description")]
        [MaxLength(500)]
        public string SiteDescription { get; set; } = string.Empty;

        [Display(Name = "Default Time Zone")]
        public string DefaultTimeZone { get; set; } = "UTC";

        [Display(Name = "Enable Maintenance Mode")]
        public bool MaintenanceMode { get; set; } = false;

        [Display(Name = "Maintenance Message")]
        [MaxLength(1000)]
        public string MaintenanceMessage { get; set; } = "Site is under maintenance. Please check back later.";

        [Display(Name = "Enable Analytics")]
        public bool EnableAnalytics { get; set; } = true;

        [Display(Name = "Google Analytics ID")]
        [MaxLength(50)]
        public string? GoogleAnalyticsId { get; set; }

        [Display(Name = "Contact Email")]
        [EmailAddress]
        [MaxLength(200)]
        public string? ContactEmail { get; set; }
    }

    public class ContentAggregationSettings
    {
        [Display(Name = "Enable Automatic Content Sync")]
        public bool EnableAutoSync { get; set; } = true;

        [Range(5, 1440)]
        [Display(Name = "Default Sync Interval (Minutes)")]
        public int DefaultSyncIntervalMinutes { get; set; } = 60;

        [Range(1, 1000)]
        [Display(Name = "Max Articles Per Source Per Sync")]
        public int MaxArticlesPerSourcePerSync { get; set; } = 50;

        [Range(1, 365)]
        [Display(Name = "Default Article Retention (Days)")]
        public int DefaultArticleRetentionDays { get; set; } = 30;

        [Display(Name = "Auto-Categorize Articles")]
        public bool AutoCategorizeArticles { get; set; } = true;

        [Display(Name = "Auto-Generate Tags")]
        public bool AutoGenerateTags { get; set; } = true;

        [Display(Name = "Enable Duplicate Detection")]
        public bool EnableDuplicateDetection { get; set; } = true;

        [Range(0.5, 1.0)]
        [Display(Name = "Duplicate Detection Threshold")]
        public double DuplicateDetectionThreshold { get; set; } = 0.85;

        [Range(1, 100)]
        [Display(Name = "Max Concurrent Syncs")]
        public int MaxConcurrentSyncs { get; set; } = 5;
    }

    public class QualityControlSettings
    {
        [Range(10, 10000)]
        [Display(Name = "Minimum Content Length")]
        public int MinContentLength { get; set; } = 100;

        [Range(10, 2000)]
        [Display(Name = "Maximum Summary Length")]
        public int MaxSummaryLength { get; set; } = 500;

        [Display(Name = "Require Images")]
        public bool RequireImages { get; set; } = false;

        [Display(Name = "Enable Content Filtering")]
        public bool EnableContentFiltering { get; set; } = true;

        [Display(Name = "Blocked Keywords (comma-separated)")]
        [MaxLength(2000)]
        public string? BlockedKeywords { get; set; }

        [Display(Name = "Required Keywords (comma-separated)")]
        [MaxLength(2000)]
        public string? RequiredKeywords { get; set; }

        [Range(0, 10)]
        [Display(Name = "Minimum Quality Score")]
        public double MinQualityScore { get; set; } = 3.0;

        [Display(Name = "Enable AI Content Scoring")]
        public bool EnableAiContentScoring { get; set; } = false;

        [Display(Name = "Auto-Moderate Content")]
        public bool AutoModerateContent { get; set; } = true;
    }

    public class NotificationSettings
    {
        [Display(Name = "Enable Email Notifications")]
        public bool EnableEmailNotifications { get; set; } = true;

        [Display(Name = "Admin Email")]
        [EmailAddress]
        [MaxLength(200)]
        public string? AdminEmail { get; set; }

        [Display(Name = "SMTP Server")]
        [MaxLength(200)]
        public string? SmtpServer { get; set; }

        [Range(1, 65535)]
        [Display(Name = "SMTP Port")]
        public int SmtpPort { get; set; } = 587;

        [Display(Name = "SMTP Username")]
        [MaxLength(200)]
        public string? SmtpUsername { get; set; }

        [Display(Name = "SMTP Password")]
        [MaxLength(200)]
        [DataType(DataType.Password)]
        public string? SmtpPassword { get; set; }

        [Display(Name = "Use SSL")]
        public bool SmtpUseSsl { get; set; } = true;

        [Display(Name = "From Email")]
        [EmailAddress]
        [MaxLength(200)]
        public string? FromEmail { get; set; }

        [Display(Name = "From Name")]
        [MaxLength(100)]
        public string? FromName { get; set; }

        [Display(Name = "Notify on Sync Errors")]
        public bool NotifyOnSyncErrors { get; set; } = true;

        [Display(Name = "Notify on System Errors")]
        public bool NotifyOnSystemErrors { get; set; } = true;

        [Display(Name = "Daily Summary Email")]
        public bool SendDailySummary { get; set; } = false;
    }

    public class PerformanceSettings
    {
        [Range(1, 3600)]
        [Display(Name = "Cache Duration (Seconds)")]
        public int CacheDurationSeconds { get; set; } = 300;

        [Range(1, 100)]
        [Display(Name = "Max Concurrent HTTP Requests")]
        public int MaxConcurrentHttpRequests { get; set; } = 10;

        [Range(1, 300)]
        [Display(Name = "HTTP Request Timeout (Seconds)")]
        public int HttpRequestTimeoutSeconds { get; set; } = 30;

        [Display(Name = "Enable Response Compression")]
        public bool EnableResponseCompression { get; set; } = true;

        [Display(Name = "Enable Output Caching")]
        public bool EnableOutputCaching { get; set; } = true;

        [Range(1, 1000)]
        [Display(Name = "Max Database Connections")]
        public int MaxDatabaseConnections { get; set; } = 100;

        [Range(1, 3600)]
        [Display(Name = "Database Command Timeout (Seconds)")]
        public int DatabaseCommandTimeoutSeconds { get; set; } = 30;

        [Display(Name = "Enable Background Processing")]
        public bool EnableBackgroundProcessing { get; set; } = true;
    }

    public class SecuritySettings
    {
        [Display(Name = "Require HTTPS")]
        public bool RequireHttps { get; set; } = true;

        [Display(Name = "Enable CORS")]
        public bool EnableCors { get; set; } = false;

        [Display(Name = "Allowed Origins (comma-separated)")]
        [MaxLength(1000)]
        public string? AllowedOrigins { get; set; }

        [Range(1, 100)]
        [Display(Name = "Rate Limit (Requests per minute)")]
        public int RateLimitRequestsPerMinute { get; set; } = 60;

        [Display(Name = "Enable IP Blocking")]
        public bool EnableIpBlocking { get; set; } = true;

        [Display(Name = "Blocked IP Addresses (comma-separated)")]
        [MaxLength(2000)]
        public string? BlockedIpAddresses { get; set; }

        [Display(Name = "Enable Audit Logging")]
        public bool EnableAuditLogging { get; set; } = true;

        [Range(1, 365)]
        [Display(Name = "Audit Log Retention (Days)")]
        public int AuditLogRetentionDays { get; set; } = 90;

        [Display(Name = "Enable Failed Login Tracking")]
        public bool EnableFailedLoginTracking { get; set; } = true;

        [Range(3, 100)]
        [Display(Name = "Max Failed Login Attempts")]
        public int MaxFailedLoginAttempts { get; set; } = 5;

        [Range(1, 1440)]
        [Display(Name = "Login Lockout Duration (Minutes)")]
        public int LoginLockoutDurationMinutes { get; set; } = 30;
    }

    public class SettingsUpdateResult
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> UpdatedSettings { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
    }
}