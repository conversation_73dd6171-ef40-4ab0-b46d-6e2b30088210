@model NewsSite.Areas.Admin.Models.SourceManagementViewModel
@{
    ViewData["Title"] = "Content Sources Management";
    ViewData["Subtitle"] = "Manage all content sources and their configurations";
    Layout = "_AdminLayout";
}

<div class="row g-4 mb-4">
    <!-- Summary Cards -->
    <div class="col-xl-3 col-md-6">
        <div class="admin-stat-card">
            <div class="d-flex align-items-center">
                <div class="admin-stat-icon bg-primary bg-opacity-10 text-primary me-3">
                    <i class="fas fa-rss"></i>
                </div>
                <div>
                    <h3 class="mb-0">@Model.Sources.Count</h3>
                    <p class="text-muted mb-0">Total Sources</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="admin-stat-card">
            <div class="d-flex align-items-center">
                <div class="admin-stat-icon bg-success bg-opacity-10 text-success me-3">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div>
                    <h3 class="mb-0">@Model.Sources.Count(s => s.IsActive)</h3>
                    <p class="text-muted mb-0">Active Sources</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="admin-stat-card">
            <div class="d-flex align-items-center">
                <div class="admin-stat-icon bg-warning bg-opacity-10 text-warning me-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div>
                    <h3 class="mb-0">@Model.Sources.Count(s => s.Status == "Error" || s.Status == "Warning")</h3>
                    <p class="text-muted mb-0">Issues Detected</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="admin-stat-card">
            <div class="d-flex align-items-center">
                <div class="admin-stat-icon bg-info bg-opacity-10 text-info me-3">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div>
                    <h3 class="mb-0">@Model.Sources.Sum(s => s.RecentArticleCount)</h3>
                    <p class="text-muted mb-0">Articles (7 days)</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="admin-card">
    <div class="card-body">
        <!-- Header with Actions -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h5 class="card-title mb-1">
                    <i class="fas fa-rss me-2"></i>
                    Content Sources
                </h5>
                <p class="text-muted mb-0">Manage your content sources and synchronization settings</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-info btn-sm" onclick="syncAllSources()" id="syncAllBtn">
                    <i class="fas fa-sync me-1"></i>
                    Sync All
                </button>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-tasks me-1"></i>
                        Bulk Actions
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="bulkAction('activate')">
                            <i class="fas fa-toggle-on me-2"></i>Activate Selected
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="bulkAction('deactivate')">
                            <i class="fas fa-toggle-off me-2"></i>Deactivate Selected
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="bulkAction('sync')">
                            <i class="fas fa-sync me-2"></i>Sync Selected
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                            <i class="fas fa-trash me-2"></i>Delete Selected
                        </a></li>
                    </ul>
                </div>
                <a href="/admin/sources/create" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    Add Source
                </a>
            </div>
        </div>

        <!-- Filters -->
        <form method="get" class="mb-4">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Search</label>
                    <input type="text" class="form-control" name="SearchTerm" value="@Model.Filters.SearchTerm" 
                           placeholder="Search sources...">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Type</label>
                    <select class="form-select" name="Type">
                        <option value="">All Types</option>
                        @foreach (var type in Enum.GetValues<NewsSite.Models.SourceType>())
                        {
                            <option value="@type" selected="@(Model.Filters.Type == type)">@type</option>
                        }
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Status</label>
                    <select class="form-select" name="Status">
                        <option value="">All Status</option>
                        <option value="healthy" selected="@(Model.Filters.Status == "healthy")">Healthy</option>
                        <option value="warning" selected="@(Model.Filters.Status == "warning")">Warning</option>
                        <option value="error" selected="@(Model.Filters.Status == "error")">Error</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Active</label>
                    <select class="form-select" name="IsActive">
                        <option value="">All</option>
                        <option value="true" selected="@(Model.Filters.IsActive == true)">Active</option>
                        <option value="false" selected="@(Model.Filters.IsActive == false)">Inactive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="/admin/sources" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </div>
        </form>

        <!-- Sources Table -->
        <div class="table-responsive">
            <table class="table table-dark table-hover">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>
                            <a href="?sortBy=name&sortDirection=@(Model.Filters.SortBy == "name" && Model.Filters.SortDirection == "asc" ? "desc" : "asc")" 
                               class="text-decoration-none text-light">
                                Source Name
                                @if (Model.Filters.SortBy == "name")
                                {
                                    <i class="fas fa-sort-@(Model.Filters.SortDirection == "asc" ? "up" : "down") ms-1"></i>
                                }
                            </a>
                        </th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>
                            <a href="?sortBy=lastsync&sortDirection=@(Model.Filters.SortBy == "lastsync" && Model.Filters.SortDirection == "asc" ? "desc" : "asc")" 
                               class="text-decoration-none text-light">
                                Last Sync
                                @if (Model.Filters.SortBy == "lastsync")
                                {
                                    <i class="fas fa-sort-@(Model.Filters.SortDirection == "asc" ? "up" : "down") ms-1"></i>
                                }
                            </a>
                        </th>
                        <th>Articles</th>
                        <th>Update Interval</th>
                        <th width="120">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var source in Model.Sources)
                    {
                        <tr class="@(!source.IsActive ? "opacity-75" : "")" data-source-id="@source.Id">
                            <td>
                                <input type="checkbox" class="form-check-input source-checkbox" value="@source.Id">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="status-indicator <EMAIL>() me-2"></div>
                                    <div>
                                        <strong class="d-block">@source.Name</strong>
                                        <small class="text-muted">@source.Url</small>
                                        @if (!source.IsActive)
                                        {
                                            <span class="badge bg-secondary ms-2">Inactive</span>
                                        }
                                        @if (source.HasApiKey)
                                        {
                                            <span class="badge bg-info ms-1">API</span>
                                        }
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-@(source.Type switch {
                                    NewsSite.Models.SourceType.RssFeed => "primary",
                                    NewsSite.Models.SourceType.YouTubeChannel => "danger",
                                    NewsSite.Models.SourceType.GitHubRepository => "dark",
                                    NewsSite.Models.SourceType.ArXivCategory => "success",
                                    _ => "secondary"
                                })">
                                    @source.Type
                                </span>
                            </td>
                            <td>
                                <span class="<EMAIL>()">
                                    <i class="fas fa-@(source.Status == "Healthy" ? "check-circle" : 
                                                       source.Status == "Warning" ? "exclamation-triangle" : 
                                                       "times-circle") me-1"></i>
                                    @source.Status
                                </span>
                                @if (source.ConsecutiveFailures > 0)
                                {
                                    <small class="d-block text-warning">
                                        @source.ConsecutiveFailures consecutive failures
                                    </small>
                                }
                                @if (!string.IsNullOrEmpty(source.LastError))
                                {
                                    <small class="d-block text-danger" title="@source.LastError">
                                        @(source.LastError.Length > 30 ? source.LastError[..30] + "..." : source.LastError)
                                    </small>
                                }
                            </td>
                            <td>
                                @if (source.LastSync.HasValue)
                                {
                                    <span title="@source.LastSync.Value.ToString("yyyy-MM-dd HH:mm:ss UTC")">
                                        @source.LastSync.Value.ToString("MMM dd, HH:mm")
                                    </span>
                                    @if (source.LastSuccessfulSync.HasValue && source.LastSuccessfulSync != source.LastSync)
                                    {
                                        <small class="d-block text-muted">
                                            Last success: @source.LastSuccessfulSync.Value.ToString("MMM dd, HH:mm")
                                        </small>
                                    }
                                }
                                else
                                {
                                    <span class="text-muted">Never</span>
                                }
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-@(source.ArticleCount > 0 ? "success" : "secondary") me-2">
                                        @source.ArticleCount.ToString("N0")
                                    </span>
                                    @if (source.RecentArticleCount > 0)
                                    {
                                        <small class="text-success">
                                            +@source.RecentArticleCount this week
                                        </small>
                                    }
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    @(source.UpdateIntervalMinutes < 60 ? 
                                      $"{source.UpdateIntervalMinutes}m" : 
                                      source.UpdateIntervalMinutes < 1440 ? 
                                      $"{source.UpdateIntervalMinutes / 60}h" : 
                                      $"{source.UpdateIntervalMinutes / 1440}d")
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="syncSource(@source.Id)" title="Sync Now">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="testSource(@source.Id)" title="Test Connection">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <a href="/admin/sources/edit/@source.Id" class="btn btn-outline-secondary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split" 
                                                data-bs-toggle="dropdown" title="More Actions">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="/admin/sources/edit/@source.Id">
                                                <i class="fas fa-edit me-2"></i>Edit Configuration
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="viewSourceStats(@source.Id)">
                                                <i class="fas fa-chart-bar me-2"></i>View Statistics
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="@(source.IsActive ? "deactivateSource" : "activateSource")(@source.Id)">
                                                <i class="fas fa-toggle-@(source.IsActive ? "off" : "on") me-2"></i>@(source.IsActive ? "Deactivate" : "Activate")
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="/admin/sources/delete/@source.Id">
                                                <i class="fas fa-trash me-2"></i>Delete Source
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

        @if (!Model.Sources.Any())
        {
            <div class="text-center py-5">
                <i class="fas fa-rss fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No sources found</h5>
                <p class="text-muted">Get started by adding your first content source.</p>
                <a href="/admin/sources/create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add Your First Source
                </a>
            </div>
        }

        <!-- Pagination -->
        @if (Model.Pagination.TotalPages > 1)
        {
            <nav aria-label="Sources pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item @(!Model.Pagination.HasPrevious ? "disabled" : "")">
                        <a class="page-link" href="?page=@(Model.Pagination.CurrentPage - 1)@GetFilterQueryString()">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    
                    @for (int i = Math.Max(1, Model.Pagination.CurrentPage - 2); i <= Math.Min(Model.Pagination.TotalPages, Model.Pagination.CurrentPage + 2); i++)
                    {
                        <li class="page-item @(i == Model.Pagination.CurrentPage ? "active" : "")">
                            <a class="page-link" href="?page=@i@GetFilterQueryString()">@i</a>
                        </li>
                    }
                    
                    <li class="page-item @(!Model.Pagination.HasNext ? "disabled" : "")">
                        <a class="page-link" href="?page=@(Model.Pagination.CurrentPage + 1)@GetFilterQueryString()">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                </ul>
                
                <div class="text-center text-muted">
                    Showing @((Model.Pagination.CurrentPage - 1) * Model.Pagination.PageSize + 1) to 
                    @Math.Min(Model.Pagination.CurrentPage * Model.Pagination.PageSize, Model.Pagination.TotalItems) 
                    of @Model.Pagination.TotalItems.ToString("N0") sources
                </div>
            </nav>
        }
    </div>
</div>

@section Scripts {
    <script>
        let selectedSources = [];

        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const sourceCheckboxes = document.querySelectorAll('.source-checkbox');
            
            sourceCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            
            updateSelectedSources();
        }

        function updateSelectedSources() {
            selectedSources = Array.from(document.querySelectorAll('.source-checkbox:checked'))
                .map(checkbox => parseInt(checkbox.value));
            
            document.getElementById('selectAll').indeterminate = 
                selectedSources.length > 0 && selectedSources.length < document.querySelectorAll('.source-checkbox').length;
        }

        // Add event listeners to source checkboxes
        document.querySelectorAll('.source-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedSources);
        });

        async function syncSource(sourceId) {
            const button = event.target.closest('button');
            const originalHtml = button.innerHTML;
            
            try {
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                button.disabled = true;
                
                const response = await fetch('/admin/dashboard/TriggerSync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                    },
                    body: JSON.stringify([sourceId])
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('success', 'Sync triggered successfully');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', 'Failed to trigger sync: ' + result.message);
                }
            } catch (error) {
                showAlert('danger', 'Error triggering sync: ' + error.message);
            } finally {
                button.innerHTML = originalHtml;
                button.disabled = false;
            }
        }

        async function syncAllSources() {
            const button = document.getElementById('syncAllBtn');
            const originalHtml = button.innerHTML;
            
            if (!confirm('Are you sure you want to sync all active sources? This may take some time.')) {
                return;
            }
            
            try {
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Syncing...';
                button.disabled = true;
                
                // Get all active source IDs
                const allSourceIds = Array.from(document.querySelectorAll('[data-source-id]'))
                    .map(row => parseInt(row.dataset.sourceId));
                
                const response = await fetch('/admin/dashboard/TriggerSync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                    },
                    body: JSON.stringify(allSourceIds)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('success', 'Sync triggered for all sources');
                    setTimeout(() => location.reload(), 3000);
                } else {
                    showAlert('danger', 'Failed to trigger sync: ' + result.message);
                }
            } catch (error) {
                showAlert('danger', 'Error triggering sync: ' + error.message);
            } finally {
                button.innerHTML = originalHtml;
                button.disabled = false;
            }
        }

        async function testSource(sourceId) {
            const button = event.target.closest('button');
            const originalHtml = button.innerHTML;
            
            try {
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                button.disabled = true;
                
                const response = await fetch('/admin/sources/TestSource', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                    },
                    body: JSON.stringify(sourceId)
                });
                
                const result = await response.json();
                
                if (result.isSuccessful) {
                    showAlert('success', `Connection test successful! Found ${result.testArticleCount} articles. Response time: ${result.responseTime}ms`);
                } else {
                    showAlert('danger', 'Connection test failed: ' + result.message);
                }
                
                if (result.warnings && result.warnings.length > 0) {
                    result.warnings.forEach(warning => showAlert('warning', warning));
                }
            } catch (error) {
                showAlert('danger', 'Error testing source: ' + error.message);
            } finally {
                button.innerHTML = originalHtml;
                button.disabled = false;
            }
        }

        async function bulkAction(action) {
            if (selectedSources.length === 0) {
                showAlert('warning', 'Please select sources first');
                return;
            }
            
            const actionNames = {
                'activate': 'activate',
                'deactivate': 'deactivate', 
                'sync': 'sync',
                'delete': 'delete'
            };
            
            if (!confirm(`Are you sure you want to ${actionNames[action]} ${selectedSources.length} selected sources?`)) {
                return;
            }
            
            try {
                const response = await fetch('/admin/sources/BulkAction', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                    },
                    body: JSON.stringify({
                        sourceIds: selectedSources,
                        action: action
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('success', result.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', 'Bulk action failed: ' + result.message);
                }
            } catch (error) {
                showAlert('danger', 'Error performing bulk action: ' + error.message);
            }
        }

        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            const container = document.querySelector('.admin-content');
            container.insertAdjacentHTML('afterbegin', alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
}

@functions {
    string GetFilterQueryString()
    {
        var queryParams = new List<string>();
        
        if (!string.IsNullOrEmpty(Model.Filters.SearchTerm))
            queryParams.Add($"SearchTerm={Uri.EscapeDataString(Model.Filters.SearchTerm)}");
        
        if (Model.Filters.Type.HasValue)
            queryParams.Add($"Type={Model.Filters.Type}");
        
        if (!string.IsNullOrEmpty(Model.Filters.Status))
            queryParams.Add($"Status={Model.Filters.Status}");
        
        if (Model.Filters.IsActive.HasValue)
            queryParams.Add($"IsActive={Model.Filters.IsActive}");
        
        if (!string.IsNullOrEmpty(Model.Filters.SortBy))
            queryParams.Add($"SortBy={Model.Filters.SortBy}");
        
        if (!string.IsNullOrEmpty(Model.Filters.SortDirection))
            queryParams.Add($"SortDirection={Model.Filters.SortDirection}");
        
        return queryParams.Any() ? "&" + string.Join("&", queryParams) : "";
    }
}