using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NewsSite.Areas.Admin.Models;
using NewsSite.Data;
using NewsSite.Models;
using NewsSite.Services;

namespace NewsSite.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class DashboardController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IContentService _contentService;
        private readonly ISourceService _sourceService;
        private readonly UserManager<IdentityUser> _userManager;
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(
            ApplicationDbContext context,
            IContentService contentService,
            ISourceService sourceService,
            UserManager<IdentityUser> userManager,
            ILogger<DashboardController> logger)
        {
            _context = context;
            _contentService = contentService;
            _sourceService = sourceService;
            _userManager = userManager;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var viewModel = new AdminDashboardViewModel
                {
                    Stats = await GetDashboardStatsAsync(),
                    SourceHealth = await GetSourceHealthStatusAsync(),
                    RecentActivities = await GetRecentActivitiesAsync(),
                    SystemHealth = await GetSystemHealthAsync(),
                    ContentMetrics = await GetContentMetricsAsync()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading admin dashboard");
                TempData["Error"] = "Failed to load dashboard data. Please try again.";
                return View(new AdminDashboardViewModel());
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetStats()
        {
            try
            {
                var stats = await GetDashboardStatsAsync();
                return Json(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving dashboard stats");
                return Json(new { error = "Failed to retrieve stats" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetSourceHealth()
        {
            try
            {
                var sourceHealth = await GetSourceHealthStatusAsync();
                return Json(sourceHealth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving source health");
                return Json(new { error = "Failed to retrieve source health" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetSystemHealth()
        {
            try
            {
                var systemHealth = await GetSystemHealthAsync();
                return Json(systemHealth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving system health");
                return Json(new { error = "Failed to retrieve system health" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> TriggerSync([FromBody] List<int> sourceIds)
        {
            try
            {
                if (sourceIds?.Any() != true)
                {
                    return Json(new { success = false, message = "No sources selected" });
                }

                // In a real implementation, this would trigger background jobs
                await LogActivity($"Manual sync triggered for {sourceIds.Count} sources", "Info");
                
                return Json(new { success = true, message = $"Sync triggered for {sourceIds.Count} sources" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error triggering sync");
                return Json(new { success = false, message = "Failed to trigger sync" });
            }
        }

        private async Task<DashboardStats> GetDashboardStatsAsync()
        {
            var now = DateTime.UtcNow;
            var today = now.Date;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(today.Year, today.Month, 1);

            return new DashboardStats
            {
                TotalArticles = await _context.Articles.CountAsync(),
                TotalSources = await _context.Sources.CountAsync(),
                ActiveSources = await _context.Sources.CountAsync(s => s.IsActive),
                FailedSyncs = await _context.Sources.CountAsync(s => s.ConsecutiveFailures > 0),
                ArticlesToday = await _context.Articles.CountAsync(a => a.CreatedDate >= today),
                ArticlesThisWeek = await _context.Articles.CountAsync(a => a.CreatedDate >= weekStart),
                ArticlesThisMonth = await _context.Articles.CountAsync(a => a.CreatedDate >= monthStart),
                TotalCategories = await _context.Categories.CountAsync(c => c.IsActive),
                TotalTags = await _context.Tags.CountAsync(t => t.IsActive),
                UsersCount = await _userManager.Users.CountAsync(),
                LastSync = await _context.Sources
                    .Where(s => s.LastSyncDate.HasValue)
                    .Select(s => s.LastSyncDate!.Value)
                    .OrderByDescending(d => d)
                    .FirstOrDefaultAsync()
            };
        }

        private async Task<List<SourceHealthStatus>> GetSourceHealthStatusAsync()
        {
            var sources = await _context.Sources
                .Include(s => s.Configuration)
                .Select(s => new SourceHealthStatus
                {
                    Id = s.Id,
                    Name = s.Name,
                    Type = s.Type,
                    Status = s.ConsecutiveFailures == 0 ? "Healthy" :
                             s.ConsecutiveFailures < 3 ? "Warning" : "Error",
                    LastSync = s.LastSyncDate,
                    LastSuccessfulSync = s.LastSuccessfulSyncDate,
                    ConsecutiveFailures = s.ConsecutiveFailures,
                    LastError = s.LastErrorMessage,
                    ArticlesThisWeek = s.Articles.Count(a => a.CreatedDate >= DateTime.UtcNow.AddDays(-7)),
                    IsActive = s.IsActive
                })
                .OrderByDescending(s => s.LastSync)
                .Take(10)
                .ToListAsync();

            return sources;
        }

        private async Task<List<RecentActivity>> GetRecentActivitiesAsync()
        {
            // In a real implementation, this would come from an audit log table
            var activities = new List<RecentActivity>();
            
            // Get recent articles as activity
            var recentArticles = await _context.Articles
                .Include(a => a.Source)
                .OrderByDescending(a => a.CreatedDate)
                .Take(5)
                .Select(a => new RecentActivity
                {
                    Timestamp = a.CreatedDate,
                    Activity = "Article Created",
                    Details = $"New article '{a.Title}' from {a.Source.Name}",
                    Type = "Success"
                })
                .ToListAsync();

            activities.AddRange(recentArticles);

            // Get sources with recent errors
            var errorSources = await _context.Sources
                .Where(s => s.ConsecutiveFailures > 0 && s.LastSyncDate.HasValue)
                .OrderByDescending(s => s.LastSyncDate)
                .Take(3)
                .Select(s => new RecentActivity
                {
                    Timestamp = s.LastSyncDate!.Value,
                    Activity = "Sync Failed",
                    Details = $"Source '{s.Name}' failed: {s.LastErrorMessage}",
                    Type = "Error"
                })
                .ToListAsync();

            activities.AddRange(errorSources);

            return activities.OrderByDescending(a => a.Timestamp).Take(10).ToList();
        }

        private async Task<SystemHealth> GetSystemHealthAsync()
        {
            var issues = new List<string>();
            
            // Check for sources with consecutive failures
            var failedSources = await _context.Sources.CountAsync(s => s.ConsecutiveFailures >= 3);
            if (failedSources > 0)
            {
                issues.Add($"{failedSources} sources have multiple consecutive failures");
            }

            // Check for old articles that haven't been synced
            var oldestSync = await _context.Sources
                .Where(s => s.IsActive && s.LastSyncDate.HasValue)
                .Select(s => s.LastSyncDate!.Value)
                .OrderBy(d => d)
                .FirstOrDefaultAsync();

            if (oldestSync < DateTime.UtcNow.AddHours(-6))
            {
                issues.Add("Some sources haven't synced in over 6 hours");
            }

            // Check database size (simplified)
            var articleCount = await _context.Articles.CountAsync();
            var estimatedDbSize = articleCount * 1024; // Rough estimate

            return new SystemHealth
            {
                IsHealthy = issues.Count == 0,
                CpuUsage = new Random().NextDouble() * 100, // Placeholder
                MemoryUsage = new Random().NextDouble() * 100, // Placeholder
                DatabaseSize = estimatedDbSize,
                ActiveJobs = 0, // Would integrate with Hangfire
                FailedJobs = 0, // Would integrate with Hangfire
                LastHealthCheck = DateTime.UtcNow,
                Issues = issues
            };
        }

        private async Task<List<ContentMetrics>> GetContentMetricsAsync()
        {
            var last7Days = DateTime.UtcNow.AddDays(-7);
            
            var metrics = await _context.Categories
                .Where(c => c.IsActive)
                .Select(c => new ContentMetrics
                {
                    CategoryName = c.Name,
                    ArticleCount = c.Articles.Count(a => a.CreatedDate >= last7Days),
                    ViewCount = 0, // Would need to implement view tracking
                    EngagementRate = 0.0, // Would need to implement engagement tracking
                    Date = DateTime.UtcNow
                })
                .ToListAsync();

            return metrics;
        }

        private async Task LogActivity(string activity, string type, string? details = null)
        {
            // In a real implementation, this would log to an audit table
            _logger.LogInformation("Admin Activity: {Activity} - {Type} - {Details}", activity, type, details);
            await Task.CompletedTask;
        }
    }
}