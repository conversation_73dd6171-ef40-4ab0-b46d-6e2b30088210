# AI Frontiers Azure Deployment Script
# This script deploys the complete AI Frontiers application to Azure

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$SubscriptionId,
    
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [SecureString]$SqlAdminPassword,
    
    [Parameter(Mandatory=$false)]
    [string]$Location = "East US 2",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipInfrastructure,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipDatabase,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipApplication
)

# Import required modules
if (!(Get-Module -ListAvailable -Name Az)) {
    Write-Host "Installing Azure PowerShell module..." -ForegroundColor Yellow
    Install-Module -Name Az -AllowClobber -Scope CurrentUser -Force
}

Import-Module Az

# Connect to Azure
Write-Host "Connecting to Azure..." -ForegroundColor Green
Connect-AzAccount
Set-AzContext -SubscriptionId $SubscriptionId

# Variables
$AppName = "aifrontiers"
$DeploymentName = "aifrontiers-deployment-$(Get-Date -Format 'yyyyMMddHHmmss')"
$ParametersFile = "azure-infrastructure.parameters.json"
$TemplateFile = "azure-infrastructure.json"

# Create resource group if it doesn't exist
Write-Host "Checking resource group..." -ForegroundColor Green
$rg = Get-AzResourceGroup -Name $ResourceGroupName -ErrorAction SilentlyContinue
if (!$rg) {
    Write-Host "Creating resource group $ResourceGroupName..." -ForegroundColor Yellow
    New-AzResourceGroup -Name $ResourceGroupName -Location $Location
}

# Deploy infrastructure
if (!$SkipInfrastructure) {
    Write-Host "Deploying Azure infrastructure..." -ForegroundColor Green
    
    # Update parameters
    $parameters = @{
        appName = $AppName
        environment = $Environment
        location = $Location
        sqlAdminPassword = $SqlAdminPassword
    }
    
    $deployment = New-AzResourceGroupDeployment `
        -ResourceGroupName $ResourceGroupName `
        -TemplateFile $TemplateFile `
        -TemplateParameterObject $parameters `
        -Name $DeploymentName `
        -Verbose
    
    if ($deployment.ProvisioningState -eq "Succeeded") {
        Write-Host "Infrastructure deployment completed successfully!" -ForegroundColor Green
        
        # Output important values
        Write-Host "Web App URL: $($deployment.Outputs.webAppUrl.Value)" -ForegroundColor Cyan
        Write-Host "SQL Server FQDN: $($deployment.Outputs.sqlServerFqdn.Value)" -ForegroundColor Cyan
        Write-Host "Key Vault URI: $($deployment.Outputs.keyVaultUri.Value)" -ForegroundColor Cyan
        Write-Host "CDN Endpoint: $($deployment.Outputs.cdnEndpointUrl.Value)" -ForegroundColor Cyan
    } else {
        Write-Error "Infrastructure deployment failed!"
        exit 1
    }
}

# Deploy database migrations
if (!$SkipDatabase) {
    Write-Host "Deploying database migrations..." -ForegroundColor Green
    
    try {
        # Build the project first
        Write-Host "Building project..." -ForegroundColor Yellow
        dotnet build --configuration Release
        
        # Create connection string
        $SqlServerName = "$AppName-sql-$Environment.database.windows.net"
        $SqlDatabaseName = "$AppName-db-$Environment"
        $SqlAdminUsername = "$AppName-admin"
        
        # Convert SecureString to plain text for connection string
        $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($SqlAdminPassword)
        $PlainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
        
        $ConnectionString = "Server=tcp:$SqlServerName,1433;Initial Catalog=$SqlDatabaseName;Persist Security Info=False;User ID=$SqlAdminUsername;Password=$PlainPassword;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=60;"
        
        # Run EF migrations
        Write-Host "Running Entity Framework migrations..." -ForegroundColor Yellow
        $env:ASPNETCORE_ENVIRONMENT = "Production"
        dotnet ef database update --connection $ConnectionString --verbose
        
        # Run custom SQL script for optimization
        Write-Host "Running database optimization script..." -ForegroundColor Yellow
        Invoke-Sqlcmd -ServerInstance $SqlServerName -Database $SqlDatabaseName -Username $SqlAdminUsername -Password $PlainPassword -InputFile "deploy-database.sql" -TrustServerCertificate
        
        Write-Host "Database deployment completed successfully!" -ForegroundColor Green
    }
    catch {
        Write-Error "Database deployment failed: $($_.Exception.Message)"
        exit 1
    }
    finally {
        # Clear password from memory
        [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)
        $PlainPassword = $null
    }
}

# Deploy application
if (!$SkipApplication) {
    Write-Host "Deploying application..." -ForegroundColor Green
    
    try {
        # Publish the application
        Write-Host "Publishing application..." -ForegroundColor Yellow
        dotnet publish --configuration Release --output ./publish
        
        # Create ZIP package
        Write-Host "Creating deployment package..." -ForegroundColor Yellow
        $ZipPath = "./publish.zip"
        if (Test-Path $ZipPath) { Remove-Item $ZipPath }
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::CreateFromDirectory("./publish", $ZipPath)
        
        # Deploy to Azure Web App
        $WebAppName = "$AppName-web-$Environment"
        Write-Host "Deploying to Azure Web App: $WebAppName..." -ForegroundColor Yellow
        
        Publish-AzWebApp -ResourceGroupName $ResourceGroupName -Name $WebAppName -ArchivePath $ZipPath -Force
        
        Write-Host "Application deployment completed successfully!" -ForegroundColor Green
        
        # Wait for app to start
        Write-Host "Waiting for application to start..." -ForegroundColor Yellow
        Start-Sleep -Seconds 30
        
        # Health check
        $WebAppUrl = "https://$WebAppName.azurewebsites.net"
        try {
            $response = Invoke-WebRequest -Uri "$WebAppUrl/health" -Method GET -TimeoutSec 30
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ Health check passed!" -ForegroundColor Green
                Write-Host "Application is available at: $WebAppUrl" -ForegroundColor Cyan
            } else {
                Write-Warning "Health check returned status code: $($response.StatusCode)"
            }
        }
        catch {
            Write-Warning "Health check failed: $($_.Exception.Message)"
        }
    }
    catch {
        Write-Error "Application deployment failed: $($_.Exception.Message)"
        exit 1
    }
    finally {
        # Cleanup
        if (Test-Path "./publish") { Remove-Item "./publish" -Recurse -Force }
        if (Test-Path "./publish.zip") { Remove-Item "./publish.zip" -Force }
    }
}

Write-Host ""
Write-Host "🚀 Deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Configure custom domain (if needed)"
Write-Host "2. Set up SSL certificate"
Write-Host "3. Configure monitoring alerts"
Write-Host "4. Set up backup schedules"
Write-Host "5. Configure CDN custom domain"
Write-Host ""
Write-Host "Important URLs:" -ForegroundColor Cyan
Write-Host "- Application: https://$AppName-web-$Environment.azurewebsites.net"
Write-Host "- Health Check: https://$AppName-web-$Environment.azurewebsites.net/health"
Write-Host "- Admin Panel: https://$AppName-web-$Environment.azurewebsites.net/admin"
Write-Host ""