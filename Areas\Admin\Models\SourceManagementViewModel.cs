using NewsSite.Models;
using System.ComponentModel.DataAnnotations;

namespace NewsSite.Areas.Admin.Models
{
    public class SourceManagementViewModel
    {
        public List<SourceListItem> Sources { get; set; } = new();
        public SourceFilterOptions Filters { get; set; } = new();
        public PaginationInfo Pagination { get; set; } = new();
    }

    public class SourceListItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public SourceType Type { get; set; }
        public string Url { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime? LastSync { get; set; }
        public DateTime? LastSuccessfulSync { get; set; }
        public int ConsecutiveFailures { get; set; }
        public string Status { get; set; } = string.Empty;
        public int UpdateIntervalMinutes { get; set; }
        public int ArticleCount { get; set; }
        public int RecentArticleCount { get; set; }
        public string? LastError { get; set; }
        public bool HasApiKey { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class SourceFilterOptions
    {
        public SourceType? Type { get; set; }
        public bool? IsActive { get; set; }
        public string? Status { get; set; }
        public string? SearchTerm { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 25;
        public string SortBy { get; set; } = "Name";
        public string SortDirection { get; set; } = "asc";
    }

    public class PaginationInfo
    {
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int TotalItems { get; set; }
        public int PageSize { get; set; }
        public bool HasPrevious { get; set; }
        public bool HasNext { get; set; }
    }

    public class CreateSourceViewModel
    {
        [Required]
        [MaxLength(200)]
        [Display(Name = "Source Name")]
        public string Name { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Source Type")]
        public SourceType Type { get; set; }

        [Required]
        [MaxLength(1000)]
        [Url]
        [Display(Name = "URL")]
        public string Url { get; set; } = string.Empty;

        [MaxLength(500)]
        [Display(Name = "Description")]
        public string? Description { get; set; }

        [MaxLength(200)]
        [Display(Name = "API Key")]
        public string? ApiKey { get; set; }

        [MaxLength(200)]
        [Display(Name = "API Secret")]
        public string? ApiSecret { get; set; }

        [Display(Name = "Active")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "Requires Authentication")]
        public bool RequiresAuthentication { get; set; }

        [Range(1, 1000)]
        [Display(Name = "Max Requests Per Hour")]
        public int MaxRequestsPerHour { get; set; } = 60;

        [Display(Name = "Enable Content Filtering")]
        public bool EnableContentFiltering { get; set; } = true;

        [MaxLength(1000)]
        [Display(Name = "Content Filter Keywords (comma-separated)")]
        public string? ContentFilterKeywords { get; set; }

        // Configuration properties
        [Range(1, 1440)]
        [Display(Name = "Update Interval (Minutes)")]
        public int UpdateIntervalMinutes { get; set; } = 60;

        [Range(1, 500)]
        [Display(Name = "Max Articles Per Sync")]
        public int MaxArticlesPerSync { get; set; } = 50;

        [Range(1, 365)]
        [Display(Name = "Article Retention (Days)")]
        public int ArticleRetentionDays { get; set; } = 30;

        [Display(Name = "Auto Categorize")]
        public bool AutoCategorize { get; set; } = true;

        [Display(Name = "Auto Generate Tags")]
        public bool AutoGenerateTags { get; set; } = true;

        [Display(Name = "Enable Duplicate Detection")]
        public bool EnableDuplicateDetection { get; set; } = true;

        [Range(1, 10)]
        [Display(Name = "Priority (1-10)")]
        public int Priority { get; set; } = 5;

        [Range(50, 2000)]
        [Display(Name = "Max Summary Length")]
        public int MaxSummaryLength { get; set; } = 500;

        [Display(Name = "Use AI Summarization")]
        public bool UseAiSummarization { get; set; } = false;

        [Range(10, 10000)]
        [Display(Name = "Min Content Length")]
        public int MinContentLength { get; set; } = 100;

        [Display(Name = "Require Images")]
        public bool RequireImages { get; set; } = false;

        [Display(Name = "Notify On Errors")]
        public bool NotifyOnErrors { get; set; } = true;

        [Display(Name = "Notify On Success")]
        public bool NotifyOnSuccess { get; set; } = false;

        [Display(Name = "Content From Date")]
        public DateTime? ContentFromDate { get; set; }

        [MaxLength(500)]
        [Display(Name = "Notification Emails (comma-separated)")]
        public string? NotificationEmails { get; set; }

        [MaxLength(1000)]
        [Display(Name = "Custom Headers (JSON)")]
        public string? CustomHeaders { get; set; }

        [MaxLength(2000)]
        [Display(Name = "API Settings (JSON)")]
        public string? ApiSettingsJson { get; set; }
    }

    public class EditSourceViewModel : CreateSourceViewModel
    {
        public int Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
        
        // Display-only properties
        public DateTime? LastSync { get; set; }
        public DateTime? LastSuccessfulSync { get; set; }
        public int ConsecutiveFailures { get; set; }
        public string? LastErrorMessage { get; set; }
        public int ArticleCount { get; set; }
        public int RecentArticleCount { get; set; }

        [Display(Name = "Masked API Key")]
        public string MaskedApiKey => !string.IsNullOrEmpty(ApiKey) ? 
            $"{ApiKey[..Math.Min(4, ApiKey.Length)]}***" : string.Empty;

        [Display(Name = "Masked API Secret")]
        public string MaskedApiSecret => !string.IsNullOrEmpty(ApiSecret) ? 
            $"{ApiSecret[..Math.Min(4, ApiSecret.Length)]}***" : string.Empty;
    }

    public class BulkSourceAction
    {
        public List<int> SourceIds { get; set; } = new();
        public string Action { get; set; } = string.Empty; // "activate", "deactivate", "delete", "sync"
    }

    public class SourceTestResult
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; } = string.Empty;
        public int TestArticleCount { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
    }
}