﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace NewsSite.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddContentModels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Categories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Slug = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Color = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Icon = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Sources",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Url = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ApiKey = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ApiSecret = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    RequiresAuthentication = table.Column<bool>(type: "bit", nullable: false),
                    MaxRequestsPerHour = table.Column<int>(type: "int", nullable: false),
                    EnableContentFiltering = table.Column<bool>(type: "bit", nullable: false),
                    ContentFilterKeywords = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    LastSyncDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastSuccessfulSyncDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ConsecutiveFailures = table.Column<int>(type: "int", nullable: false),
                    LastErrorMessage = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Sources", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Tags",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Slug = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Color = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    UsageCount = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsSystemGenerated = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Tags", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Articles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Title = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Slug = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Summary = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    OriginalUrl = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    ImageUrl = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    ImageAlt = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Author = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    PublishedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    OriginalPublishedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CategoryId = table.Column<int>(type: "int", nullable: false),
                    SourceId = table.Column<int>(type: "int", nullable: false),
                    IsFeatured = table.Column<bool>(type: "bit", nullable: false),
                    IsBreaking = table.Column<bool>(type: "bit", nullable: false),
                    IsTrending = table.Column<bool>(type: "bit", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Priority = table.Column<int>(type: "int", nullable: false),
                    ExternalId = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ExternalData = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    MetaTitle = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    MetaDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, defaultValue: "System"),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Articles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Articles_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Articles_Sources_SourceId",
                        column: x => x.SourceId,
                        principalTable: "Sources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "SourceConfigurations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SourceId = table.Column<int>(type: "int", nullable: false),
                    UpdateIntervalMinutes = table.Column<int>(type: "int", nullable: false),
                    MaxArticlesPerSync = table.Column<int>(type: "int", nullable: false),
                    ArticleRetentionDays = table.Column<int>(type: "int", nullable: false),
                    AutoCategorize = table.Column<bool>(type: "bit", nullable: false),
                    AutoGenerateTags = table.Column<bool>(type: "bit", nullable: false),
                    EnableDuplicateDetection = table.Column<bool>(type: "bit", nullable: false),
                    MaxSummaryLength = table.Column<int>(type: "int", nullable: false),
                    UseAiSummarization = table.Column<bool>(type: "bit", nullable: false),
                    Priority = table.Column<int>(type: "int", nullable: false),
                    ContentFromDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CustomHeaders = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    XPathSelectors = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    MinContentLength = table.Column<int>(type: "int", nullable: false),
                    RequireImages = table.Column<bool>(type: "bit", nullable: false),
                    NotifyOnErrors = table.Column<bool>(type: "bit", nullable: false),
                    NotifyOnSuccess = table.Column<bool>(type: "bit", nullable: false),
                    NotificationEmails = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SourceConfigurations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SourceConfigurations_Sources_SourceId",
                        column: x => x.SourceId,
                        principalTable: "Sources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ArticleTags",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ArticleId = table.Column<int>(type: "int", nullable: false),
                    TagId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsSystemGenerated = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ArticleTags", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ArticleTags_Articles_ArticleId",
                        column: x => x.ArticleId,
                        principalTable: "Articles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ArticleTags_Tags_TagId",
                        column: x => x.TagId,
                        principalTable: "Tags",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ContentMetadata",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ArticleId = table.Column<int>(type: "int", nullable: false),
                    ContentHash = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: false),
                    UrlHash = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true),
                    ViewCount = table.Column<int>(type: "int", nullable: false),
                    ShareCount = table.Column<int>(type: "int", nullable: false),
                    LikeCount = table.Column<int>(type: "int", nullable: false),
                    CommentCount = table.Column<int>(type: "int", nullable: false),
                    ClickThroughCount = table.Column<int>(type: "int", nullable: false),
                    LastClickDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    WordCount = table.Column<int>(type: "int", nullable: false),
                    ReadingTimeMinutes = table.Column<int>(type: "int", nullable: false),
                    Language = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    QualityScore = table.Column<decimal>(type: "decimal(5,2)", nullable: false),
                    HasImages = table.Column<bool>(type: "bit", nullable: false),
                    HasVideo = table.Column<bool>(type: "bit", nullable: false),
                    MetaKeywords = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    MetaDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ExternalLikes = table.Column<int>(type: "int", nullable: true),
                    ExternalShares = table.Column<int>(type: "int", nullable: true),
                    ExternalComments = table.Column<int>(type: "int", nullable: true),
                    TrendingScore = table.Column<decimal>(type: "decimal(10,4)", nullable: false),
                    TrendingCalculatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDuplicate = table.Column<bool>(type: "bit", nullable: false),
                    OriginalArticleId = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContentMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContentMetadata_Articles_ArticleId",
                        column: x => x.ArticleId,
                        principalTable: "Articles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ContentMetadata_Articles_OriginalArticleId",
                        column: x => x.OriginalArticleId,
                        principalTable: "Articles",
                        principalColumn: "Id");
                });

            migrationBuilder.InsertData(
                table: "Categories",
                columns: new[] { "Id", "Color", "CreatedDate", "Description", "DisplayOrder", "Icon", "IsActive", "ModifiedDate", "Name", "Slug" },
                values: new object[,]
                {
                    { 1, "#ff4444", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4548), "Latest breaking news and developments in artificial intelligence", 1, "fas fa-bolt", true, null, "Breaking AI News", "breaking-ai-news" },
                    { 2, "#ff0000", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4551), "Curated AI content from top YouTube channels and creators", 2, "fab fa-youtube", true, null, "YouTube AI Discoveries", "youtube-ai-discoveries" },
                    { 3, "#4CAF50", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4553), "Academic research, papers, and scientific breakthroughs in AI", 3, "fas fa-flask", true, null, "Research & Papers", "research-papers" },
                    { 4, "#2196F3", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4555), "AI agents, autonomous systems, and intelligent platforms", 4, "fas fa-robot", true, null, "Agentic Platforms", "agentic-platforms" },
                    { 5, "#9C27B0", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4557), "Tools, frameworks, and resources for AI developers", 5, "fas fa-tools", true, null, "AI Development Tools", "ai-development-tools" },
                    { 6, "#FF9800", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4559), "Popular open-source AI projects and repositories", 6, "fab fa-github", true, null, "Trending Open Source", "trending-open-source" }
                });

            migrationBuilder.InsertData(
                table: "Tags",
                columns: new[] { "Id", "Color", "CreatedDate", "Description", "IsActive", "IsSystemGenerated", "ModifiedDate", "Name", "Slug", "UsageCount" },
                values: new object[,]
                {
                    { 1, "#4CAF50", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4716), null, true, false, null, "Machine Learning", "machine-learning", 0 },
                    { 2, "#2196F3", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4718), null, true, false, null, "Deep Learning", "deep-learning", 0 },
                    { 3, "#FF9800", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4720), null, true, false, null, "Natural Language Processing", "nlp", 0 },
                    { 4, "#9C27B0", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4722), null, true, false, null, "Computer Vision", "computer-vision", 0 },
                    { 5, "#FF5722", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4723), null, true, false, null, "Large Language Models", "llm", 0 },
                    { 6, "#607D8B", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4725), null, true, false, null, "AI Ethics", "ai-ethics", 0 },
                    { 7, "#E91E63", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4726), null, true, false, null, "Generative AI", "generative-ai", 0 },
                    { 8, "#795548", new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4728), null, true, false, null, "Robotics", "robotics", 0 }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Articles_CategoryId",
                table: "Articles",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Articles_ExternalId",
                table: "Articles",
                column: "ExternalId");

            migrationBuilder.CreateIndex(
                name: "IX_Articles_Featured_PublishedDate",
                table: "Articles",
                columns: new[] { "IsFeatured", "PublishedDate" });

            migrationBuilder.CreateIndex(
                name: "IX_Articles_PublishedDate",
                table: "Articles",
                column: "PublishedDate");

            migrationBuilder.CreateIndex(
                name: "IX_Articles_Slug",
                table: "Articles",
                column: "Slug",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Articles_SourceId",
                table: "Articles",
                column: "SourceId");

            migrationBuilder.CreateIndex(
                name: "IX_Articles_Status_IsDeleted",
                table: "Articles",
                columns: new[] { "Status", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Articles_Trending_PublishedDate",
                table: "Articles",
                columns: new[] { "IsTrending", "PublishedDate" });

            migrationBuilder.CreateIndex(
                name: "IX_ArticleTags_ArticleId_TagId",
                table: "ArticleTags",
                columns: new[] { "ArticleId", "TagId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ArticleTags_TagId",
                table: "ArticleTags",
                column: "TagId");

            migrationBuilder.CreateIndex(
                name: "IX_Categories_Active_DisplayOrder",
                table: "Categories",
                columns: new[] { "IsActive", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_Categories_Slug",
                table: "Categories",
                column: "Slug",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ContentMetadata_ArticleId",
                table: "ContentMetadata",
                column: "ArticleId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ContentMetadata_ContentHash",
                table: "ContentMetadata",
                column: "ContentHash");

            migrationBuilder.CreateIndex(
                name: "IX_ContentMetadata_Duplicate_Original",
                table: "ContentMetadata",
                columns: new[] { "IsDuplicate", "OriginalArticleId" });

            migrationBuilder.CreateIndex(
                name: "IX_ContentMetadata_OriginalArticleId",
                table: "ContentMetadata",
                column: "OriginalArticleId");

            migrationBuilder.CreateIndex(
                name: "IX_ContentMetadata_Trending",
                table: "ContentMetadata",
                columns: new[] { "TrendingScore", "TrendingCalculatedDate" });

            migrationBuilder.CreateIndex(
                name: "IX_ContentMetadata_UrlHash",
                table: "ContentMetadata",
                column: "UrlHash");

            migrationBuilder.CreateIndex(
                name: "IX_SourceConfigurations_Priority_UpdateInterval",
                table: "SourceConfigurations",
                columns: new[] { "Priority", "UpdateIntervalMinutes" });

            migrationBuilder.CreateIndex(
                name: "IX_SourceConfigurations_SourceId",
                table: "SourceConfigurations",
                column: "SourceId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Sources_Active_Type",
                table: "Sources",
                columns: new[] { "IsActive", "Type" });

            migrationBuilder.CreateIndex(
                name: "IX_Sources_LastSyncDate",
                table: "Sources",
                column: "LastSyncDate");

            migrationBuilder.CreateIndex(
                name: "IX_Tags_Active_UsageCount",
                table: "Tags",
                columns: new[] { "IsActive", "UsageCount" });

            migrationBuilder.CreateIndex(
                name: "IX_Tags_Slug",
                table: "Tags",
                column: "Slug",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ArticleTags");

            migrationBuilder.DropTable(
                name: "ContentMetadata");

            migrationBuilder.DropTable(
                name: "SourceConfigurations");

            migrationBuilder.DropTable(
                name: "Tags");

            migrationBuilder.DropTable(
                name: "Articles");

            migrationBuilder.DropTable(
                name: "Categories");

            migrationBuilder.DropTable(
                name: "Sources");
        }
    }
}
