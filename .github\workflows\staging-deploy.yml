name: Deploy to Staging

on:
  push:
    branches: [ develop ]
  workflow_dispatch:

env:
  AZURE_WEBAPP_NAME: aifrontiers-web-staging
  AZURE_WEBAPP_PACKAGE_PATH: '.'
  DOTNET_VERSION: '8.0.x'
  RESOURCE_GROUP: rg-aifrontiers-staging

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build application
      run: dotnet build --configuration Release --no-restore
    
    - name: Run tests
      run: dotnet test --configuration Release --no-build --verbosity normal
    
    - name: Publish application
      run: dotnet publish --configuration Release --output ./publish --no-build
    
    - name: Upload artifact for deployment job
      uses: actions/upload-artifact@v3
      with:
        name: .net-app-staging
        path: ./publish

  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment: staging
    
    steps:
    - name: Download artifact from build job
      uses: actions/download-artifact@v3
      with:
        name: .net-app-staging
        path: ./publish
    
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS_STAGING }}
    
    - name: Deploy to Azure Web App
      uses: azure/webapps-deploy@v2
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        package: ./publish
    
    - name: Configure App Settings
      uses: azure/appservice-settings@v1
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        app-settings-json: |
          [
            {
              "name": "ASPNETCORE_ENVIRONMENT",
              "value": "Staging"
            }
          ]

  smoke-tests:
    runs-on: ubuntu-latest
    needs: deploy
    
    steps:
    - name: Wait for deployment
      run: sleep 30
    
    - name: Smoke Test
      run: |
        # Test health endpoint
        response=$(curl -s -o /dev/null -w "%{http_code}" https://${{ env.AZURE_WEBAPP_NAME }}.azurewebsites.net/health)
        if [ $response -eq 200 ]; then
          echo "✅ Staging health check passed"
        else
          echo "❌ Staging health check failed"
          exit 1
        fi