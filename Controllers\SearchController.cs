using Microsoft.AspNetCore.Mvc;
using NewsSite.Services;
using NewsSite.Models;

namespace NewsSite.Controllers
{
    public class SearchController : Controller
    {
        private readonly ISearchService _searchService;
        private readonly ICategoryService _categoryService;
        private readonly ISourceService _sourceService;
        private readonly ITagService _tagService;
        private readonly ILogger<SearchController> _logger;

        public SearchController(
            ISearchService searchService,
            ICategoryService categoryService,
            ISourceService sourceService,
            ITagService tagService,
            ILogger<SearchController> logger)
        {
            _searchService = searchService;
            _categoryService = categoryService;
            _sourceService = sourceService;
            _tagService = tagService;
            _logger = logger;
        }

        // GET: /Search
        [HttpGet]
        public async Task<IActionResult> Index(string q = "", int page = 1, int size = 20)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q))
                {
                    // Show search form with trending/popular queries
                    var model = new SearchIndexViewModel
                    {
                        PopularSearches = await _searchService.GetPopularSearchesAsync(10),
                        TrendingQueries = await _searchService.GetTrendingQueriesAsync(8),
                        Categories = await _categoryService.GetActiveCategoriesAsync(),
                        RecentSearches = GetRecentSearches()
                    };
                    return View(model);
                }

                // Perform search
                var searchResults = await _searchService.SearchAsync(q, page, size);
                var viewModel = new SearchResultsViewModel
                {
                    Results = searchResults,
                    Query = q,
                    Page = page,
                    PageSize = size,
                    Categories = await _categoryService.GetActiveCategoriesAsync(),
                    Sources = await _sourceService.GetActiveSourcesAsync(),
                    PopularTags = await _tagService.GetPopularTagsAsync(20),
                    FilterOptions = CreateFilterOptions(),
                    SortOptions = CreateSortOptions()
                };

                // Store search in recent searches
                StoreRecentSearch(q);

                return View("Results", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing search for query: {Query}", q);
                return View("Error");
            }
        }

        // GET: /Search/Advanced
        [HttpGet("Search/Advanced")]
        public async Task<IActionResult> Advanced()
        {
            try
            {
                var model = new AdvancedSearchViewModel
                {
                    Categories = await _categoryService.GetActiveCategoriesAsync(),
                    Sources = await _sourceService.GetActiveSourcesAsync(),
                    PopularTags = await _tagService.GetPopularTagsAsync(50),
                    Authors = await GetPopularAuthorsAsync(),
                    FilterOptions = CreateFilterOptions(),
                    SortOptions = CreateSortOptions(),
                    DateRangeOptions = CreateDateRangeOptions()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading advanced search page");
                return View("Error");
            }
        }

        // POST: /Search/Advanced
        [HttpPost("Search/Advanced")]
        public async Task<IActionResult> Advanced(AdvancedSearchRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var model = new AdvancedSearchViewModel
                    {
                        Categories = await _categoryService.GetActiveCategoriesAsync(),
                        Sources = await _sourceService.GetActiveSourcesAsync(),
                        PopularTags = await _tagService.GetPopularTagsAsync(50),
                        Authors = await GetPopularAuthorsAsync(),
                        FilterOptions = CreateFilterOptions(),
                        SortOptions = CreateSortOptions(),
                        DateRangeOptions = CreateDateRangeOptions(),
                        SearchRequest = request
                    };
                    
                    return View(model);
                }

                var filters = MapToSearchFilters(request);
                var sortOptions = MapToSortOptions(request);

                var searchResults = await _searchService.AdvancedSearchAsync(
                    request.Query ?? "", 
                    filters, 
                    sortOptions, 
                    request.Page, 
                    request.PageSize);

                var viewModel = new SearchResultsViewModel
                {
                    Results = searchResults,
                    Query = request.Query ?? "",
                    Page = request.Page,
                    PageSize = request.PageSize,
                    Categories = await _categoryService.GetActiveCategoriesAsync(),
                    Sources = await _sourceService.GetActiveSourcesAsync(),
                    PopularTags = await _tagService.GetPopularTagsAsync(20),
                    FilterOptions = CreateFilterOptions(),
                    SortOptions = CreateSortOptions(),
                    IsAdvancedSearch = true,
                    AdvancedFilters = filters
                };

                return View("Results", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing advanced search");
                return View("Error");
            }
        }

        // GET: /Search/Category/{categoryId}
        [HttpGet("Search/Category/{categoryId:int}")]
        public async Task<IActionResult> Category(int categoryId, string q = "", int page = 1, int size = 20)
        {
            try
            {
                var category = await _categoryService.GetCategoryByIdAsync(categoryId);
                if (category == null)
                {
                    return NotFound();
                }

                var searchResults = await _searchService.SearchInCategoryAsync(q, categoryId, page, size);
                var viewModel = new SearchResultsViewModel
                {
                    Results = searchResults,
                    Query = q,
                    Page = page,
                    PageSize = size,
                    Categories = await _categoryService.GetActiveCategoriesAsync(),
                    Sources = await _sourceService.GetActiveSourcesAsync(),
                    PopularTags = await _tagService.GetPopularTagsAsync(20),
                    FilterOptions = CreateFilterOptions(),
                    SortOptions = CreateSortOptions(),
                    SelectedCategory = category,
                    SearchContext = $"in {category.Name}"
                };

                return View("Results", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching in category {CategoryId}", categoryId);
                return View("Error");
            }
        }

        // GET: /Search/Source/{sourceId}
        [HttpGet("Search/Source/{sourceId:int}")]
        public async Task<IActionResult> Source(int sourceId, string q = "", int page = 1, int size = 20)
        {
            try
            {
                var source = await _sourceService.GetSourceByIdAsync(sourceId);
                if (source == null)
                {
                    return NotFound();
                }

                var searchResults = await _searchService.SearchInSourceAsync(q, sourceId, page, size);
                var viewModel = new SearchResultsViewModel
                {
                    Results = searchResults,
                    Query = q,
                    Page = page,
                    PageSize = size,
                    Categories = await _categoryService.GetActiveCategoriesAsync(),
                    Sources = await _sourceService.GetActiveSourcesAsync(),
                    PopularTags = await _tagService.GetPopularTagsAsync(20),
                    FilterOptions = CreateFilterOptions(),
                    SortOptions = CreateSortOptions(),
                    SelectedSource = source,
                    SearchContext = $"in {source.Name}"
                };

                return View("Results", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching in source {SourceId}", sourceId);
                return View("Error");
            }
        }

        // GET: /Search/Author/{author}
        [HttpGet("Search/Author/{author}")]
        public async Task<IActionResult> Author(string author, string q = "", int page = 1, int size = 20)
        {
            try
            {
                var searchResults = await _searchService.SearchByAuthorAsync(q, author, page, size);
                var viewModel = new SearchResultsViewModel
                {
                    Results = searchResults,
                    Query = q,
                    Page = page,
                    PageSize = size,
                    Categories = await _categoryService.GetActiveCategoriesAsync(),
                    Sources = await _sourceService.GetActiveSourcesAsync(),
                    PopularTags = await _tagService.GetPopularTagsAsync(20),
                    FilterOptions = CreateFilterOptions(),
                    SortOptions = CreateSortOptions(),
                    SelectedAuthor = author,
                    SearchContext = $"by {author}"
                };

                return View("Results", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching by author {Author}", author);
                return View("Error");
            }
        }

        // GET: /Search/Tags
        [HttpGet("Search/Tags")]
        public async Task<IActionResult> Tags(string tags = "", string q = "", int page = 1, int size = 20)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tags))
                {
                    return RedirectToAction("Index", new { q, page, size });
                }

                var tagNames = tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                  .Select(t => t.Trim())
                                  .ToList();

                var tagIds = await _tagService.GetTagIdsByNamesAsync(tagNames);
                
                var searchResults = await _searchService.SearchWithTagsAsync(q, tagIds, page, size);
                var viewModel = new SearchResultsViewModel
                {
                    Results = searchResults,
                    Query = q,
                    Page = page,
                    PageSize = size,
                    Categories = await _categoryService.GetActiveCategoriesAsync(),
                    Sources = await _sourceService.GetActiveSourcesAsync(),
                    PopularTags = await _tagService.GetPopularTagsAsync(20),
                    FilterOptions = CreateFilterOptions(),
                    SortOptions = CreateSortOptions(),
                    SelectedTags = tagNames,
                    SearchContext = $"tagged with {string.Join(", ", tagNames)}"
                };

                return View("Results", viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching with tags {Tags}", tags);
                return View("Error");
            }
        }

        // GET: /Search/Similar/{articleId}
        [HttpGet("Search/Similar/{articleId:int}")]
        public async Task<IActionResult> Similar(int articleId, int count = 10)
        {
            try
            {
                var similarArticles = await _searchService.FindSimilarArticlesAsync(articleId, count);
                return Json(similarArticles.Select(a => new
                {
                    id = a.Id,
                    title = a.Title,
                    summary = a.Summary,
                    author = a.Author,
                    publishedDate = a.PublishedDate,
                    url = Url.Action("Details", "Article", new { id = a.Id }),
                    category = a.Category.Name,
                    source = a.Source.Name,
                    imageUrl = a.ImageUrl
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding similar articles for {ArticleId}", articleId);
                return Json(new { error = "Failed to find similar articles" });
            }
        }

        #region Helper Methods

        private SearchFilters MapToSearchFilters(AdvancedSearchRequest request)
        {
            var filters = new SearchFilters();

            if (request.CategoryIds?.Any() == true)
                filters.CategoryId = request.CategoryIds.First(); // For now, take first category

            if (request.SourceIds?.Any() == true)
                filters.SourceId = request.SourceIds.First(); // For now, take first source

            if (request.TagIds?.Any() == true)
                filters.TagIds = request.TagIds.ToList();

            if (!string.IsNullOrEmpty(request.Author))
                filters.Author = request.Author;

            if (request.FromDate.HasValue)
                filters.FromDate = request.FromDate.Value;

            if (request.ToDate.HasValue)
                filters.ToDate = request.ToDate.Value;

            if (request.IsFeatured.HasValue)
                filters.IsFeatured = request.IsFeatured.Value;

            if (request.IsBreaking.HasValue)
                filters.IsBreaking = request.IsBreaking.Value;

            if (request.IsTrending.HasValue)
                filters.IsTrending = request.IsTrending.Value;

            if (request.HasImages.HasValue)
                filters.HasImages = request.HasImages.Value;

            if (request.HasVideo.HasValue)
                filters.HasVideo = request.HasVideo.Value;

            if (request.MinReadingTime.HasValue)
                filters.MinReadingTime = request.MinReadingTime.Value;

            if (request.MaxReadingTime.HasValue)
                filters.MaxReadingTime = request.MaxReadingTime.Value;

            if (request.MinQualityScore.HasValue)
                filters.MinQualityScore = request.MinQualityScore.Value;

            return filters;
        }

        private SearchSortOptions MapToSortOptions(AdvancedSearchRequest request)
        {
            return new SearchSortOptions
            {
                SortBy = request.SortBy ?? "Relevance",
                SortDescending = request.SortDescending ?? true,
                BoostFeatured = request.BoostFeatured ?? true,
                BoostRecent = request.BoostRecent ?? true
            };
        }

        private List<SearchFilterOption> CreateFilterOptions()
        {
            return new List<SearchFilterOption>
            {
                new() { Label = "Featured Content", Value = "featured", FilterType = "checkbox" },
                new() { Label = "Breaking News", Value = "breaking", FilterType = "checkbox" },
                new() { Label = "Trending", Value = "trending", FilterType = "checkbox" },
                new() { Label = "Has Images", Value = "hasImages", FilterType = "checkbox" },
                new() { Label = "Has Video", Value = "hasVideo", FilterType = "checkbox" },
                new() { Label = "Reading Time", Value = "readingTime", FilterType = "range" },
                new() { Label = "Quality Score", Value = "qualityScore", FilterType = "range" },
                new() { Label = "Date Range", Value = "dateRange", FilterType = "daterange" }
            };
        }

        private List<SearchSortOption> CreateSortOptions()
        {
            return new List<SearchSortOption>
            {
                new() { Label = "Relevance", Value = "relevance", IsDefault = true },
                new() { Label = "Date (Newest First)", Value = "date_desc" },
                new() { Label = "Date (Oldest First)", Value = "date_asc" },
                new() { Label = "Title (A-Z)", Value = "title_asc" },
                new() { Label = "Title (Z-A)", Value = "title_desc" },
                new() { Label = "Author (A-Z)", Value = "author_asc" },
                new() { Label = "Author (Z-A)", Value = "author_desc" },
                new() { Label = "Most Popular", Value = "views_desc" }
            };
        }

        private List<DateRangeOption> CreateDateRangeOptions()
        {
            return new List<DateRangeOption>
            {
                new() { Label = "Last 24 Hours", Value = "1day", Days = 1 },
                new() { Label = "Last 3 Days", Value = "3days", Days = 3 },
                new() { Label = "Last Week", Value = "1week", Days = 7 },
                new() { Label = "Last Month", Value = "1month", Days = 30 },
                new() { Label = "Last 3 Months", Value = "3months", Days = 90 },
                new() { Label = "Last 6 Months", Value = "6months", Days = 180 },
                new() { Label = "Last Year", Value = "1year", Days = 365 },
                new() { Label = "Custom Range", Value = "custom", Days = null }
            };
        }

        private async Task<List<string>> GetPopularAuthorsAsync()
        {
            // Get popular authors from search service or implement separate method
            var suggestions = await _searchService.GetAuthorFacetsAsync("", null, 20);
            return suggestions.Keys.ToList();
        }

        private List<string> GetRecentSearches()
        {
            var recentSearches = HttpContext.Session.GetString("RecentSearches");
            if (string.IsNullOrEmpty(recentSearches))
                return new List<string>();

            return System.Text.Json.JsonSerializer.Deserialize<List<string>>(recentSearches) ?? new List<string>();
        }

        private void StoreRecentSearch(string query)
        {
            if (string.IsNullOrWhiteSpace(query))
                return;

            var recentSearches = GetRecentSearches();
            
            // Remove if already exists to avoid duplicates
            recentSearches.RemoveAll(s => s.Equals(query, StringComparison.OrdinalIgnoreCase));
            
            // Add to front
            recentSearches.Insert(0, query);
            
            // Keep only last 10 searches
            if (recentSearches.Count > 10)
                recentSearches = recentSearches.Take(10).ToList();

            var json = System.Text.Json.JsonSerializer.Serialize(recentSearches);
            HttpContext.Session.SetString("RecentSearches", json);
        }

        #endregion
    }

    #region View Models

    public class SearchIndexViewModel
    {
        public IEnumerable<SearchSuggestion> PopularSearches { get; set; } = new List<SearchSuggestion>();
        public IEnumerable<string> TrendingQueries { get; set; } = new List<string>();
        public IEnumerable<Category> Categories { get; set; } = new List<Category>();
        public List<string> RecentSearches { get; set; } = new List<string>();
    }

    public class SearchResultsViewModel
    {
        public SearchResultsPage Results { get; set; } = null!;
        public string Query { get; set; } = string.Empty;
        public int Page { get; set; }
        public int PageSize { get; set; }
        public IEnumerable<Category> Categories { get; set; } = new List<Category>();
        public IEnumerable<Source> Sources { get; set; } = new List<Source>();
        public IEnumerable<Tag> PopularTags { get; set; } = new List<Tag>();
        public List<SearchFilterOption> FilterOptions { get; set; } = new List<SearchFilterOption>();
        public List<SearchSortOption> SortOptions { get; set; } = new List<SearchSortOption>();
        public bool IsAdvancedSearch { get; set; }
        public SearchFilters? AdvancedFilters { get; set; }
        public Category? SelectedCategory { get; set; }
        public Source? SelectedSource { get; set; }
        public string? SelectedAuthor { get; set; }
        public List<string>? SelectedTags { get; set; }
        public string? SearchContext { get; set; }
    }

    public class AdvancedSearchViewModel
    {
        public IEnumerable<Category> Categories { get; set; } = new List<Category>();
        public IEnumerable<Source> Sources { get; set; } = new List<Source>();
        public IEnumerable<Tag> PopularTags { get; set; } = new List<Tag>();
        public List<string> Authors { get; set; } = new List<string>();
        public List<SearchFilterOption> FilterOptions { get; set; } = new List<SearchFilterOption>();
        public List<SearchSortOption> SortOptions { get; set; } = new List<SearchSortOption>();
        public List<DateRangeOption> DateRangeOptions { get; set; } = new List<DateRangeOption>();
        public AdvancedSearchRequest? SearchRequest { get; set; }
    }

    public class AdvancedSearchRequest
    {
        public string? Query { get; set; }
        public List<int>? CategoryIds { get; set; }
        public List<int>? SourceIds { get; set; }
        public List<int>? TagIds { get; set; }
        public string? Author { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? IsFeatured { get; set; }
        public bool? IsBreaking { get; set; }
        public bool? IsTrending { get; set; }
        public bool? HasImages { get; set; }
        public bool? HasVideo { get; set; }
        public int? MinReadingTime { get; set; }
        public int? MaxReadingTime { get; set; }
        public decimal? MinQualityScore { get; set; }
        public string? SortBy { get; set; }
        public bool? SortDescending { get; set; }
        public bool? BoostFeatured { get; set; }
        public bool? BoostRecent { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class SearchFilterOption
    {
        public string Label { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string FilterType { get; set; } = string.Empty; // checkbox, range, daterange, select
        public bool IsSelected { get; set; }
        public Dictionary<string, object> Options { get; set; } = new();
    }

    public class SearchSortOption
    {
        public string Label { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public bool IsDefault { get; set; }
    }

    public class DateRangeOption
    {
        public string Label { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public int? Days { get; set; }
    }

    #endregion
}