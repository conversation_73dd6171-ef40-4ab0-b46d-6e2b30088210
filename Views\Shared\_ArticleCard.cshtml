@model Article
@{
    var viewMode = ViewData["ViewMode"]?.ToString() ?? "grid";
    var categoryColor = ViewData["CategoryColor"]?.ToString() ?? Model.Category?.Color ?? "#00d4ff";
    var showCategoryInfo = ViewData["ShowCategoryInfo"] != null ? (bool)ViewData["ShowCategoryInfo"] : true;
    var cardClass = viewMode switch {
        "list" => "article-card article-card-list",
        "compact" => "article-card article-card-compact",
        _ => "article-card"
    };
    var colClass = viewMode switch {
        "grid" => "col-12 col-md-6 col-lg-4",
        "list" => "col-12",
        "compact" => "col-12",
        _ => "col-12 col-md-6 col-lg-4"
    };
}

<div class="@colClass mb-4">
    <article class="@cardClass h-100" data-article-id="@Model.Id">
        @if (viewMode == "list")
        {
            <!-- List View Layout -->
            <div class="row g-0 h-100">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <div class="col-md-3">
                        <div class="article-image-container">
                            <img src="@Model.ImageUrl" 
                                 alt="@(Model.ImageAlt ?? Model.Title)" 
                                 class="card-img h-100" 
                                 loading="lazy"
                                 onerror="this.style.display='none'">
                            @if (Model.IsBreaking)
                            {
                                <div class="breaking-badge">
                                    <i class="fas fa-bolt"></i>
                                </div>
                            }
                        </div>
                    </div>
                }
                <div class="@(string.IsNullOrEmpty(Model.ImageUrl) ? "col-12" : "col-md-9")">
                    <div class="card-body d-flex flex-column h-100">
                        <!-- Article Header -->
                        <div class="article-header mb-2">
                            @if (showCategoryInfo && Model.Category != null)
                            {
                                <div class="article-category-info mb-2">
                                    <a href="/@Model.Category.Slug" class="category-link text-decoration-none">
                                        <i class="@(Model.Category.Icon ?? "fas fa-layer-group") me-1" 
                                           style="color: @(Model.Category.Color ?? categoryColor);"></i>
                                        <span style="color: @(Model.Category.Color ?? categoryColor);">@Model.Category.Name</span>
                                    </a>
                                </div>
                            }
                            <h5 class="card-title mb-2">
                                <a href="/article/@Model.Slug" class="text-decoration-none text-primary-brand article-title-link">
                                    @Model.Title
                                </a>
                            </h5>
                        </div>

                        <!-- Article Content -->
                        <div class="article-content flex-grow-1">
                            @if (!string.IsNullOrEmpty(Model.Summary))
                            {
                                <p class="card-text">@Model.Summary</p>
                            }
                        </div>

                        <!-- Article Footer -->
                        <div class="article-meta mt-auto">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="article-info">
                                    <small class="text-muted d-flex align-items-center">
                                        <i class="fas fa-clock me-1"></i>
                                        <time datetime="@Model.PublishedDate.ToString("yyyy-MM-dd")" title="@Model.PublishedDate.ToString("MMM dd, yyyy h:mm tt")">
                                            @GetTimeAgo(Model.PublishedDate)
                                        </time>
                                        @if (!string.IsNullOrEmpty(Model.Author))
                                        {
                                            <span class="mx-2">•</span>
                                            <span>@Model.Author</span>
                                        }
                                    </small>
                                </div>
                                <div class="article-badges">
                                    @if (Model.IsFeatured)
                                    {
                                        <span class="badge featured-badge me-1" style="background-color: @(categoryColor)20; color: @categoryColor;">
                                            <i class="fas fa-star me-1"></i>Featured
                                        </span>
                                    }
                                    @if (Model.IsBreaking)
                                    {
                                        <span class="badge breaking-badge me-1" style="background-color: @(categoryColor)20; color: @categoryColor;">
                                            <i class="fas fa-bolt me-1"></i>Breaking
                                        </span>
                                    }
                                    @if (Model.IsTrending)
                                    {
                                        <span class="badge trending-badge" style="background-color: @(categoryColor)20; color: @categoryColor;">
                                            <i class="fas fa-fire me-1"></i>Trending
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        else if (viewMode == "compact")
        {
            <!-- Compact View Layout -->
            <div class="d-flex align-items-start">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <div class="article-image-thumbnail me-3 flex-shrink-0">
                        <img src="@Model.ImageUrl" 
                             alt="@(Model.ImageAlt ?? Model.Title)" 
                             class="rounded" 
                             width="80" 
                             height="60"
                             loading="lazy"
                             onerror="this.style.display='none'">
                    </div>
                }
                <div class="article-content flex-grow-1">
                    <!-- Article Header -->
                    <div class="article-header mb-1">
                        @if (showCategoryInfo && Model.Category != null)
                        {
                            <div class="article-category-info mb-1">
                                <a href="/@Model.Category.Slug" class="category-link text-decoration-none">
                                    <small>
                                        <i class="@(Model.Category.Icon ?? "fas fa-layer-group") me-1" 
                                           style="color: @(Model.Category.Color ?? categoryColor);"></i>
                                        <span style="color: @(Model.Category.Color ?? categoryColor);">@Model.Category.Name</span>
                                    </small>
                                </a>
                            </div>
                        }
                        <h6 class="card-title mb-1">
                            <a href="/article/@Model.Slug" class="text-decoration-none text-primary-brand article-title-link">
                                @Model.Title
                            </a>
                        </h6>
                    </div>

                    <!-- Article Meta -->
                    <div class="article-meta">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                <time datetime="@Model.PublishedDate.ToString("yyyy-MM-dd")" title="@Model.PublishedDate.ToString("MMM dd, yyyy h:mm tt")">
                                    @GetTimeAgo(Model.PublishedDate)
                                </time>
                            </small>
                            <div class="article-badges">
                                @if (Model.IsBreaking)
                                {
                                    <i class="fas fa-bolt text-danger" title="Breaking News"></i>
                                }
                                @if (Model.IsFeatured)
                                {
                                    <i class="fas fa-star ms-1" style="color: @categoryColor;" title="Featured"></i>
                                }
                                @if (Model.IsTrending)
                                {
                                    <i class="fas fa-fire ms-1" style="color: @categoryColor;" title="Trending"></i>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <!-- Grid View Layout (Default) -->
            @if (!string.IsNullOrEmpty(Model.ImageUrl))
            {
                <div class="article-image-container">
                    <img src="@Model.ImageUrl" 
                         alt="@(Model.ImageAlt ?? Model.Title)" 
                         class="card-img-top" 
                         loading="lazy"
                         onerror="this.style.display='none'">
                    @if (Model.IsBreaking)
                    {
                        <div class="breaking-badge">
                            <i class="fas fa-bolt"></i>
                        </div>
                    }
                    @if (Model.IsFeatured)
                    {
                        <div class="featured-badge">
                            <i class="fas fa-star"></i>
                        </div>
                    }
                </div>
            }
            
            <div class="card-body d-flex flex-column">
                <!-- Article Header -->
                <div class="article-header mb-2">
                    @if (showCategoryInfo && Model.Category != null)
                    {
                        <div class="article-category-info mb-2">
                            <a href="/@Model.Category.Slug" class="category-link text-decoration-none">
                                <small>
                                    <i class="@(Model.Category.Icon ?? "fas fa-layer-group") me-1" 
                                       style="color: @(Model.Category.Color ?? categoryColor);"></i>
                                    <span style="color: @(Model.Category.Color ?? categoryColor);">@Model.Category.Name</span>
                                </small>
                            </a>
                        </div>
                    }
                    <h5 class="card-title">
                        <a href="/article/@Model.Slug" class="text-decoration-none text-primary-brand article-title-link">
                            @Model.Title
                        </a>
                    </h5>
                </div>

                <!-- Article Content -->
                <div class="article-content flex-grow-1 mb-3">
                    @if (!string.IsNullOrEmpty(Model.Summary))
                    {
                        <p class="card-text">@Model.Summary</p>
                    }
                </div>

                <!-- Article Footer -->
                <div class="article-meta mt-auto">
                    <div class="d-flex justify-content-between align-items-end">
                        <div class="article-info">
                            <small class="text-muted d-block mb-1">
                                <i class="fas fa-clock me-1"></i>
                                <time datetime="@Model.PublishedDate.ToString("yyyy-MM-dd")" title="@Model.PublishedDate.ToString("MMM dd, yyyy h:mm tt")">
                                    @GetTimeAgo(Model.PublishedDate)
                                </time>
                            </small>
                            @if (!string.IsNullOrEmpty(Model.Author))
                            {
                                <small class="text-muted d-block">
                                    <i class="fas fa-user me-1"></i>@Model.Author
                                </small>
                            }
                            @if (Model.Source != null)
                            {
                                <small class="text-muted d-block">
                                    <i class="fas fa-external-link-alt me-1"></i>@Model.Source.Name
                                </small>
                            }
                        </div>
                        <div class="article-badges">
                            @if (Model.IsFeatured)
                            {
                                <span class="badge featured-badge mb-1 d-block" style="background-color: @(categoryColor)20; color: @categoryColor;">
                                    <i class="fas fa-star me-1"></i>Featured
                                </span>
                            }
                            @if (Model.IsTrending)
                            {
                                <span class="badge trending-badge" style="background-color: @(categoryColor)20; color: @categoryColor;">
                                    <i class="fas fa-fire me-1"></i>Trending
                                </span>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Article Actions (visible on hover) -->
        <div class="article-actions">
            <button class="btn btn-sm btn-outline-secondary" onclick="shareArticle('@Model.Slug', '@Model.Title')" title="Share Article">
                <i class="fas fa-share-alt"></i>
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="bookmarkArticle(@Model.Id)" title="Bookmark Article">
                <i class="fas fa-bookmark"></i>
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="openArticleMenu(@Model.Id)" title="More Options">
                <i class="fas fa-ellipsis-v"></i>
            </button>
        </div>

        <!-- Reading Progress Indicator (for longer articles) -->
        @if (Model.Metadata?.ReadingTimeMinutes > 5)
        {
            <div class="reading-time-indicator">
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i>@(Model.Metadata.ReadingTimeMinutes) min read
                </small>
            </div>
        }
    </article>
</div>

@functions {
    private static string GetTimeAgo(DateTime publishedDate)
    {
        var timeSpan = DateTime.UtcNow - publishedDate;

        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes}m ago";
        
        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours}h ago";
        
        if (timeSpan.TotalDays < 7)
            return $"{(int)timeSpan.TotalDays}d ago";
        
        if (timeSpan.TotalDays < 30)
            return $"{(int)(timeSpan.TotalDays / 7)}w ago";
        
        if (timeSpan.TotalDays < 365)
            return $"{(int)(timeSpan.TotalDays / 30)}mo ago";
        
        return $"{(int)(timeSpan.TotalDays / 365)}y ago";
    }
}

<style>
    /* Article Card Styles */
    .article-card {
        background: var(--ai-dark-secondary);
        border: 1px solid var(--ai-dark-tertiary);
        border-radius: var(--ai-radius-lg);
        overflow: hidden;
        transition: var(--ai-transition-normal);
        position: relative;
    }

    .article-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--ai-shadow-lg);
        border-color: var(--ai-brand-primary);
    }

    .article-image-container {
        position: relative;
        overflow: hidden;
    }

    .article-card .card-img-top {
        height: 200px;
        object-fit: cover;
        transition: var(--ai-transition-normal);
    }

    .article-card:hover .card-img-top {
        transform: scale(1.05);
    }

    .article-card-list .card-img {
        width: 200px;
        min-height: 150px;
        object-fit: cover;
        transition: var(--ai-transition-normal);
    }

    .article-card-compact {
        padding: 1rem;
        border-radius: var(--ai-radius-md);
        transition: var(--ai-transition-fast);
    }

    .article-card-compact:hover {
        background: var(--ai-dark-hover);
        transform: translateX(4px);
    }

    .article-image-thumbnail img {
        object-fit: cover;
        border-radius: var(--ai-radius-sm);
    }

    /* Badges and Indicators */
    .breaking-badge, .featured-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(255, 68, 68, 0.9);
        color: white;
        padding: 4px 8px;
        border-radius: var(--ai-radius-sm);
        font-size: 0.75rem;
        font-weight: 600;
        backdrop-filter: blur(5px);
        z-index: 2;
    }

    .featured-badge {
        background: rgba(255, 193, 7, 0.9);
        top: 10px;
        left: 10px;
        right: auto;
    }

    .badge.featured-badge,
    .badge.breaking-badge,
    .badge.trending-badge {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        border-radius: var(--ai-radius-sm);
    }

    /* Article Content */
    .article-title-link {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
        transition: var(--ai-transition-fast);
    }

    .article-title-link:hover {
        color: var(--ai-brand-primary) !important;
    }

    .article-card .card-text {
        color: var(--ai-text-secondary);
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.5;
        margin-bottom: 0;
    }

    .article-card-compact .article-content {
        max-height: none;
    }

    /* Category Links */
    .category-link {
        font-size: 0.875rem;
        font-weight: 500;
        transition: var(--ai-transition-fast);
    }

    .category-link:hover {
        opacity: 0.8;
    }

    /* Article Actions */
    .article-actions {
        position: absolute;
        top: 10px;
        left: 10px;
        display: flex;
        gap: 0.5rem;
        opacity: 0;
        transition: var(--ai-transition-normal);
        z-index: 3;
    }

    .article-card:hover .article-actions {
        opacity: 1;
    }

    .article-actions .btn {
        background: rgba(0, 0, 0, 0.7);
        border: none;
        color: white;
        padding: 0.25rem 0.5rem;
        backdrop-filter: blur(5px);
    }

    .article-actions .btn:hover {
        background: rgba(0, 0, 0, 0.9);
        color: var(--ai-brand-primary);
    }

    /* Reading Time Indicator */
    .reading-time-indicator {
        position: absolute;
        bottom: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.7);
        padding: 0.25rem 0.5rem;
        border-radius: var(--ai-radius-sm);
        backdrop-filter: blur(5px);
        opacity: 0;
        transition: var(--ai-transition-normal);
    }

    .article-card:hover .reading-time-indicator {
        opacity: 1;
    }

    /* Article Meta */
    .article-meta {
        padding-top: 0.75rem;
        border-top: 1px solid var(--ai-dark-tertiary);
        margin-top: auto;
    }

    .article-card-compact .article-meta {
        border-top: none;
        padding-top: 0;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .article-card-list {
            flex-direction: column;
        }

        .article-card-list .card-img {
            width: 100%;
            height: 150px;
        }

        .article-actions {
            position: relative;
            top: auto;
            left: auto;
            opacity: 1;
            justify-content: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--ai-dark-tertiary);
        }

        .article-card .card-img-top {
            height: 150px;
        }

        .breaking-badge, .featured-badge {
            font-size: 0.7rem;
            padding: 3px 6px;
        }
    }

    /* Print Styles */
    @media print {
        .article-actions,
        .reading-time-indicator {
            display: none !important;
        }
        
        .article-card {
            border: 1px solid #ccc !important;
            box-shadow: none !important;
            break-inside: avoid;
        }
    }
</style>

<script>
    // Article card functionality
    function shareArticle(slug, title) {
        if (navigator.share) {
            navigator.share({
                title: title,
                url: window.location.origin + '/article/' + slug
            });
        } else {
            // Fallback to clipboard
            navigator.clipboard.writeText(window.location.origin + '/article/' + slug)
                .then(() => {
                    // You could show a toast notification here
                    console.log('Article URL copied to clipboard');
                });
        }
    }

    function bookmarkArticle(articleId) {
        // Implement bookmark functionality
        const bookmarks = JSON.parse(localStorage.getItem('bookmarkedArticles') || '[]');
        
        if (bookmarks.includes(articleId)) {
            // Remove bookmark
            const index = bookmarks.indexOf(articleId);
            bookmarks.splice(index, 1);
            $(event.target).removeClass('fas').addClass('far');
        } else {
            // Add bookmark
            bookmarks.push(articleId);
            $(event.target).removeClass('far').addClass('fas');
        }
        
        localStorage.setItem('bookmarkedArticles', JSON.stringify(bookmarks));
    }

    function openArticleMenu(articleId) {
        // Implement article context menu
        console.log('Open menu for article:', articleId);
    }

    // Initialize bookmarked state on page load
    $(document).ready(function() {
        const bookmarks = JSON.parse(localStorage.getItem('bookmarkedArticles') || '[]');
        bookmarks.forEach(articleId => {
            $(`[data-article-id="${articleId}"] .article-actions .fa-bookmark`)
                .removeClass('far').addClass('fas');
        });
    });
</script>