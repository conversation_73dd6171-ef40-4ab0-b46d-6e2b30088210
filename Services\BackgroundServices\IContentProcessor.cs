using NewsSite.Models;

namespace NewsSite.Services.BackgroundServices
{
    public interface IContentProcessor
    {
        /// <summary>
        /// Processes and validates an article, including duplicate detection
        /// </summary>
        Task<ProcessedArticle?> ProcessArticleAsync(Article article);

        /// <summary>
        /// Validates article content and metadata
        /// </summary>
        Task<ValidationResult> ValidateArticleAsync(Article article);

        /// <summary>
        /// Sanitizes article content for security
        /// </summary>
        Task<Article> SanitizeArticleAsync(Article article);

        /// <summary>
        /// Extracts and assigns appropriate tags to an article
        /// </summary>
        Task<List<Tag>> ExtractTagsAsync(Article article);

        /// <summary>
        /// Determines the appropriate category for an article
        /// </summary>
        Task<Category?> DetermineCategoryAsync(Article article);
    }

    public class ProcessedArticle
    {
        public Article Article { get; set; } = null!;
        public bool IsNew { get; set; }
        public bool IsDuplicate { get; set; }
        public ValidationResult ValidationResult { get; set; } = null!;
        public List<Tag> ExtractedTags { get; set; } = new();
        public Category? AssignedCategory { get; set; }
    }

    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public ContentQualityScore QualityScore { get; set; } = new();
    }

    public class ContentQualityScore
    {
        public int Overall { get; set; }
        public int ContentLength { get; set; }
        public int ReadabilityScore { get; set; }
        public int RelevanceScore { get; set; }
        public bool HasImages { get; set; }
        public bool HasValidLinks { get; set; }
    }
}