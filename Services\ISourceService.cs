using NewsSite.Models;

namespace NewsSite.Services
{
    public class SourceHealthStatus
    {
        public Source Source { get; set; } = null!;
        public bool IsHealthy { get; set; }
        public int ConsecutiveFailures { get; set; }
        public DateTime? LastSuccessfulSync { get; set; }
        public DateTime? LastSyncAttempt { get; set; }
        public string? LastErrorMessage { get; set; }
        public TimeSpan? TimeSinceLastSync { get; set; }
        public bool RequiresAttention { get; set; }
    }

    public class SourceWithStats
    {
        public Source Source { get; set; } = null!;
        public int TotalArticles { get; set; }
        public int PublishedArticles { get; set; }
        public int ArticlesThisMonth { get; set; }
        public DateTime? LastArticleDate { get; set; }
        public SourceHealthStatus HealthStatus { get; set; } = null!;
    }

    public interface ISourceService
    {
        // Basic CRUD operations
        Task<Source?> GetByIdAsync(int id);
        Task<Source?> GetSourceByIdAsync(int id);
        Task<Source?> GetSourceBySlugAsync(string slug);
        Task<Source?> GetByNameAsync(string name);
        Task<IEnumerable<Source>> GetAllSourcesAsync();
        Task<IEnumerable<Source>> GetActiveSourcesAsync();
        Task<IEnumerable<Source>> GetSourcesByTypeAsync(SourceType sourceType);
        Task<Source> CreateSourceAsync(Source source);
        Task<Source> UpdateSourceAsync(Source source);
        Task<Source> CreateAsync(Source source);
        Task<Source> UpdateAsync(Source source);
        Task<bool> DeleteSourceAsync(int id);
        Task<IEnumerable<Source>> GetPrioritySourcesAsync();

        // Source configuration management
        Task<SourceConfiguration?> GetSourceConfigurationAsync(int sourceId);
        Task<SourceConfiguration> CreateOrUpdateConfigurationAsync(SourceConfiguration configuration);
        Task<bool> DeleteSourceConfigurationAsync(int sourceId);

        // Source health monitoring
        Task<SourceHealthStatus> GetSourceHealthStatusAsync(int sourceId);
        Task<IEnumerable<SourceHealthStatus>> GetAllSourceHealthStatusAsync();
        Task<IEnumerable<Source>> GetUnhealthySourcesAsync();
        Task<IEnumerable<Source>> GetSourcesRequiringAttentionAsync();

        // Sync tracking and management
        Task<bool> UpdateLastSyncDateAsync(int sourceId, DateTime syncDate, bool wasSuccessful, string? errorMessage = null);
        Task<bool> IncrementFailureCountAsync(int sourceId, string errorMessage);
        Task<bool> ResetFailureCountAsync(int sourceId);
        Task<IEnumerable<Source>> GetSourcesDueForSyncAsync();
        Task<IEnumerable<Source>> GetSourcesByPriorityAsync(int minPriority = 1);

        // Source activation and management
        Task<bool> SetSourceActiveStatusAsync(int sourceId, bool isActive);
        Task<bool> UpdateSourceRateLimitAsync(int sourceId, int maxRequestsPerHour);
        Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> sourceIds, bool isActive);

        // Content filtering and configuration
        Task<bool> UpdateContentFilteringAsync(int sourceId, bool enableFiltering, string? keywords = null);
        Task<string?> GetContentFilterKeywordsAsync(int sourceId);
        Task<bool> RequiresAuthenticationAsync(int sourceId);

        // Source statistics and analytics
        Task<SourceWithStats?> GetSourceWithStatsAsync(int sourceId);
        Task<IEnumerable<SourceWithStats>> GetSourcesWithStatsAsync();
        Task<int> GetArticleCountBySourceAsync(int sourceId, ArticleStatus? status = null);
        Task<Dictionary<int, int>> GetArticleCountsForAllSourcesAsync(ArticleStatus? status = null);
        Task<IEnumerable<Source>> GetTopSourcesByArticleCountAsync(int count = 10, int days = 30);

        // Source articles management
        Task<PaginatedResult<Article>> GetSourceArticlesAsync(int sourceId, int page = 1, int pageSize = 20, ArticleStatus? status = null);
        Task<IEnumerable<Article>> GetLatestSourceArticlesAsync(int sourceId, int count = 10);
        Task<DateTime?> GetLastArticleDateAsync(int sourceId);

        // Validation and utility
        Task<bool> ValidateSourceAsync(Source source);
        Task<bool> IsSourceNameUniqueAsync(string name, int? excludeId = null);
        Task<bool> IsSourceUrlUniqueAsync(string url, int? excludeId = null);
        Task<bool> CanDeleteSourceAsync(int sourceId);

        // Search and filtering
        Task<IEnumerable<Source>> SearchSourcesAsync(string query);
        Task<IEnumerable<Source>> GetSourcesRequiringSyncAsync(int maxHoursSinceLastSync = 24);
        Task<IEnumerable<Source>> GetSourcesWithConsecutiveFailuresAsync(int minFailures = 3);

        // Error handling and notifications
        Task<bool> ShouldNotifyForSourceErrorAsync(int sourceId);
        Task<IEnumerable<string>> GetNotificationEmailsForSourceAsync(int sourceId);
        Task<bool> UpdateSourceErrorStatusAsync(int sourceId, string errorMessage, bool shouldNotify = true);

        // Bulk operations
        Task<int> BulkUpdateSyncSettingsAsync(IEnumerable<int> sourceIds, int updateIntervalMinutes, int maxArticlesPerSync);
        Task<int> BulkResetFailureCountsAsync(IEnumerable<int> sourceIds);
    }
}