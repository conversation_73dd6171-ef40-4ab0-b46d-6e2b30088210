using NewsSite.Models;

namespace NewsSite.Services
{
    public class SearchFilters
    {
        public int? CategoryId { get; set; }
        public int? SourceId { get; set; }
        public List<int>? TagIds { get; set; }
        public ArticleStatus? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Author { get; set; }
        public bool? IsFeatured { get; set; }
        public bool? IsBreaking { get; set; }
        public bool? IsTrending { get; set; }
        public int? MinReadingTime { get; set; }
        public int? MaxReadingTime { get; set; }
        public bool? HasImages { get; set; }
        public bool? HasVideo { get; set; }
        public decimal? MinQualityScore { get; set; }
    }

    public class SearchSortOptions
    {
        public string SortBy { get; set; } = "Relevance";
        public bool SortDescending { get; set; } = true;
        public bool BoostFeatured { get; set; } = true;
        public bool BoostRecent { get; set; } = true;
    }

    public class SearchResult
    {
        public Article Article { get; set; } = null!;
        public decimal RelevanceScore { get; set; }
        public List<string> MatchedFields { get; set; } = new();
        public List<string> HighlightedSnippets { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class SearchResultsPage
    {
        public IEnumerable<SearchResult> Results { get; set; } = new List<SearchResult>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => Page > 1;
        public bool HasNextPage => Page < TotalPages;
        public string Query { get; set; } = string.Empty;
        public SearchFilters? Filters { get; set; }
        public SearchSortOptions? SortOptions { get; set; }
        public TimeSpan SearchDuration { get; set; }
        public Dictionary<string, object> Facets { get; set; } = new();
    }

    public class SearchSuggestion
    {
        public string Text { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // "query", "category", "tag", "source", "author"
        public int Count { get; set; }
        public decimal Relevance { get; set; }
    }

    public class SearchAnalytics
    {
        public string Query { get; set; } = string.Empty;
        public int SearchCount { get; set; }
        public int ResultCount { get; set; }
        public DateTime FirstSearched { get; set; }
        public DateTime LastSearched { get; set; }
        public TimeSpan AverageSearchTime { get; set; }
        public int ClickThroughCount { get; set; }
        public decimal ClickThroughRate { get; set; }
    }

    public interface ISearchService
    {
        // Core search functionality
        Task<SearchResultsPage> SearchAsync(string query, int page = 1, int pageSize = 20, SearchFilters? filters = null, SearchSortOptions? sortOptions = null);
        Task<SearchResultsPage> AdvancedSearchAsync(string query, SearchFilters filters, SearchSortOptions sortOptions, int page = 1, int pageSize = 20);
        Task<IEnumerable<Article>> QuickSearchAsync(string query, int maxResults = 10);

        // Search suggestions and auto-complete
        Task<IEnumerable<SearchSuggestion>> GetSearchSuggestionsAsync(string partialQuery, int maxSuggestions = 10);
        Task<IEnumerable<string>> GetQueryAutoCompleteAsync(string partialQuery, int maxSuggestions = 8);
        Task<IEnumerable<SearchSuggestion>> GetPopularSearchesAsync(int count = 10, int days = 30);
        Task<IEnumerable<SearchSuggestion>> GetRelatedSearchesAsync(string query, int count = 5);

        // Faceted search
        Task<Dictionary<string, object>> GetSearchFacetsAsync(string query, SearchFilters? filters = null);
        Task<Dictionary<string, int>> GetCategoryFacetsAsync(string query, SearchFilters? filters = null);
        Task<Dictionary<string, int>> GetSourceFacetsAsync(string query, SearchFilters? filters = null);
        Task<Dictionary<string, int>> GetTagFacetsAsync(string query, SearchFilters? filters = null, int maxTags = 20);
        Task<Dictionary<string, int>> GetAuthorFacetsAsync(string query, SearchFilters? filters = null, int maxAuthors = 15);

        // Search within specific contexts
        Task<SearchResultsPage> SearchInCategoryAsync(string query, int categoryId, int page = 1, int pageSize = 20, SearchSortOptions? sortOptions = null);
        Task<SearchResultsPage> SearchInSourceAsync(string query, int sourceId, int page = 1, int pageSize = 20, SearchSortOptions? sortOptions = null);
        Task<SearchResultsPage> SearchWithTagsAsync(string query, IEnumerable<int> tagIds, int page = 1, int pageSize = 20, SearchSortOptions? sortOptions = null);
        Task<SearchResultsPage> SearchByAuthorAsync(string query, string author, int page = 1, int pageSize = 20, SearchSortOptions? sortOptions = null);

        // Similar and related content
        Task<IEnumerable<Article>> FindSimilarArticlesAsync(int articleId, int maxResults = 10);
        Task<IEnumerable<Article>> FindSimilarByContentAsync(string title, string content, int maxResults = 10);
        Task<IEnumerable<Article>> FindSimilarByTagsAsync(IEnumerable<int> tagIds, int excludeArticleId = 0, int maxResults = 10);

        // Trending and popular searches
        Task<IEnumerable<string>> GetTrendingQueriesAsync(int count = 10, int days = 7);
        Task<IEnumerable<SearchSuggestion>> GetPopularQueriesByPeriodAsync(DateTime fromDate, DateTime toDate, int count = 10);
        Task<Dictionary<string, int>> GetSearchVolumeByDateAsync(DateTime fromDate, DateTime toDate);

        // Search analytics and tracking
        Task<bool> RecordSearchAsync(string query, int resultCount, TimeSpan searchDuration, SearchFilters? filters = null);
        Task<bool> RecordSearchClickAsync(string query, int articleId);
        Task<SearchAnalytics?> GetQueryAnalyticsAsync(string query);
        Task<IEnumerable<SearchAnalytics>> GetTopQueriesAnalyticsAsync(int count = 20, int days = 30);

        // Search index management
        Task<bool> ReindexArticleAsync(int articleId);
        Task<bool> RemoveFromIndexAsync(int articleId);
        Task<int> ReindexAllArticlesAsync();
        Task<bool> OptimizeSearchIndexAsync();

        // Search quality and relevance
        Task<decimal> CalculateSearchRelevanceAsync(string query, Article article);
        Task<IEnumerable<string>> ExtractSearchTermsAsync(string query);
        Task<IEnumerable<string>> GetQueryExpansionsAsync(string query);
        Task<bool> UpdateSearchRankingAsync(string query, int articleId, int newRank);

        // Export and reporting
        Task<Dictionary<string, object>> GetSearchStatsAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<SearchAnalytics>> ExportSearchAnalyticsAsync(DateTime fromDate, DateTime toDate);
        Task<Dictionary<string, object>> GetSearchPerformanceReportAsync(int days = 30);

        // Search configuration
        Task<bool> AddSearchStopWordAsync(string word);
        Task<bool> RemoveSearchStopWordAsync(string word);
        Task<IEnumerable<string>> GetSearchStopWordsAsync();
        Task<bool> UpdateSearchWeightsAsync(Dictionary<string, decimal> fieldWeights);

        // Cleanup and maintenance
        Task<int> CleanupOldSearchLogsAsync(int daysToKeep = 90);
        Task<int> ConsolidateSearchStatsAsync();
        Task<bool> ValidateSearchIndexAsync();
    }
}