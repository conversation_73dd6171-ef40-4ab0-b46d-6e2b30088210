namespace NewsSite.Services.Utilities
{
    public interface IHttpService
    {
        /// <summary>
        /// Performs HTTP GET request with retry logic and rate limiting
        /// </summary>
        Task<string> GetStringAsync(string url, Dictionary<string, string>? headers = null);

        /// <summary>
        /// Performs HTTP GET request and returns byte array
        /// </summary>
        Task<byte[]> GetByteArrayAsync(string url, Dictionary<string, string>? headers = null);

        /// <summary>
        /// Performs HTTP POST request with retry logic
        /// </summary>
        Task<string> PostAsync(string url, string content, Dictionary<string, string>? headers = null);

        /// <summary>
        /// Performs HTTP POST request with JSON content
        /// </summary>
        Task<string> PostJsonAsync(string url, object data, Dictionary<string, string>? headers = null);

        /// <summary>
        /// Checks if a URL is accessible
        /// </summary>
        Task<bool> IsUrlAccessibleAsync(string url);

        /// <summary>
        /// Gets HTTP response headers for a URL
        /// </summary>
        Task<Dictionary<string, string>> GetResponseHeadersAsync(string url);

        /// <summary>
        /// Downloads content with progress reporting
        /// </summary>
        Task<byte[]> DownloadWithProgressAsync(string url, IProgress<double>? progress = null);
    }
}