using NewsSite.Models;

namespace NewsSite.Services.External
{
    public interface IYouTubeService
    {
        /// <summary>
        /// Fetches videos from YouTube channels based on source configuration
        /// </summary>
        Task<List<Article>> FetchVideosAsync(SourceConfiguration configuration);

        /// <summary>
        /// Fetches latest videos from a specific YouTube channel
        /// </summary>
        Task<List<Article>> FetchChannelVideosAsync(string channelId, int maxResults = 25);

        /// <summary>
        /// Searches YouTube for videos based on query
        /// </summary>
        Task<List<Article>> SearchVideosAsync(string query, int maxResults = 25);

        /// <summary>
        /// Gets detailed information about a YouTube video
        /// </summary>
        Task<YouTubeVideoInfo?> GetVideoInfoAsync(string videoId);

        /// <summary>
        /// Validates YouTube channel ID
        /// </summary>
        Task<bool> ValidateChannelAsync(string channelId);
    }

    public class YouTubeVideoInfo
    {
        public string VideoId { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string ChannelTitle { get; set; } = "";
        public string ChannelId { get; set; } = "";
        public DateTime PublishedAt { get; set; }
        public string ThumbnailUrl { get; set; } = "";
        public TimeSpan Duration { get; set; }
        public long ViewCount { get; set; }
        public long LikeCount { get; set; }
        public List<string> Tags { get; set; } = new();
        public string CategoryName { get; set; } = "";
    }
}