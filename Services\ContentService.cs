using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NewsSite.Data;
using NewsSite.Models;
using System.Linq.Expressions;
using System.Security.Cryptography;
using System.Text;

namespace NewsSite.Services
{
    public class ContentService : IContentService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ContentService> _logger;
        private readonly IMemoryCache _cache;
        private const int DefaultCacheExpirationMinutes = 15;

        public ContentService(ApplicationDbContext context, ILogger<ContentService> logger, IMemoryCache cache)
        {
            _context = context;
            _logger = logger;
            _cache = cache;
        }

        #region Basic CRUD Operations

        public async Task<Article?> GetByIdAsync(int id, bool includeDeleted = false)
        {
            try
            {
                var cacheKey = $"article_{id}_{includeDeleted}";
                if (_cache.TryGetValue(cacheKey, out Article? cachedArticle))
                {
                    return cachedArticle;
                }

                var query = _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Include(a => a.Metadata)
                    .Include(a => a.ArticleTags)
                        .ThenInclude(at => at.Tag)
                    .AsQueryable();

                if (includeDeleted)
                {
                    query = query.IgnoreQueryFilters();
                }

                var article = await query.FirstOrDefaultAsync(a => a.Id == id);

                if (article != null)
                {
                    _cache.Set(cacheKey, article, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return article;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving article with ID {ArticleId}", id);
                throw;
            }
        }

        public async Task<Article?> GetBySlugAsync(string slug, bool includeDeleted = false)
        {
            try
            {
                var cacheKey = $"article_slug_{slug}_{includeDeleted}";
                if (_cache.TryGetValue(cacheKey, out Article? cachedArticle))
                {
                    return cachedArticle;
                }

                var query = _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Include(a => a.Metadata)
                    .Include(a => a.ArticleTags)
                        .ThenInclude(at => at.Tag)
                    .AsQueryable();

                if (includeDeleted)
                {
                    query = query.IgnoreQueryFilters();
                }

                var article = await query.FirstOrDefaultAsync(a => a.Slug == slug);

                if (article != null)
                {
                    _cache.Set(cacheKey, article, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return article;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving article with slug {Slug}", slug);
                throw;
            }
        }

        public async Task<PaginatedResult<Article>> GetArticlesAsync(ArticleSearchCriteria criteria)
        {
            try
            {
                var query = BuildArticleQuery(criteria);
                
                var totalCount = await query.CountAsync();
                var skip = (criteria.Page - 1) * criteria.PageSize;
                
                var articles = await query
                    .Skip(skip)
                    .Take(criteria.PageSize)
                    .ToListAsync();

                return new PaginatedResult<Article>
                {
                    Items = articles,
                    TotalCount = totalCount,
                    Page = criteria.Page,
                    PageSize = criteria.PageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles with criteria");
                throw;
            }
        }

        public async Task<Article> CreateArticleAsync(Article article)
        {
            try
            {
                // Generate slug if not provided
                if (string.IsNullOrEmpty(article.Slug))
                {
                    article.Slug = await GenerateUniqueSlugAsync(article.Title);
                }

                // Generate content hash for duplicate detection
                var contentHash = GenerateContentHash(article.Title + article.Summary);
                var urlHash = GenerateContentHash(article.OriginalUrl);

                // Create metadata
                var metadata = new ContentMetadata
                {
                    ContentHash = contentHash,
                    UrlHash = urlHash,
                    WordCount = CountWords(article.Summary),
                    ReadingTimeMinutes = CalculateReadingTime(article.Summary),
                    HasImages = !string.IsNullOrEmpty(article.ImageUrl)
                };

                article.Metadata = metadata;
                article.CreatedDate = DateTime.UtcNow;

                _context.Articles.Add(article);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created new article with ID {ArticleId}", article.Id);
                InvalidateArticleCache();

                return article;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating article");
                throw;
            }
        }

        public async Task<Article> UpdateArticleAsync(Article article)
        {
            try
            {
                var existingArticle = await _context.Articles
                    .Include(a => a.Metadata)
                    .FirstOrDefaultAsync(a => a.Id == article.Id);

                if (existingArticle == null)
                {
                    throw new ArgumentException("Article not found", nameof(article));
                }

                // Update properties
                existingArticle.Title = article.Title;
                existingArticle.Slug = article.Slug;
                existingArticle.Summary = article.Summary;
                existingArticle.OriginalUrl = article.OriginalUrl;
                existingArticle.ImageUrl = article.ImageUrl;
                existingArticle.ImageAlt = article.ImageAlt;
                existingArticle.Author = article.Author;
                existingArticle.PublishedDate = article.PublishedDate;
                existingArticle.Status = article.Status;
                existingArticle.CategoryId = article.CategoryId;
                existingArticle.SourceId = article.SourceId;
                existingArticle.IsFeatured = article.IsFeatured;
                existingArticle.IsBreaking = article.IsBreaking;
                existingArticle.IsTrending = article.IsTrending;
                existingArticle.Priority = article.Priority;
                existingArticle.MetaTitle = article.MetaTitle;
                existingArticle.MetaDescription = article.MetaDescription;
                existingArticle.ModifiedDate = DateTime.UtcNow;
                existingArticle.ModifiedBy = article.ModifiedBy;

                // Update metadata if content changed
                if (existingArticle.Metadata != null)
                {
                    var contentHash = GenerateContentHash(existingArticle.Title + existingArticle.Summary);
                    existingArticle.Metadata.ContentHash = contentHash;
                    existingArticle.Metadata.WordCount = CountWords(existingArticle.Summary);
                    existingArticle.Metadata.ReadingTimeMinutes = CalculateReadingTime(existingArticle.Summary);
                    existingArticle.Metadata.HasImages = !string.IsNullOrEmpty(existingArticle.ImageUrl);
                    existingArticle.Metadata.ModifiedDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated article with ID {ArticleId}", article.Id);
                InvalidateArticleCache();

                return existingArticle;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating article with ID {ArticleId}", article.Id);
                throw;
            }
        }

        public async Task<bool> DeleteArticleAsync(int id, string deletedBy)
        {
            try
            {
                var article = await _context.Articles.FindAsync(id);
                if (article == null)
                {
                    return false;
                }

                article.IsDeleted = true;
                article.DeletedDate = DateTime.UtcNow;
                article.DeletedBy = deletedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Soft deleted article with ID {ArticleId} by {DeletedBy}", id, deletedBy);
                InvalidateArticleCache();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting article with ID {ArticleId}", id);
                throw;
            }
        }

        public async Task<bool> RestoreArticleAsync(int id)
        {
            try
            {
                var article = await _context.Articles.IgnoreQueryFilters().FirstOrDefaultAsync(a => a.Id == id);
                if (article == null || !article.IsDeleted)
                {
                    return false;
                }

                article.IsDeleted = false;
                article.DeletedDate = null;
                article.DeletedBy = null;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Restored article with ID {ArticleId}", id);
                InvalidateArticleCache();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring article with ID {ArticleId}", id);
                throw;
            }
        }

        #endregion

        #region Special Article Collections

        public async Task<IEnumerable<Article>> GetFeaturedArticlesAsync(int count = 10)
        {
            try
            {
                var cacheKey = $"featured_articles_{count}";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Article>? cachedArticles))
                {
                    return cachedArticles!;
                }

                var articles = await _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => a.IsFeatured && a.Status == ArticleStatus.Published)
                    .OrderByDescending(a => a.Priority)
                    .ThenByDescending(a => a.PublishedDate)
                    .Take(count)
                    .ToListAsync();

                _cache.Set(cacheKey, articles, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured articles");
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetBreakingNewsAsync(int count = 5)
        {
            try
            {
                var cacheKey = $"breaking_news_{count}";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Article>? cachedArticles))
                {
                    return cachedArticles!;
                }

                var articles = await _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => a.IsBreaking && a.Status == ArticleStatus.Published)
                    .OrderByDescending(a => a.PublishedDate)
                    .Take(count)
                    .ToListAsync();

                _cache.Set(cacheKey, articles, TimeSpan.FromMinutes(5)); // Shorter cache for breaking news
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving breaking news");
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetTrendingArticlesAsync(int count = 10)
        {
            try
            {
                var cacheKey = $"trending_articles_{count}";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Article>? cachedArticles))
                {
                    return cachedArticles!;
                }

                var articles = await _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Include(a => a.Metadata)
                    .Where(a => a.IsTrending && a.Status == ArticleStatus.Published)
                    .OrderByDescending(a => a.Metadata!.TrendingScore)
                    .ThenByDescending(a => a.PublishedDate)
                    .Take(count)
                    .ToListAsync();

                _cache.Set(cacheKey, articles, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving trending articles");
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetLatestArticlesAsync(int count = 20)
        {
            try
            {
                var cacheKey = $"latest_articles_{count}";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Article>? cachedArticles))
                {
                    return cachedArticles!;
                }

                var articles = await _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => a.Status == ArticleStatus.Published)
                    .OrderByDescending(a => a.PublishedDate)
                    .Take(count)
                    .ToListAsync();

                _cache.Set(cacheKey, articles, TimeSpan.FromMinutes(5)); // Shorter cache for latest
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving latest articles");
                throw;
            }
        }

        public async Task<PaginatedResult<Article>> GetArticlesByCategoryAsync(int categoryId, int page = 1, int pageSize = 20)
        {
            try
            {
                var criteria = new ArticleSearchCriteria
                {
                    CategoryId = categoryId,
                    Status = ArticleStatus.Published,
                    Page = page,
                    PageSize = pageSize
                };

                return await GetArticlesAsync(criteria);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<PaginatedResult<Article>> GetArticlesBySourceAsync(int sourceId, int page = 1, int pageSize = 20)
        {
            try
            {
                var criteria = new ArticleSearchCriteria
                {
                    SourceId = sourceId,
                    Status = ArticleStatus.Published,
                    Page = page,
                    PageSize = pageSize
                };

                return await GetArticlesAsync(criteria);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for source {SourceId}", sourceId);
                throw;
            }
        }

        #endregion

        #region Content Management

        public async Task<bool> SetFeaturedStatusAsync(int articleId, bool isFeatured)
        {
            try
            {
                var article = await _context.Articles.FindAsync(articleId);
                if (article == null) return false;

                article.IsFeatured = isFeatured;
                article.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateArticleCache();

                _logger.LogInformation("Updated featured status for article {ArticleId} to {IsFeatured}", articleId, isFeatured);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating featured status for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<bool> SetBreakingNewsStatusAsync(int articleId, bool isBreaking)
        {
            try
            {
                var article = await _context.Articles.FindAsync(articleId);
                if (article == null) return false;

                article.IsBreaking = isBreaking;
                article.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateArticleCache();

                _logger.LogInformation("Updated breaking news status for article {ArticleId} to {IsBreaking}", articleId, isBreaking);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating breaking news status for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<bool> SetTrendingStatusAsync(int articleId, bool isTrending)
        {
            try
            {
                var article = await _context.Articles.FindAsync(articleId);
                if (article == null) return false;

                article.IsTrending = isTrending;
                article.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateArticleCache();

                _logger.LogInformation("Updated trending status for article {ArticleId} to {IsTrending}", articleId, isTrending);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating trending status for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<bool> UpdateArticleStatusAsync(int articleId, ArticleStatus status)
        {
            try
            {
                var article = await _context.Articles.FindAsync(articleId);
                if (article == null) return false;

                article.Status = status;
                article.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateArticleCache();

                _logger.LogInformation("Updated status for article {ArticleId} to {Status}", articleId, status);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating status for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<bool> UpdateArticlePriorityAsync(int articleId, int priority)
        {
            try
            {
                var article = await _context.Articles.FindAsync(articleId);
                if (article == null) return false;

                article.Priority = Math.Max(1, Math.Min(10, priority)); // Clamp between 1-10
                article.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateArticleCache();

                _logger.LogInformation("Updated priority for article {ArticleId} to {Priority}", articleId, priority);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating priority for article {ArticleId}", articleId);
                throw;
            }
        }

        #endregion

        #region Duplicate Detection

        public async Task<Article?> FindDuplicateAsync(string contentHash)
        {
            try
            {
                return await _context.Articles
                    .Include(a => a.Metadata)
                    .Where(a => a.Metadata != null && a.Metadata.ContentHash == contentHash)
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding duplicate by content hash");
                throw;
            }
        }

        public async Task<Article?> FindDuplicateByUrlAsync(string urlHash)
        {
            try
            {
                return await _context.Articles
                    .Include(a => a.Metadata)
                    .Where(a => a.Metadata != null && a.Metadata.UrlHash == urlHash)
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding duplicate by URL hash");
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetDuplicatesAsync(int originalArticleId)
        {
            try
            {
                return await _context.Articles
                    .Include(a => a.Metadata)
                    .Where(a => a.Metadata != null && a.Metadata.OriginalArticleId == originalArticleId)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving duplicates for article {ArticleId}", originalArticleId);
                throw;
            }
        }

        public async Task<bool> MarkAsDuplicateAsync(int articleId, int originalArticleId)
        {
            try
            {
                var article = await _context.Articles
                    .Include(a => a.Metadata)
                    .FirstOrDefaultAsync(a => a.Id == articleId);

                if (article?.Metadata == null) return false;

                article.Metadata.IsDuplicate = true;
                article.Metadata.OriginalArticleId = originalArticleId;
                article.Metadata.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Marked article {ArticleId} as duplicate of {OriginalArticleId}", articleId, originalArticleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking article {ArticleId} as duplicate", articleId);
                throw;
            }
        }

        #endregion

        #region Content Analysis

        public async Task<bool> UpdateContentMetadataAsync(int articleId, ContentMetadata metadata)
        {
            try
            {
                var existingMetadata = await _context.ContentMetadata
                    .FirstOrDefaultAsync(m => m.ArticleId == articleId);

                if (existingMetadata == null)
                {
                    metadata.ArticleId = articleId;
                    _context.ContentMetadata.Add(metadata);
                }
                else
                {
                    // Update existing metadata
                    existingMetadata.ViewCount = metadata.ViewCount;
                    existingMetadata.ShareCount = metadata.ShareCount;
                    existingMetadata.LikeCount = metadata.LikeCount;
                    existingMetadata.CommentCount = metadata.CommentCount;
                    existingMetadata.ClickThroughCount = metadata.ClickThroughCount;
                    existingMetadata.QualityScore = metadata.QualityScore;
                    existingMetadata.TrendingScore = metadata.TrendingScore;
                    existingMetadata.ModifiedDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating metadata for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<bool> IncrementViewCountAsync(int articleId)
        {
            try
            {
                var metadata = await _context.ContentMetadata
                    .FirstOrDefaultAsync(m => m.ArticleId == articleId);

                if (metadata != null)
                {
                    metadata.ViewCount++;
                    metadata.ModifiedDate = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }

                return metadata != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing view count for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<bool> IncrementClickThroughCountAsync(int articleId)
        {
            try
            {
                var metadata = await _context.ContentMetadata
                    .FirstOrDefaultAsync(m => m.ArticleId == articleId);

                if (metadata != null)
                {
                    metadata.ClickThroughCount++;
                    metadata.LastClickDate = DateTime.UtcNow;
                    metadata.ModifiedDate = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }

                return metadata != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing click-through count for article {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<bool> UpdateEngagementMetricsAsync(int articleId, int shareCount, int likeCount, int commentCount)
        {
            try
            {
                var metadata = await _context.ContentMetadata
                    .FirstOrDefaultAsync(m => m.ArticleId == articleId);

                if (metadata != null)
                {
                    metadata.ShareCount = shareCount;
                    metadata.LikeCount = likeCount;
                    metadata.CommentCount = commentCount;
                    metadata.ModifiedDate = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }

                return metadata != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating engagement metrics for article {ArticleId}", articleId);
                throw;
            }
        }

        #endregion

        #region Bulk Operations

        public async Task<int> BulkUpdateStatusAsync(IEnumerable<int> articleIds, ArticleStatus status)
        {
            try
            {
                var articles = await _context.Articles
                    .Where(a => articleIds.Contains(a.Id))
                    .ToListAsync();

                foreach (var article in articles)
                {
                    article.Status = status;
                    article.ModifiedDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                InvalidateArticleCache();

                _logger.LogInformation("Bulk updated status to {Status} for {Count} articles", status, articles.Count);
                return articles.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating article status");
                throw;
            }
        }

        public async Task<int> BulkDeleteAsync(IEnumerable<int> articleIds, string deletedBy)
        {
            try
            {
                var articles = await _context.Articles
                    .Where(a => articleIds.Contains(a.Id))
                    .ToListAsync();

                foreach (var article in articles)
                {
                    article.IsDeleted = true;
                    article.DeletedDate = DateTime.UtcNow;
                    article.DeletedBy = deletedBy;
                }

                await _context.SaveChangesAsync();
                InvalidateArticleCache();

                _logger.LogInformation("Bulk deleted {Count} articles by {DeletedBy}", articles.Count, deletedBy);
                return articles.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk deleting articles");
                throw;
            }
        }

        public async Task<int> BulkAssignCategoryAsync(IEnumerable<int> articleIds, int categoryId)
        {
            try
            {
                var articles = await _context.Articles
                    .Where(a => articleIds.Contains(a.Id))
                    .ToListAsync();

                foreach (var article in articles)
                {
                    article.CategoryId = categoryId;
                    article.ModifiedDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                InvalidateArticleCache();

                _logger.LogInformation("Bulk assigned category {CategoryId} to {Count} articles", categoryId, articles.Count);
                return articles.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk assigning category");
                throw;
            }
        }

        #endregion

        #region Search and Filtering

        public async Task<PaginatedResult<Article>> SearchArticlesAsync(string query, ArticleSearchCriteria? criteria = null)
        {
            try
            {
                criteria ??= new ArticleSearchCriteria();
                criteria.Keywords = query;

                return await GetArticlesAsync(criteria);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching articles with query: {Query}", query);
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetRelatedArticlesAsync(int articleId, int count = 5)
        {
            try
            {
                var article = await _context.Articles
                    .Include(a => a.ArticleTags)
                    .FirstOrDefaultAsync(a => a.Id == articleId);

                if (article == null) return new List<Article>();

                var tagIds = article.ArticleTags.Select(at => at.TagId).ToList();

                return await _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => a.Id != articleId && 
                               a.Status == ArticleStatus.Published &&
                               (a.CategoryId == article.CategoryId || 
                                a.ArticleTags.Any(at => tagIds.Contains(at.TagId))))
                    .OrderByDescending(a => a.PublishedDate)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving related articles for {ArticleId}", articleId);
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetPopularArticlesAsync(int days = 7, int count = 10)
        {
            try
            {
                var fromDate = DateTime.UtcNow.AddDays(-days);

                return await _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Include(a => a.Metadata)
                    .Where(a => a.Status == ArticleStatus.Published && 
                               a.PublishedDate >= fromDate)
                    .OrderByDescending(a => a.Metadata!.ViewCount)
                    .ThenByDescending(a => a.Metadata!.ShareCount)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving popular articles");
                throw;
            }
        }

        #endregion

        #region Statistics

        public async Task<int> GetTotalArticleCountAsync()
        {
            try
            {
                return await _context.Articles.CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total article count");
                throw;
            }
        }

        public async Task<int> GetArticleCountByStatusAsync(ArticleStatus status)
        {
            try
            {
                return await _context.Articles.CountAsync(a => a.Status == status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting article count by status {Status}", status);
                throw;
            }
        }

        public async Task<int> GetArticleCountByCategoryAsync(int categoryId)
        {
            try
            {
                return await _context.Articles.CountAsync(a => a.CategoryId == categoryId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting article count for category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetArticleCountByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var articles = await _context.Articles
                    .Where(a => a.PublishedDate >= fromDate && a.PublishedDate <= toDate)
                    .GroupBy(a => a.PublishedDate.Date)
                    .Select(g => new { Date = g.Key, Count = g.Count() })
                    .ToListAsync();

                return articles.ToDictionary(x => x.Date.ToString("yyyy-MM-dd"), x => x.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting article count by date range");
                throw;
            }
        }

        #endregion

        #region Validation

        public async Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null)
        {
            try
            {
                var query = _context.Articles.Where(a => a.Slug == slug);
                
                if (excludeId.HasValue)
                {
                    query = query.Where(a => a.Id != excludeId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking slug uniqueness for {Slug}", slug);
                throw;
            }
        }

        public async Task<bool> ValidateArticleAsync(Article article)
        {
            try
            {
                // Check required fields
                if (string.IsNullOrWhiteSpace(article.Title) ||
                    string.IsNullOrWhiteSpace(article.Summary) ||
                    string.IsNullOrWhiteSpace(article.OriginalUrl))
                {
                    return false;
                }

                // Check slug uniqueness
                if (!await IsSlugUniqueAsync(article.Slug, article.Id))
                {
                    return false;
                }

                // Check category exists
                var categoryExists = await _context.Categories.AnyAsync(c => c.Id == article.CategoryId);
                if (!categoryExists)
                {
                    return false;
                }

                // Check source exists
                var sourceExists = await _context.Sources.AnyAsync(s => s.Id == article.SourceId);
                if (!sourceExists)
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating article");
                throw;
            }
        }

        #endregion

        #region Private Helper Methods

        private IQueryable<Article> BuildArticleQuery(ArticleSearchCriteria criteria)
        {
            var query = _context.Articles
                .Include(a => a.Category)
                .Include(a => a.Source)
                .Include(a => a.Metadata)
                .Include(a => a.ArticleTags)
                    .ThenInclude(at => at.Tag)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(criteria.Keywords))
            {
                query = query.Where(a => a.Title.Contains(criteria.Keywords) || 
                                        a.Summary.Contains(criteria.Keywords) ||
                                        (a.Author != null && a.Author.Contains(criteria.Keywords)));
            }

            if (criteria.CategoryId.HasValue)
            {
                query = query.Where(a => a.CategoryId == criteria.CategoryId.Value);
            }

            if (criteria.SourceId.HasValue)
            {
                query = query.Where(a => a.SourceId == criteria.SourceId.Value);
            }

            if (criteria.Status.HasValue)
            {
                query = query.Where(a => a.Status == criteria.Status.Value);
            }

            if (criteria.IsFeatured.HasValue)
            {
                query = query.Where(a => a.IsFeatured == criteria.IsFeatured.Value);
            }

            if (criteria.IsBreaking.HasValue)
            {
                query = query.Where(a => a.IsBreaking == criteria.IsBreaking.Value);
            }

            if (criteria.IsTrending.HasValue)
            {
                query = query.Where(a => a.IsTrending == criteria.IsTrending.Value);
            }

            if (criteria.FromDate.HasValue)
            {
                query = query.Where(a => a.PublishedDate >= criteria.FromDate.Value);
            }

            if (criteria.ToDate.HasValue)
            {
                query = query.Where(a => a.PublishedDate <= criteria.ToDate.Value);
            }

            if (criteria.TagIds?.Any() == true)
            {
                query = query.Where(a => a.ArticleTags.Any(at => criteria.TagIds.Contains(at.TagId)));
            }

            if (!string.IsNullOrEmpty(criteria.Author))
            {
                query = query.Where(a => a.Author != null && a.Author.Contains(criteria.Author));
            }

            // Apply sorting
            query = criteria.SortBy.ToLower() switch
            {
                "title" => criteria.SortDescending ? query.OrderByDescending(a => a.Title) : query.OrderBy(a => a.Title),
                "publisheddate" => criteria.SortDescending ? query.OrderByDescending(a => a.PublishedDate) : query.OrderBy(a => a.PublishedDate),
                "priority" => criteria.SortDescending ? query.OrderByDescending(a => a.Priority) : query.OrderBy(a => a.Priority),
                "viewcount" => criteria.SortDescending ? query.OrderByDescending(a => a.Metadata!.ViewCount) : query.OrderBy(a => a.Metadata!.ViewCount),
                _ => criteria.SortDescending ? query.OrderByDescending(a => a.PublishedDate) : query.OrderBy(a => a.PublishedDate)
            };

            return query;
        }

        private async Task<string> GenerateUniqueSlugAsync(string title)
        {
            var baseSlug = GenerateSlug(title);
            var slug = baseSlug;
            var counter = 1;

            while (!await IsSlugUniqueAsync(slug))
            {
                slug = $"{baseSlug}-{counter}";
                counter++;
            }

            return slug;
        }

        private static string GenerateSlug(string title)
        {
            if (string.IsNullOrEmpty(title))
                return string.Empty;

            return title
                .ToLowerInvariant()
                .Replace(" ", "-")
                .Replace("&", "and")
                .RegexReplace(@"[^a-z0-9\-]", "")
                .RegexReplace(@"-+", "-")
                .Trim('-');
        }

        private static string GenerateContentHash(string content)
        {
            using var sha256 = SHA256.Create();
            var bytes = Encoding.UTF8.GetBytes(content);
            var hash = sha256.ComputeHash(bytes);
            return Convert.ToHexString(hash).ToLowerInvariant();
        }

        private static int CountWords(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return 0;

            return text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
        }

        private static int CalculateReadingTime(string text)
        {
            var wordCount = CountWords(text);
            const int wordsPerMinute = 200; // Average reading speed
            return Math.Max(1, (int)Math.Ceiling((double)wordCount / wordsPerMinute));
        }

        private void InvalidateArticleCache()
        {
            // In a real application, you might want to implement more sophisticated cache invalidation
            // For now, we'll rely on the cache expiration times
        }

        #endregion
    }

    public static class StringExtensions
    {
        public static string RegexReplace(this string input, string pattern, string replacement)
        {
            return System.Text.RegularExpressions.Regex.Replace(input, pattern, replacement);
        }
    }
}