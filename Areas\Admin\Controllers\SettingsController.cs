using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NewsSite.Areas.Admin.Models;

namespace NewsSite.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class SettingsController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SettingsController> _logger;

        public SettingsController(
            IConfiguration configuration,
            ILogger<SettingsController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var viewModel = await LoadSettingsAsync();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading system settings");
                TempData["Error"] = "Failed to load settings. Please try again.";
                return View(new SystemSettingsViewModel());
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateGlobal(GlobalSettings model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var viewModel = await LoadSettingsAsync();
                    viewModel.Global = model;
                    return View("Index", viewModel);
                }

                // In a real implementation, these would be stored in database or configuration
                // For now, we'll just simulate the update
                var updatedSettings = new List<string>
                {
                    "Site Name",
                    "Site Description",
                    "Default Time Zone"
                };

                if (model.MaintenanceMode)
                {
                    updatedSettings.Add("Maintenance Mode");
                }

                TempData["Success"] = $"Global settings updated: {string.Join(", ", updatedSettings)}";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating global settings");
                ModelState.AddModelError("", "Failed to update settings. Please try again.");
                
                var viewModel = await LoadSettingsAsync();
                viewModel.Global = model;
                return View("Index", viewModel);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateContentAggregation(ContentAggregationSettings model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var viewModel = await LoadSettingsAsync();
                    viewModel.ContentAggregation = model;
                    return View("Index", viewModel);
                }

                // Simulate updating settings
                var updatedSettings = new List<string>
                {
                    "Auto Sync",
                    "Default Sync Interval",
                    "Max Articles Per Sync"
                };

                TempData["Success"] = $"Content aggregation settings updated: {string.Join(", ", updatedSettings)}";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating content aggregation settings");
                ModelState.AddModelError("", "Failed to update settings. Please try again.");
                
                var viewModel = await LoadSettingsAsync();
                viewModel.ContentAggregation = model;
                return View("Index", viewModel);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateQualityControl(QualityControlSettings model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var viewModel = await LoadSettingsAsync();
                    viewModel.QualityControl = model;
                    return View("Index", viewModel);
                }

                // Simulate updating settings
                var updatedSettings = new List<string>
                {
                    "Content Filtering",
                    "Quality Scoring",
                    "Minimum Content Length"
                };

                TempData["Success"] = $"Quality control settings updated: {string.Join(", ", updatedSettings)}";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating quality control settings");
                ModelState.AddModelError("", "Failed to update settings. Please try again.");
                
                var viewModel = await LoadSettingsAsync();
                viewModel.QualityControl = model;
                return View("Index", viewModel);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateNotifications(NotificationSettings model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var viewModel = await LoadSettingsAsync();
                    viewModel.Notifications = model;
                    return View("Index", viewModel);
                }

                // Validate SMTP settings if email notifications are enabled
                if (model.EnableEmailNotifications)
                {
                    if (string.IsNullOrEmpty(model.SmtpServer))
                    {
                        ModelState.AddModelError("Notifications.SmtpServer", "SMTP server is required when email notifications are enabled");
                    }
                    if (string.IsNullOrEmpty(model.AdminEmail))
                    {
                        ModelState.AddModelError("Notifications.AdminEmail", "Admin email is required when email notifications are enabled");
                    }
                }

                if (!ModelState.IsValid)
                {
                    var viewModel = await LoadSettingsAsync();
                    viewModel.Notifications = model;
                    return View("Index", viewModel);
                }

                // Simulate updating settings
                var updatedSettings = new List<string>
                {
                    "Email Notifications",
                    "SMTP Configuration",
                    "Notification Preferences"
                };

                TempData["Success"] = $"Notification settings updated: {string.Join(", ", updatedSettings)}";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating notification settings");
                ModelState.AddModelError("", "Failed to update settings. Please try again.");
                
                var viewModel = await LoadSettingsAsync();
                viewModel.Notifications = model;
                return View("Index", viewModel);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdatePerformance(PerformanceSettings model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var viewModel = await LoadSettingsAsync();
                    viewModel.Performance = model;
                    return View("Index", viewModel);
                }

                // Simulate updating settings
                var updatedSettings = new List<string>
                {
                    "Caching Settings",
                    "HTTP Configuration",
                    "Database Settings"
                };

                TempData["Success"] = $"Performance settings updated: {string.Join(", ", updatedSettings)}";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating performance settings");
                ModelState.AddModelError("", "Failed to update settings. Please try again.");
                
                var viewModel = await LoadSettingsAsync();
                viewModel.Performance = model;
                return View("Index", viewModel);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateSecurity(SecuritySettings model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var viewModel = await LoadSettingsAsync();
                    viewModel.Security = model;
                    return View("Index", viewModel);
                }

                // Simulate updating settings
                var updatedSettings = new List<string>
                {
                    "Security Policies",
                    "Rate Limiting",
                    "Audit Settings"
                };

                TempData["Success"] = $"Security settings updated: {string.Join(", ", updatedSettings)}";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating security settings");
                ModelState.AddModelError("", "Failed to update settings. Please try again.");
                
                var viewModel = await LoadSettingsAsync();
                viewModel.Security = model;
                return View("Index", viewModel);
            }
        }

        [HttpPost]
        public async Task<IActionResult> TestEmailSettings([FromBody] NotificationSettings model)
        {
            try
            {
                // In a real implementation, this would send a test email
                await Task.Delay(1000); // Simulate email sending

                return Json(new { 
                    success = true, 
                    message = "Test email sent successfully to " + model.AdminEmail 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing email settings");
                return Json(new { 
                    success = false, 
                    message = "Failed to send test email: " + ex.Message 
                });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ClearCache()
        {
            try
            {
                // In a real implementation, this would clear various caches
                await Task.Delay(500); // Simulate cache clearing

                return Json(new { 
                    success = true, 
                    message = "All caches cleared successfully" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache");
                return Json(new { 
                    success = false, 
                    message = "Failed to clear cache: " + ex.Message 
                });
            }
        }

        [HttpGet]
        public async Task<IActionResult> ExportSettings()
        {
            try
            {
                var settings = await LoadSettingsAsync();
                var json = System.Text.Json.JsonSerializer.Serialize(settings, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });

                var bytes = System.Text.Encoding.UTF8.GetBytes(json);
                var fileName = $"system-settings-{DateTime.UtcNow:yyyyMMdd-HHmmss}.json";

                return File(bytes, "application/json", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting settings");
                TempData["Error"] = "Failed to export settings. Please try again.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportSettings(IFormFile settingsFile)
        {
            try
            {
                if (settingsFile == null || settingsFile.Length == 0)
                {
                    TempData["Error"] = "Please select a settings file to import.";
                    return RedirectToAction(nameof(Index));
                }

                using var reader = new StreamReader(settingsFile.OpenReadStream());
                var json = await reader.ReadToEndAsync();
                
                var settings = System.Text.Json.JsonSerializer.Deserialize<SystemSettingsViewModel>(json);
                
                if (settings == null)
                {
                    TempData["Error"] = "Invalid settings file format.";
                    return RedirectToAction(nameof(Index));
                }

                // In a real implementation, this would update the actual settings
                TempData["Success"] = "Settings imported successfully. Please review and save changes.";
                return View("Index", settings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing settings");
                TempData["Error"] = "Failed to import settings. Please check the file format.";
                return RedirectToAction(nameof(Index));
            }
        }

        private async Task<SystemSettingsViewModel> LoadSettingsAsync()
        {
            // In a real implementation, these would be loaded from database or configuration
            await Task.CompletedTask;

            return new SystemSettingsViewModel
            {
                Global = new GlobalSettings
                {
                    SiteName = _configuration["SiteName"] ?? "AI Frontiers",
                    SiteDescription = _configuration["SiteDescription"] ?? "Your source for AI news and insights",
                    DefaultTimeZone = "UTC",
                    MaintenanceMode = false,
                    EnableAnalytics = true,
                    GoogleAnalyticsId = _configuration["GoogleAnalytics:TrackingId"],
                    ContactEmail = _configuration["ContactEmail"]
                },
                ContentAggregation = new ContentAggregationSettings
                {
                    EnableAutoSync = true,
                    DefaultSyncIntervalMinutes = 60,
                    MaxArticlesPerSourcePerSync = 50,
                    DefaultArticleRetentionDays = 30,
                    AutoCategorizeArticles = true,
                    AutoGenerateTags = true,
                    EnableDuplicateDetection = true,
                    DuplicateDetectionThreshold = 0.85,
                    MaxConcurrentSyncs = 5
                },
                QualityControl = new QualityControlSettings
                {
                    MinContentLength = 100,
                    MaxSummaryLength = 500,
                    RequireImages = false,
                    EnableContentFiltering = true,
                    MinQualityScore = 3.0,
                    EnableAiContentScoring = false,
                    AutoModerateContent = true
                },
                Notifications = new NotificationSettings
                {
                    EnableEmailNotifications = true,
                    AdminEmail = _configuration["AdminEmail"],
                    SmtpServer = _configuration["Smtp:Server"],
                    SmtpPort = int.TryParse(_configuration["Smtp:Port"], out var port) ? port : 587,
                    SmtpUsername = _configuration["Smtp:Username"],
                    SmtpUseSsl = true,
                    FromEmail = _configuration["Smtp:FromEmail"],
                    FromName = _configuration["Smtp:FromName"] ?? "AI Frontiers",
                    NotifyOnSyncErrors = true,
                    NotifyOnSystemErrors = true,
                    SendDailySummary = false
                },
                Performance = new PerformanceSettings
                {
                    CacheDurationSeconds = 300,
                    MaxConcurrentHttpRequests = 10,
                    HttpRequestTimeoutSeconds = 30,
                    EnableResponseCompression = true,
                    EnableOutputCaching = true,
                    MaxDatabaseConnections = 100,
                    DatabaseCommandTimeoutSeconds = 30,
                    EnableBackgroundProcessing = true
                },
                Security = new SecuritySettings
                {
                    RequireHttps = true,
                    EnableCors = false,
                    RateLimitRequestsPerMinute = 60,
                    EnableIpBlocking = true,
                    EnableAuditLogging = true,
                    AuditLogRetentionDays = 90,
                    EnableFailedLoginTracking = true,
                    MaxFailedLoginAttempts = 5,
                    LoginLockoutDurationMinutes = 30
                }
            };
        }
    }
}