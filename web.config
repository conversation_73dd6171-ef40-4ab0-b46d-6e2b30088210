<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" 
                  arguments=".\NewsSite.dll" 
                  stdoutLogEnabled="false" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="inprocess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
          <environmentVariable name="ASPNETCORE_HTTPS_PORT" value="443" />
        </environmentVariables>
      </aspNetCore>
      
      <!-- Security Headers -->
      <httpProtocol>
        <customHeaders>
          <add name="X-Content-Type-Options" value="nosniff" />
          <add name="X-Frame-Options" value="DENY" />
          <add name="X-XSS-Protection" value="1; mode=block" />
          <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
          <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
        </customHeaders>
      </httpProtocol>
      
      <!-- URL Rewrite Rules -->
      <rewrite>
        <rules>
          <!-- Force HTTPS -->
          <rule name="Redirect to HTTPS" stopProcessing="true">
            <match url=".*" />
            <conditions>
              <add input="{HTTPS}" pattern="off" ignoreCase="true" />
              <add input="{HTTP_HOST}" pattern="localhost" negate="true" />
            </conditions>
            <action type="Redirect" url="https://{HTTP_HOST}/{R:0}" redirectType="Permanent" />
          </rule>
          
          <!-- Remove trailing slash -->
          <rule name="Remove trailing slash" stopProcessing="true">
            <match url="(.*)/$" />
            <conditions>
              <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
              <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            </conditions>
            <action type="Redirect" redirectType="Permanent" url="{R:1}" />
          </rule>
        </rules>
      </rewrite>
      
      <!-- Static Content Caching -->
      <staticContent>
        <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="30.00:00:00" />
        <remove fileExtension=".woff" />
        <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
        <remove fileExtension=".woff2" />
        <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
      </staticContent>
      
      <!-- Compression -->
      <httpCompression>
        <dynamicTypes>
          <add mimeType="application/json" enabled="true" />
          <add mimeType="application/javascript" enabled="true" />
          <add mimeType="text/css" enabled="true" />
          <add mimeType="text/html" enabled="true" />
          <add mimeType="text/xml" enabled="true" />
          <add mimeType="application/xml" enabled="true" />
        </dynamicTypes>
        <staticTypes>
          <add mimeType="application/javascript" enabled="true" />
          <add mimeType="text/css" enabled="true" />
          <add mimeType="text/xml" enabled="true" />
          <add mimeType="application/xml" enabled="true" />
        </staticTypes>
      </httpCompression>
      
      <!-- Default Documents -->
      <defaultDocument>
        <files>
          <clear />
          <add value="index.html" />
        </files>
      </defaultDocument>
      
      <!-- Error Pages -->
      <httpErrors errorMode="Custom" defaultResponseMode="ExecuteURL">
        <remove statusCode="404" subStatusCode="-1" />
        <error statusCode="404" path="/Home/Error" responseMode="ExecuteURL" />
        <remove statusCode="500" subStatusCode="-1" />
        <error statusCode="500" path="/Home/Error" responseMode="ExecuteURL" />
      </httpErrors>
      
      <!-- Request Filtering -->
      <security>
        <requestFiltering>
          <requestLimits maxAllowedContentLength="52428800" />
          <hiddenSegments>
            <add segment="bin" />
            <add segment="App_code" />
            <add segment="App_GlobalResources" />
            <add segment="App_LocalResources" />
            <add segment="App_WebReferences" />
            <add segment="App_Data" />
            <add segment="App_Browsers" />
          </hiddenSegments>
        </requestFiltering>
      </security>
    </system.webServer>
  </location>
</configuration>