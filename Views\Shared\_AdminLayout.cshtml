<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - AI Frontiers Admin</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/admin.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        /* Admin-specific dark theme overrides */
        :root {
            --bs-primary: #0d6efd;
            --bs-success: #198754;
            --bs-warning: #ffc107;
            --bs-danger: #dc3545;
            --bs-info: #0dcaf0;
            --admin-sidebar-bg: #1a1d23;
            --admin-sidebar-border: #2d3136;
            --admin-card-bg: #262b36;
            --admin-card-border: #3a4049;
        }

        body {
            background-color: #0f1419;
            color: #e9ecef;
        }

        .admin-wrapper {
            min-height: 100vh;
            display: flex;
        }

        .admin-sidebar {
            width: 280px;
            background-color: var(--admin-sidebar-bg);
            border-right: 1px solid var(--admin-sidebar-border);
            padding: 1.5rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .admin-content {
            flex: 1;
            margin-left: 280px;
            padding: 2rem;
        }

        .admin-nav-link {
            color: #adb5bd;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            transition: all 0.2s;
        }

        .admin-nav-link:hover,
        .admin-nav-link.active {
            background-color: rgba(13, 110, 253, 0.1);
            color: var(--bs-primary);
            border-right: 3px solid var(--bs-primary);
        }

        .admin-nav-link i {
            width: 20px;
            margin-right: 0.75rem;
        }

        .admin-card {
            background-color: var(--admin-card-bg);
            border: 1px solid var(--admin-card-border);
            border-radius: 0.75rem;
        }

        .admin-stat-card {
            background: linear-gradient(135deg, var(--admin-card-bg) 0%, #1e242d 100%);
            border: 1px solid var(--admin-card-border);
            border-radius: 1rem;
            padding: 1.5rem;
            transition: transform 0.2s;
        }

        .admin-stat-card:hover {
            transform: translateY(-2px);
        }

        .admin-stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .table-dark {
            --bs-table-bg: var(--admin-card-bg);
            --bs-table-border-color: var(--admin-card-border);
        }

        .status-healthy { color: var(--bs-success); }
        .status-warning { color: var(--bs-warning); }
        .status-error { color: var(--bs-danger); }

        .chart-container {
            position: relative;
            height: 300px;
            background-color: var(--admin-card-bg);
            border: 1px solid var(--admin-card-border);
            border-radius: 0.75rem;
            padding: 1rem;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .admin-sidebar.show {
                transform: translateX(0);
            }
            
            .admin-content {
                margin-left: 0;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-wrapper">
        <!-- Admin Sidebar -->
        <nav class="admin-sidebar">
            <div class="px-3 mb-4">
                <h4 class="text-primary">
                    <i class="fas fa-cogs me-2"></i>
                    Admin Panel
                </h4>
                <small class="text-muted">AI Frontiers Management</small>
            </div>

            <div class="nav flex-column">
                <a class="admin-nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "Dashboard" ? "active" : "")" 
                   asp-area="Admin" asp-controller="Dashboard" asp-action="Index">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                
                <a class="admin-nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "Sources" ? "active" : "")" 
                   asp-area="Admin" asp-controller="Sources" asp-action="Index">
                    <i class="fas fa-rss"></i>
                    Content Sources
                </a>
                
                <a class="admin-nav-link" href="#" onclick="toggleSubmenu('content-menu')">
                    <i class="fas fa-newspaper"></i>
                    Content Management
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div id="content-menu" class="collapse">
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-list"></i>
                        All Articles
                    </a>
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-tags"></i>
                        Categories & Tags
                    </a>
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-flag"></i>
                        Moderation Queue
                    </a>
                </div>
                
                <a class="admin-nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "Settings" ? "active" : "")" 
                   asp-area="Admin" asp-controller="Settings" asp-action="Index">
                    <i class="fas fa-cog"></i>
                    System Settings
                </a>
                
                <a class="admin-nav-link" href="/hangfire" target="_blank">
                    <i class="fas fa-tasks"></i>
                    Background Jobs
                    <i class="fas fa-external-link-alt ms-auto"></i>
                </a>
                
                <a class="admin-nav-link" href="#" onclick="toggleSubmenu('analytics-menu')">
                    <i class="fas fa-chart-line"></i>
                    Analytics & Reports
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div id="analytics-menu" class="collapse">
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-chart-bar"></i>
                        Content Analytics
                    </a>
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-users"></i>
                        User Analytics
                    </a>
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-download"></i>
                        Export Reports
                    </a>
                </div>
                
                <hr class="my-3 border-secondary">
                
                <a class="admin-nav-link" href="#" onclick="toggleSubmenu('system-menu')">
                    <i class="fas fa-server"></i>
                    System Health
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div id="system-menu" class="collapse">
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-database"></i>
                        Database Status
                    </a>
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-file-alt"></i>
                        System Logs
                    </a>
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-shield-alt"></i>
                        Security Audit
                    </a>
                </div>
                
                <a class="admin-nav-link" href="#" onclick="toggleSubmenu('user-menu')">
                    <i class="fas fa-users-cog"></i>
                    User Management
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div id="user-menu" class="collapse">
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-user-friends"></i>
                        All Users
                    </a>
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-user-shield"></i>
                        Roles & Permissions
                    </a>
                    <a class="admin-nav-link ps-5" href="#">
                        <i class="fas fa-user-lock"></i>
                        Banned Users
                    </a>
                </div>

                <hr class="my-3 border-secondary">
                
                <a class="admin-nav-link" asp-controller="Home" asp-action="Index" asp-area="">
                    <i class="fas fa-external-link-alt"></i>
                    Back to Site
                </a>
                
                <partial name="_LoginPartial" />
            </div>
        </nav>

        <!-- Main Content -->
        <div class="admin-content">
            <!-- Top Bar -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">@ViewData["Title"]</h1>
                    @if (ViewData["Subtitle"] != null)
                    {
                        <p class="text-muted mb-0">@ViewData["Subtitle"]</p>
                    }
                </div>
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-secondary d-md-none me-2" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-plus me-1"></i>Quick Actions
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" asp-area="Admin" asp-controller="Sources" asp-action="Create">
                                <i class="fas fa-rss me-2"></i>Add Source
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="triggerSync()">
                                <i class="fas fa-sync me-2"></i>Sync All Sources
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="clearCache()">
                                <i class="fas fa-broom me-2"></i>Clear Cache
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            @if (TempData["Success"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    @TempData["Success"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            @if (TempData["Error"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @TempData["Error"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            @if (TempData["Warning"] != null)
            {
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    @TempData["Warning"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }
            @if (TempData["Info"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    @TempData["Info"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Page Content -->
            <main>
                @RenderBody()
            </main>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Admin-specific JavaScript
        function toggleSubmenu(menuId) {
            const menu = document.getElementById(menuId);
            const isOpen = menu.classList.contains('show');
            
            // Close all other submenus
            document.querySelectorAll('.collapse.show').forEach(el => {
                if (el.id !== menuId) {
                    el.classList.remove('show');
                }
            });
            
            // Toggle current menu
            if (isOpen) {
                menu.classList.remove('show');
            } else {
                menu.classList.add('show');
            }
        }

        function toggleSidebar() {
            document.querySelector('.admin-sidebar').classList.toggle('show');
        }

        function triggerSync() {
            if (confirm('Are you sure you want to trigger a sync for all active sources?')) {
                // Implementation would go here
                alert('Sync triggered for all active sources');
            }
        }

        function clearCache() {
            if (confirm('Are you sure you want to clear all caches?')) {
                fetch('/admin/settings/ClearCache', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Cache cleared successfully');
                    } else {
                        alert('Failed to clear cache: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error clearing cache: ' + error.message);
                });
            }
        }

        // Auto-refresh dashboard data every 30 seconds
        if (window.location.pathname.includes('/admin/dashboard')) {
            setInterval(function() {
                // Refresh stats and health data
                refreshDashboardData();
            }, 30000);
        }

        function refreshDashboardData() {
            // Implementation would refresh dashboard widgets
        }

        // Initialize tooltips and popovers
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>