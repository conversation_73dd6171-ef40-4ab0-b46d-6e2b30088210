using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NewsSite.Data;
using NewsSite.Models;

namespace NewsSite.Services
{
    public class SourceService : ISourceService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SourceService> _logger;
        private readonly IMemoryCache _cache;
        private const int DefaultCacheExpirationMinutes = 15;
        private const int MaxConsecutiveFailuresBeforeDeactivation = 10;

        public SourceService(ApplicationDbContext context, ILogger<SourceService> logger, IMemoryCache cache)
        {
            _context = context;
            _logger = logger;
            _cache = cache;
        }

        #region Basic CRUD Operations

        public async Task<Source?> GetSourceByIdAsync(int id)
        {
            return await GetByIdAsync(id);
        }

        public async Task<Source?> GetSourceBySlugAsync(string slug)
        {
            try
            {
                var cacheKey = $"source_slug_{slug}";
                if (_cache.TryGetValue(cacheKey, out Source? cached))
                {
                    return cached;
                }

                var source = await _context.Sources
                    .FirstOrDefaultAsync(s => s.Slug == slug && s.IsActive);

                if (source != null)
                {
                    _cache.Set(cacheKey, source, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return source;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving source by slug: {Slug}", slug);
                throw;
            }
        }

        public async Task<Source?> GetByIdAsync(int id)
        {
            try
            {
                var cacheKey = $"source_{id}";
                if (_cache.TryGetValue(cacheKey, out Source? cachedSource))
                {
                    return cachedSource;
                }

                var source = await _context.Sources
                    .Include(s => s.Configuration)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (source != null)
                {
                    _cache.Set(cacheKey, source, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return source;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving source with ID {SourceId}", id);
                throw;
            }
        }

        public async Task<Source?> GetByNameAsync(string name)
        {
            try
            {
                var cacheKey = $"source_name_{name}";
                if (_cache.TryGetValue(cacheKey, out Source? cachedSource))
                {
                    return cachedSource;
                }

                var source = await _context.Sources
                    .Include(s => s.Configuration)
                    .FirstOrDefaultAsync(s => s.Name == name);

                if (source != null)
                {
                    _cache.Set(cacheKey, source, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return source;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving source with name {SourceName}", name);
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetAllSourcesAsync()
        {
            try
            {
                const string cacheKey = "all_sources";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Source>? cachedSources))
                {
                    return cachedSources!;
                }

                var sources = await _context.Sources
                    .Include(s => s.Configuration)
                    .OrderBy(s => s.Name)
                    .ToListAsync();

                _cache.Set(cacheKey, sources, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return sources;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all sources");
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetActiveSourcesAsync()
        {
            try
            {
                const string cacheKey = "active_sources";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Source>? cachedSources))
                {
                    return cachedSources!;
                }

                var sources = await _context.Sources
                    .Include(s => s.Configuration)
                    .Where(s => s.IsActive)
                    .OrderBy(s => s.Name)
                    .ToListAsync();

                _cache.Set(cacheKey, sources, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return sources;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active sources");
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetSourcesByTypeAsync(SourceType sourceType)
        {
            try
            {
                var cacheKey = $"sources_type_{sourceType}";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Source>? cachedSources))
                {
                    return cachedSources!;
                }

                var sources = await _context.Sources
                    .Include(s => s.Configuration)
                    .Where(s => s.Type == sourceType)
                    .OrderBy(s => s.Name)
                    .ToListAsync();

                _cache.Set(cacheKey, sources, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return sources;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sources by type {SourceType}", sourceType);
                throw;
            }
        }

        public async Task<Source> CreateSourceAsync(Source source)
        {
            try
            {
                source.CreatedDate = DateTime.UtcNow;
                if (string.IsNullOrEmpty(source.CreatedBy))
                {
                    source.CreatedBy = "System";
                }

                _context.Sources.Add(source);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created new source with ID {SourceId}", source.Id);
                InvalidateSourceCache();

                return source;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating source");
                throw;
            }
        }

        public async Task<Source> UpdateSourceAsync(Source source)
        {
            try
            {
                var existingSource = await _context.Sources
                    .Include(s => s.Configuration)
                    .FirstOrDefaultAsync(s => s.Id == source.Id);

                if (existingSource == null)
                {
                    throw new ArgumentException("Source not found", nameof(source));
                }

                // Update properties
                existingSource.Name = source.Name;
                existingSource.Type = source.Type;
                existingSource.Url = source.Url;
                existingSource.Description = source.Description;
                existingSource.ApiKey = source.ApiKey;
                existingSource.ApiSecret = source.ApiSecret;
                existingSource.IsActive = source.IsActive;
                existingSource.RequiresAuthentication = source.RequiresAuthentication;
                existingSource.MaxRequestsPerHour = source.MaxRequestsPerHour;
                existingSource.EnableContentFiltering = source.EnableContentFiltering;
                existingSource.ContentFilterKeywords = source.ContentFilterKeywords;
                existingSource.ModifiedDate = DateTime.UtcNow;
                existingSource.ModifiedBy = source.ModifiedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated source with ID {SourceId}", source.Id);
                InvalidateSourceCache();

                return existingSource;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating source with ID {SourceId}", source.Id);
                throw;
            }
        }

        public async Task<bool> DeleteSourceAsync(int id)
        {
            try
            {
                var source = await _context.Sources.FindAsync(id);
                if (source == null)
                {
                    return false;
                }

                // Check if source has articles
                var hasArticles = await _context.Articles.AnyAsync(a => a.SourceId == id);
                if (hasArticles)
                {
                    throw new InvalidOperationException("Cannot delete source that has articles. Delete articles first or assign them to another source.");
                }

                _context.Sources.Remove(source);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted source with ID {SourceId}", id);
                InvalidateSourceCache();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting source with ID {SourceId}", id);
                throw;
            }
        }

        #endregion

        #region Source Configuration Management

        public async Task<SourceConfiguration?> GetSourceConfigurationAsync(int sourceId)
        {
            try
            {
                var cacheKey = $"source_config_{sourceId}";
                if (_cache.TryGetValue(cacheKey, out SourceConfiguration? cachedConfig))
                {
                    return cachedConfig;
                }

                var config = await _context.SourceConfigurations
                    .FirstOrDefaultAsync(sc => sc.SourceId == sourceId);

                if (config != null)
                {
                    _cache.Set(cacheKey, config, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving configuration for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<SourceConfiguration> CreateOrUpdateConfigurationAsync(SourceConfiguration configuration)
        {
            try
            {
                var existingConfig = await _context.SourceConfigurations
                    .FirstOrDefaultAsync(sc => sc.SourceId == configuration.SourceId);

                if (existingConfig == null)
                {
                    configuration.CreatedDate = DateTime.UtcNow;
                    _context.SourceConfigurations.Add(configuration);
                    _logger.LogInformation("Created new configuration for source {SourceId}", configuration.SourceId);
                }
                else
                {
                    // Update existing configuration
                    existingConfig.UpdateIntervalMinutes = configuration.UpdateIntervalMinutes;
                    existingConfig.MaxArticlesPerSync = configuration.MaxArticlesPerSync;
                    existingConfig.ArticleRetentionDays = configuration.ArticleRetentionDays;
                    existingConfig.AutoCategorize = configuration.AutoCategorize;
                    existingConfig.AutoGenerateTags = configuration.AutoGenerateTags;
                    existingConfig.EnableDuplicateDetection = configuration.EnableDuplicateDetection;
                    existingConfig.MaxSummaryLength = configuration.MaxSummaryLength;
                    existingConfig.UseAiSummarization = configuration.UseAiSummarization;
                    existingConfig.Priority = configuration.Priority;
                    existingConfig.ContentFromDate = configuration.ContentFromDate;
                    existingConfig.CustomHeaders = configuration.CustomHeaders;
                    existingConfig.XPathSelectors = configuration.XPathSelectors;
                    existingConfig.MinContentLength = configuration.MinContentLength;
                    existingConfig.RequireImages = configuration.RequireImages;
                    existingConfig.NotifyOnErrors = configuration.NotifyOnErrors;
                    existingConfig.NotifyOnSuccess = configuration.NotifyOnSuccess;
                    existingConfig.NotificationEmails = configuration.NotificationEmails;
                    existingConfig.ModifiedDate = DateTime.UtcNow;

                    configuration = existingConfig;
                    _logger.LogInformation("Updated configuration for source {SourceId}", configuration.SourceId);
                }

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                return configuration;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating/updating configuration for source {SourceId}", configuration.SourceId);
                throw;
            }
        }

        public async Task<bool> DeleteSourceConfigurationAsync(int sourceId)
        {
            try
            {
                var config = await _context.SourceConfigurations
                    .FirstOrDefaultAsync(sc => sc.SourceId == sourceId);

                if (config == null)
                {
                    return false;
                }

                _context.SourceConfigurations.Remove(config);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted configuration for source {SourceId}", sourceId);
                InvalidateSourceCache();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting configuration for source {SourceId}", sourceId);
                throw;
            }
        }

        #endregion

        #region Source Health Monitoring

        public async Task<SourceHealthStatus> GetSourceHealthStatusAsync(int sourceId)
        {
            try
            {
                var source = await GetByIdAsync(sourceId);
                if (source == null)
                {
                    throw new ArgumentException("Source not found", nameof(sourceId));
                }

                return await BuildHealthStatusAsync(source);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting health status for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<IEnumerable<SourceHealthStatus>> GetAllSourceHealthStatusAsync()
        {
            try
            {
                var sources = await GetAllSourcesAsync();
                var healthStatuses = new List<SourceHealthStatus>();

                foreach (var source in sources)
                {
                    healthStatuses.Add(await BuildHealthStatusAsync(source));
                }

                return healthStatuses;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting health status for all sources");
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetUnhealthySourcesAsync()
        {
            try
            {
                var threshold = DateTime.UtcNow.AddHours(-48); // 48 hours without successful sync
                
                return await _context.Sources
                    .Where(s => s.IsActive && 
                               (s.LastSuccessfulSyncDate == null || 
                                s.LastSuccessfulSyncDate < threshold ||
                                s.ConsecutiveFailures >= 5))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unhealthy sources");
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetSourcesRequiringAttentionAsync()
        {
            try
            {
                return await _context.Sources
                    .Where(s => s.IsActive && s.ConsecutiveFailures >= 3)
                    .OrderByDescending(s => s.ConsecutiveFailures)
                    .ThenByDescending(s => s.LastSyncDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sources requiring attention");
                throw;
            }
        }

        #endregion

        #region Sync Tracking and Management

        public async Task<bool> UpdateLastSyncDateAsync(int sourceId, DateTime syncDate, bool wasSuccessful, string? errorMessage = null)
        {
            try
            {
                var source = await _context.Sources.FindAsync(sourceId);
                if (source == null) return false;

                source.LastSyncDate = syncDate;

                if (wasSuccessful)
                {
                    source.LastSuccessfulSyncDate = syncDate;
                    source.ConsecutiveFailures = 0;
                    source.LastErrorMessage = null;
                }
                else
                {
                    source.ConsecutiveFailures++;
                    source.LastErrorMessage = errorMessage;

                    // Auto-deactivate source if too many consecutive failures
                    if (source.ConsecutiveFailures >= MaxConsecutiveFailuresBeforeDeactivation)
                    {
                        source.IsActive = false;
                        _logger.LogWarning("Source {SourceId} automatically deactivated due to {Failures} consecutive failures", 
                                         sourceId, source.ConsecutiveFailures);
                    }
                }

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                _logger.LogInformation("Updated sync status for source {SourceId}: Success={Success}, Failures={Failures}", 
                                     sourceId, wasSuccessful, source.ConsecutiveFailures);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating sync date for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<bool> IncrementFailureCountAsync(int sourceId, string errorMessage)
        {
            try
            {
                return await UpdateLastSyncDateAsync(sourceId, DateTime.UtcNow, false, errorMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing failure count for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<bool> ResetFailureCountAsync(int sourceId)
        {
            try
            {
                var source = await _context.Sources.FindAsync(sourceId);
                if (source == null) return false;

                source.ConsecutiveFailures = 0;
                source.LastErrorMessage = null;

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                _logger.LogInformation("Reset failure count for source {SourceId}", sourceId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting failure count for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetSourcesDueForSyncAsync()
        {
            try
            {
                var sources = await _context.Sources
                    .Include(s => s.Configuration)
                    .Where(s => s.IsActive)
                    .ToListAsync();

                var dueForSync = new List<Source>();
                var now = DateTime.UtcNow;

                foreach (var source in sources)
                {
                    var intervalMinutes = source.Configuration?.UpdateIntervalMinutes ?? 60;
                    var nextSyncTime = (source.LastSyncDate ?? DateTime.MinValue).AddMinutes(intervalMinutes);

                    if (now >= nextSyncTime)
                    {
                        dueForSync.Add(source);
                    }
                }

                return dueForSync.OrderBy(s => s.Configuration?.Priority ?? 5).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sources due for sync");
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetSourcesByPriorityAsync(int minPriority = 1)
        {
            try
            {
                return await _context.Sources
                    .Include(s => s.Configuration)
                    .Where(s => s.IsActive && (s.Configuration == null || s.Configuration.Priority >= minPriority))
                    .OrderByDescending(s => s.Configuration!.Priority)
                    .ThenBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sources by priority");
                throw;
            }
        }

        #endregion

        #region Source Activation and Management

        public async Task<bool> SetSourceActiveStatusAsync(int sourceId, bool isActive)
        {
            try
            {
                var source = await _context.Sources.FindAsync(sourceId);
                if (source == null) return false;

                source.IsActive = isActive;
                source.ModifiedDate = DateTime.UtcNow;

                // Reset failure count when reactivating
                if (isActive)
                {
                    source.ConsecutiveFailures = 0;
                    source.LastErrorMessage = null;
                }

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                _logger.LogInformation("Updated active status for source {SourceId} to {IsActive}", sourceId, isActive);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating active status for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<bool> UpdateSourceRateLimitAsync(int sourceId, int maxRequestsPerHour)
        {
            try
            {
                var source = await _context.Sources.FindAsync(sourceId);
                if (source == null) return false;

                source.MaxRequestsPerHour = Math.Max(1, maxRequestsPerHour);
                source.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                _logger.LogInformation("Updated rate limit for source {SourceId} to {RateLimit}", sourceId, maxRequestsPerHour);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating rate limit for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> sourceIds, bool isActive)
        {
            try
            {
                var sources = await _context.Sources
                    .Where(s => sourceIds.Contains(s.Id))
                    .ToListAsync();

                foreach (var source in sources)
                {
                    source.IsActive = isActive;
                    source.ModifiedDate = DateTime.UtcNow;

                    if (isActive)
                    {
                        source.ConsecutiveFailures = 0;
                        source.LastErrorMessage = null;
                    }
                }

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                _logger.LogInformation("Bulk updated active status to {IsActive} for {Count} sources", isActive, sources.Count);
                return sources.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating source active status");
                throw;
            }
        }

        #endregion

        #region Content Filtering and Configuration

        public async Task<bool> UpdateContentFilteringAsync(int sourceId, bool enableFiltering, string? keywords = null)
        {
            try
            {
                var source = await _context.Sources.FindAsync(sourceId);
                if (source == null) return false;

                source.EnableContentFiltering = enableFiltering;
                source.ContentFilterKeywords = keywords;
                source.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                _logger.LogInformation("Updated content filtering for source {SourceId}: Enabled={Enabled}", sourceId, enableFiltering);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating content filtering for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<string?> GetContentFilterKeywordsAsync(int sourceId)
        {
            try
            {
                var source = await _context.Sources.FindAsync(sourceId);
                return source?.ContentFilterKeywords;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving content filter keywords for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<bool> RequiresAuthenticationAsync(int sourceId)
        {
            try
            {
                var source = await _context.Sources.FindAsync(sourceId);
                return source?.RequiresAuthentication ?? false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking authentication requirement for source {SourceId}", sourceId);
                throw;
            }
        }

        #endregion

        #region Source Statistics and Analytics

        public async Task<SourceWithStats?> GetSourceWithStatsAsync(int sourceId)
        {
            try
            {
                var cacheKey = $"source_stats_{sourceId}";
                if (_cache.TryGetValue(cacheKey, out SourceWithStats? cached))
                {
                    return cached;
                }

                var source = await GetByIdAsync(sourceId);
                if (source == null) return null;

                var stats = await CalculateSourceStatsAsync(sourceId);
                var healthStatus = await BuildHealthStatusAsync(source);

                var result = new SourceWithStats
                {
                    Source = source,
                    TotalArticles = stats.TotalArticles,
                    PublishedArticles = stats.PublishedArticles,
                    ArticlesThisMonth = stats.ArticlesThisMonth,
                    LastArticleDate = stats.LastArticleDate,
                    HealthStatus = healthStatus
                };

                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(10));
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving source with stats for ID {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<IEnumerable<SourceWithStats>> GetSourcesWithStatsAsync()
        {
            try
            {
                const string cacheKey = "sources_with_stats";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<SourceWithStats>? cached))
                {
                    return cached!;
                }

                var sources = await GetAllSourcesAsync();
                var result = new List<SourceWithStats>();

                foreach (var source in sources)
                {
                    var stats = await CalculateSourceStatsAsync(source.Id);
                    var healthStatus = await BuildHealthStatusAsync(source);

                    result.Add(new SourceWithStats
                    {
                        Source = source,
                        TotalArticles = stats.TotalArticles,
                        PublishedArticles = stats.PublishedArticles,
                        ArticlesThisMonth = stats.ArticlesThisMonth,
                        LastArticleDate = stats.LastArticleDate,
                        HealthStatus = healthStatus
                    });
                }

                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(10));
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sources with stats");
                throw;
            }
        }

        public async Task<int> GetArticleCountBySourceAsync(int sourceId, ArticleStatus? status = null)
        {
            try
            {
                var query = _context.Articles.Where(a => a.SourceId == sourceId);

                if (status.HasValue)
                {
                    query = query.Where(a => a.Status == status.Value);
                }

                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting article count for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<Dictionary<int, int>> GetArticleCountsForAllSourcesAsync(ArticleStatus? status = null)
        {
            try
            {
                var query = _context.Articles.AsQueryable();

                if (status.HasValue)
                {
                    query = query.Where(a => a.Status == status.Value);
                }

                return await query
                    .GroupBy(a => a.SourceId)
                    .ToDictionaryAsync(g => g.Key, g => g.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting article counts for all sources");
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetTopSourcesByArticleCountAsync(int count = 10, int days = 30)
        {
            try
            {
                var fromDate = DateTime.UtcNow.AddDays(-days);

                return await _context.Articles
                    .Where(a => a.PublishedDate >= fromDate && a.Status == ArticleStatus.Published)
                    .GroupBy(a => a.SourceId)
                    .Select(g => new { SourceId = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(count)
                    .Join(_context.Sources,
                          ac => ac.SourceId,
                          s => s.Id,
                          (ac, s) => s)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top sources by article count");
                throw;
            }
        }

        #endregion

        #region Source Articles Management

        public async Task<PaginatedResult<Article>> GetSourceArticlesAsync(int sourceId, int page = 1, int pageSize = 20, ArticleStatus? status = null)
        {
            try
            {
                var query = _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => a.SourceId == sourceId);

                if (status.HasValue)
                {
                    query = query.Where(a => a.Status == status.Value);
                }

                var totalCount = await query.CountAsync();
                var skip = (page - 1) * pageSize;

                var articles = await query
                    .OrderByDescending(a => a.PublishedDate)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                return new PaginatedResult<Article>
                {
                    Items = articles,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetLatestSourceArticlesAsync(int sourceId, int count = 10)
        {
            try
            {
                return await _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => a.SourceId == sourceId && a.Status == ArticleStatus.Published)
                    .OrderByDescending(a => a.PublishedDate)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving latest articles for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<DateTime?> GetLastArticleDateAsync(int sourceId)
        {
            try
            {
                return await _context.Articles
                    .Where(a => a.SourceId == sourceId)
                    .MaxAsync(a => (DateTime?)a.PublishedDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last article date for source {SourceId}", sourceId);
                throw;
            }
        }

        #endregion

        #region Validation and Utility

        public async Task<bool> ValidateSourceAsync(Source source)
        {
            try
            {
                // Check required fields
                if (string.IsNullOrWhiteSpace(source.Name) || string.IsNullOrWhiteSpace(source.Url))
                {
                    return false;
                }

                // Check name uniqueness
                if (!await IsSourceNameUniqueAsync(source.Name, source.Id))
                {
                    return false;
                }

                // Check URL uniqueness
                if (!await IsSourceUrlUniqueAsync(source.Url, source.Id))
                {
                    return false;
                }

                // Validate URL format
                if (!Uri.TryCreate(source.Url, UriKind.Absolute, out _))
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating source");
                throw;
            }
        }

        public async Task<bool> IsSourceNameUniqueAsync(string name, int? excludeId = null)
        {
            try
            {
                var query = _context.Sources.Where(s => s.Name == name);

                if (excludeId.HasValue)
                {
                    query = query.Where(s => s.Id != excludeId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking name uniqueness for {Name}", name);
                throw;
            }
        }

        public async Task<bool> IsSourceUrlUniqueAsync(string url, int? excludeId = null)
        {
            try
            {
                var query = _context.Sources.Where(s => s.Url == url);

                if (excludeId.HasValue)
                {
                    query = query.Where(s => s.Id != excludeId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking URL uniqueness for {Url}", url);
                throw;
            }
        }

        public async Task<bool> CanDeleteSourceAsync(int sourceId)
        {
            try
            {
                var hasArticles = await _context.Articles.AnyAsync(a => a.SourceId == sourceId);
                return !hasArticles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if source {SourceId} can be deleted", sourceId);
                throw;
            }
        }

        #endregion

        #region Search and Filtering

        public async Task<IEnumerable<Source>> SearchSourcesAsync(string query)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(query))
                {
                    return await GetAllSourcesAsync();
                }

                return await _context.Sources
                    .Where(s => s.Name.Contains(query) || 
                               (s.Description != null && s.Description.Contains(query)) ||
                               s.Url.Contains(query))
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching sources with query: {Query}", query);
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetSourcesRequiringSyncAsync(int maxHoursSinceLastSync = 24)
        {
            try
            {
                var threshold = DateTime.UtcNow.AddHours(-maxHoursSinceLastSync);

                return await _context.Sources
                    .Where(s => s.IsActive && 
                               (s.LastSyncDate == null || s.LastSyncDate < threshold))
                    .OrderBy(s => s.LastSyncDate ?? DateTime.MinValue)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sources requiring sync");
                throw;
            }
        }

        public async Task<IEnumerable<Source>> GetSourcesWithConsecutiveFailuresAsync(int minFailures = 3)
        {
            try
            {
                return await _context.Sources
                    .Where(s => s.ConsecutiveFailures >= minFailures)
                    .OrderByDescending(s => s.ConsecutiveFailures)
                    .ThenByDescending(s => s.LastSyncDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sources with consecutive failures");
                throw;
            }
        }

        #endregion

        #region Error Handling and Notifications

        public async Task<bool> ShouldNotifyForSourceErrorAsync(int sourceId)
        {
            try
            {
                var config = await GetSourceConfigurationAsync(sourceId);
                return config?.NotifyOnErrors ?? true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking notification settings for source {SourceId}", sourceId);
                throw;
            }
        }

        public async Task<IEnumerable<string>> GetNotificationEmailsForSourceAsync(int sourceId)
        {
            try
            {
                var config = await GetSourceConfigurationAsync(sourceId);
                if (string.IsNullOrEmpty(config?.NotificationEmails))
                {
                    return new List<string>();
                }

                // Parse JSON array of emails (simplified - in real app use JSON deserialization)
                return config.NotificationEmails
                    .Trim('[', ']')
                    .Split(',')
                    .Select(e => e.Trim('"', ' '))
                    .Where(e => !string.IsNullOrEmpty(e))
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notification emails for source {SourceId}", sourceId);
                return new List<string>();
            }
        }

        public async Task<bool> UpdateSourceErrorStatusAsync(int sourceId, string errorMessage, bool shouldNotify = true)
        {
            try
            {
                var source = await _context.Sources.FindAsync(sourceId);
                if (source == null) return false;

                source.ConsecutiveFailures++;
                source.LastErrorMessage = errorMessage;
                source.LastSyncDate = DateTime.UtcNow;

                if (source.ConsecutiveFailures >= MaxConsecutiveFailuresBeforeDeactivation)
                {
                    source.IsActive = false;
                    _logger.LogWarning("Source {SourceId} automatically deactivated due to excessive failures", sourceId);
                }

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                if (shouldNotify && await ShouldNotifyForSourceErrorAsync(sourceId))
                {
                    var emails = await GetNotificationEmailsForSourceAsync(sourceId);
                    if (emails.Any())
                    {
                        _logger.LogInformation("Should send error notification to {Emails} for source {SourceId}", 
                                             string.Join(", ", emails), sourceId);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating error status for source {SourceId}", sourceId);
                throw;
            }
        }

        #endregion

        #region Bulk Operations

        public async Task<int> BulkUpdateSyncSettingsAsync(IEnumerable<int> sourceIds, int updateIntervalMinutes, int maxArticlesPerSync)
        {
            try
            {
                var configs = await _context.SourceConfigurations
                    .Where(sc => sourceIds.Contains(sc.SourceId))
                    .ToListAsync();

                foreach (var config in configs)
                {
                    config.UpdateIntervalMinutes = updateIntervalMinutes;
                    config.MaxArticlesPerSync = maxArticlesPerSync;
                    config.ModifiedDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                _logger.LogInformation("Bulk updated sync settings for {Count} source configurations", configs.Count);
                return configs.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating sync settings");
                throw;
            }
        }

        public async Task<int> BulkResetFailureCountsAsync(IEnumerable<int> sourceIds)
        {
            try
            {
                var sources = await _context.Sources
                    .Where(s => sourceIds.Contains(s.Id))
                    .ToListAsync();

                foreach (var source in sources)
                {
                    source.ConsecutiveFailures = 0;
                    source.LastErrorMessage = null;
                }

                await _context.SaveChangesAsync();
                InvalidateSourceCache();

                _logger.LogInformation("Bulk reset failure counts for {Count} sources", sources.Count);
                return sources.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk resetting failure counts");
                throw;
            }
        }

        #endregion

        #region Additional Methods

        public async Task<Source> CreateAsync(Source source)
        {
            return await CreateSourceAsync(source);
        }

        public async Task<Source> UpdateAsync(Source source)
        {
            return await UpdateSourceAsync(source);
        }

        public async Task<IEnumerable<Source>> GetPrioritySourcesAsync()
        {
            try
            {
                return await _context.Sources
                    .Include(s => s.Configuration)
                    .Where(s => s.IsActive && s.Configuration != null && s.Configuration.Priority >= 7)
                    .OrderByDescending(s => s.Configuration!.Priority)
                    .ThenBy(s => s.LastSyncDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving priority sources");
                throw;
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task<SourceHealthStatus> BuildHealthStatusAsync(Source source)
        {
            var now = DateTime.UtcNow;
            var timeSinceLastSync = source.LastSyncDate.HasValue ? now - source.LastSyncDate.Value : (TimeSpan?)null;
            var isHealthy = source.IsActive && 
                           source.ConsecutiveFailures < 5 && 
                           (source.LastSuccessfulSyncDate?.AddHours(48) > now || source.LastSuccessfulSyncDate == null);

            return new SourceHealthStatus
            {
                Source = source,
                IsHealthy = isHealthy,
                ConsecutiveFailures = source.ConsecutiveFailures,
                LastSuccessfulSync = source.LastSuccessfulSyncDate,
                LastSyncAttempt = source.LastSyncDate,
                LastErrorMessage = source.LastErrorMessage,
                TimeSinceLastSync = timeSinceLastSync,
                RequiresAttention = source.ConsecutiveFailures >= 3 || 
                                   (source.LastSuccessfulSyncDate?.AddHours(72) < now && source.IsActive)
            };
        }

        private async Task<(int TotalArticles, int PublishedArticles, int ArticlesThisMonth, DateTime? LastArticleDate)> CalculateSourceStatsAsync(int sourceId)
        {
            var firstOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1);

            var stats = await _context.Articles
                .Where(a => a.SourceId == sourceId)
                .GroupBy(a => a.SourceId)
                .Select(g => new
                {
                    TotalArticles = g.Count(),
                    PublishedArticles = g.Count(a => a.Status == ArticleStatus.Published),
                    ArticlesThisMonth = g.Count(a => a.PublishedDate >= firstOfMonth),
                    LastArticleDate = g.Max(a => (DateTime?)a.PublishedDate)
                })
                .FirstOrDefaultAsync();

            return (
                stats?.TotalArticles ?? 0,
                stats?.PublishedArticles ?? 0,
                stats?.ArticlesThisMonth ?? 0,
                stats?.LastArticleDate
            );
        }

        private void InvalidateSourceCache()
        {
            var cacheKeys = new[]
            {
                "all_sources",
                "active_sources",
                "sources_with_stats"
            };

            foreach (var key in cacheKeys)
            {
                _cache.Remove(key);
            }
        }

        #endregion
    }
}