using Microsoft.AspNetCore.Mvc;
using NewsSite.Services;
using NewsSite.Models;
using System.ComponentModel.DataAnnotations;

namespace NewsSite.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    public class SearchController : ControllerBase
    {
        private readonly ISearchService _searchService;
        private readonly ICategoryService _categoryService;
        private readonly ISourceService _sourceService;
        private readonly ITagService _tagService;
        private readonly ILogger<SearchController> _logger;

        public SearchController(
            ISearchService searchService,
            ICategoryService categoryService,
            ISourceService sourceService,
            ITagService tagService,
            ILogger<SearchController> logger)
        {
            _searchService = searchService;
            _categoryService = categoryService;
            _sourceService = sourceService;
            _tagService = tagService;
            _logger = logger;
        }

        /// <summary>
        /// Performs a basic search across all content
        /// </summary>
        /// <param name="q">Search query</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="size">Page size (default: 20, max: 100)</param>
        /// <param name="category">Category filter</param>
        /// <param name="source">Source filter</param>
        /// <param name="sort">Sort option (relevance, date, title, author, views)</param>
        /// <param name="desc">Sort descending (default: true)</param>
        /// <returns>Search results with pagination and facets</returns>
        [HttpGet]
        public async Task<IActionResult> Search(
            [FromQuery] string q = "",
            [FromQuery] int page = 1,
            [FromQuery, Range(1, 100)] int size = 20,
            [FromQuery] string? category = null,
            [FromQuery] string? source = null,
            [FromQuery] string? sort = "relevance",
            [FromQuery] bool desc = true)
        {
            try
            {
                if (page < 1) page = 1;
                if (size < 1) size = 20;
                if (size > 100) size = 100;

                var filters = new SearchFilters();
                var sortOptions = new SearchSortOptions
                {
                    SortBy = sort ?? "relevance",
                    SortDescending = desc
                };

                // Apply category filter
                if (!string.IsNullOrEmpty(category))
                {
                    var categoryEntity = await _categoryService.GetCategoryBySlugAsync(category);
                    if (categoryEntity != null)
                        filters.CategoryId = categoryEntity.Id;
                }

                // Apply source filter
                if (!string.IsNullOrEmpty(source))
                {
                    var sourceEntity = await _sourceService.GetSourceBySlugAsync(source);
                    if (sourceEntity != null)
                        filters.SourceId = sourceEntity.Id;
                }

                var results = await _searchService.SearchAsync(q, page, size, filters, sortOptions);

                var response = new
                {
                    query = q,
                    page = results.Page,
                    pageSize = results.PageSize,
                    totalCount = results.TotalCount,
                    totalPages = results.TotalPages,
                    hasMore = results.HasNextPage,
                    searchDuration = results.SearchDuration.TotalMilliseconds,
                    results = results.Results.Select(r => new
                    {
                        id = r.Article.Id,
                        title = r.Article.Title,
                        summary = r.Article.Summary,
                        author = r.Article.Author,
                        publishedDate = r.Article.PublishedDate,
                        url = r.Article.Url,
                        imageUrl = r.Article.ImageUrl,
                        category = new
                        {
                            id = r.Article.Category.Id,
                            name = r.Article.Category.Name,
                            slug = r.Article.Category.Slug
                        },
                        source = new
                        {
                            id = r.Article.Source.Id,
                            name = r.Article.Source.Name,
                            slug = r.Article.Source.Slug
                        },
                        tags = r.Article.ArticleTags.Select(at => new
                        {
                            id = at.Tag.Id,
                            name = at.Tag.Name,
                            slug = at.Tag.Slug
                        }),
                        relevanceScore = r.RelevanceScore,
                        matchedFields = r.MatchedFields,
                        highlights = r.HighlightedSnippets,
                        metadata = new
                        {
                            isFeatured = r.Article.IsFeatured,
                            isBreaking = r.Article.IsBreaking,
                            isTrending = r.Article.IsTrending,
                            viewCount = r.Article.Metadata?.ViewCount ?? 0,
                            readingTime = r.Article.Metadata?.ReadingTimeMinutes ?? 0,
                            qualityScore = r.Article.Metadata?.QualityScore ?? 0
                        }
                    }),
                    facets = results.Facets
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing API search for query: {Query}", q);
                return StatusCode(500, new { error = "Internal server error", message = ex.Message });
            }
        }

        /// <summary>
        /// Get search suggestions for autocomplete
        /// </summary>
        /// <param name="q">Partial query</param>
        /// <param name="limit">Maximum suggestions (default: 10)</param>
        /// <returns>List of search suggestions</returns>
        [HttpGet("suggestions")]
        public async Task<IActionResult> GetSuggestions(
            [FromQuery] string q = "",
            [FromQuery, Range(1, 20)] int limit = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q) || q.Length < 2)
                {
                    return Ok(new { suggestions = new List<object>() });
                }

                var suggestions = await _searchService.GetSearchSuggestionsAsync(q, limit);
                
                var response = new
                {
                    query = q,
                    suggestions = suggestions.Select(s => new
                    {
                        text = s.Text,
                        type = s.Type,
                        count = s.Count,
                        relevance = s.Relevance
                    })
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search suggestions for query: {Query}", q);
                return StatusCode(500, new { error = "Failed to get suggestions" });
            }
        }

        /// <summary>
        /// Get autocomplete suggestions for query text only
        /// </summary>
        /// <param name="q">Partial query</param>
        /// <param name="limit">Maximum suggestions (default: 8)</param>
        /// <returns>Array of suggestion strings</returns>
        [HttpGet("autocomplete")]
        public async Task<IActionResult> GetAutoComplete(
            [FromQuery] string q = "",
            [FromQuery, Range(1, 15)] int limit = 8)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q) || q.Length < 2)
                {
                    return Ok(new string[0]);
                }

                var suggestions = await _searchService.GetQueryAutoCompleteAsync(q, limit);
                return Ok(suggestions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting autocomplete for query: {Query}", q);
                return StatusCode(500, new { error = "Failed to get autocomplete" });
            }
        }

        /// <summary>
        /// Get search facets for filtering
        /// </summary>
        /// <param name="q">Search query</param>
        /// <returns>Available facets with counts</returns>
        [HttpGet("facets")]
        public async Task<IActionResult> GetFacets([FromQuery] string q = "")
        {
            try
            {
                var facets = await _searchService.GetSearchFacetsAsync(q, null);
                return Ok(facets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search facets for query: {Query}", q);
                return StatusCode(500, new { error = "Failed to get facets" });
            }
        }

        /// <summary>
        /// Get popular and trending searches
        /// </summary>
        /// <param name="type">Type of searches (popular, trending)</param>
        /// <param name="limit">Maximum results (default: 10)</param>
        /// <param name="days">Time period in days (default: 30)</param>
        /// <returns>List of popular/trending searches</returns>
        [HttpGet("popular")]
        public async Task<IActionResult> GetPopularSearches(
            [FromQuery] string type = "popular",
            [FromQuery, Range(1, 50)] int limit = 10,
            [FromQuery, Range(1, 365)] int days = 30)
        {
            try
            {
                var response = type.ToLowerInvariant() switch
                {
                    "trending" => new
                    {
                        type = "trending",
                        period = $"{days} days",
                        searches = await _searchService.GetTrendingQueriesAsync(limit, days)
                    },
                    _ => new
                    {
                        type = "popular",
                        period = $"{days} days",
                        searches = (await _searchService.GetPopularSearchesAsync(limit, days))
                                  .Select(s => s.Text)
                    }
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting popular searches");
                return StatusCode(500, new { error = "Failed to get popular searches" });
            }
        }

        /// <summary>
        /// Perform advanced search with complex filters
        /// </summary>
        /// <param name="request">Advanced search request</param>
        /// <returns>Search results with applied filters</returns>
        [HttpPost("advanced")]
        public async Task<IActionResult> AdvancedSearch([FromBody] ApiAdvancedSearchRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var filters = MapToSearchFilters(request);
                var sortOptions = MapToSortOptions(request);

                var results = await _searchService.AdvancedSearchAsync(
                    request.Query ?? "", 
                    filters, 
                    sortOptions, 
                    request.Page, 
                    request.PageSize);

                var response = new
                {
                    query = request.Query,
                    filters = request,
                    page = results.Page,
                    pageSize = results.PageSize,
                    totalCount = results.TotalCount,
                    totalPages = results.TotalPages,
                    hasMore = results.HasNextPage,
                    searchDuration = results.SearchDuration.TotalMilliseconds,
                    results = results.Results.Select(r => new
                    {
                        id = r.Article.Id,
                        title = r.Article.Title,
                        summary = r.Article.Summary,
                        author = r.Article.Author,
                        publishedDate = r.Article.PublishedDate,
                        url = r.Article.Url,
                        imageUrl = r.Article.ImageUrl,
                        category = new
                        {
                            id = r.Article.Category.Id,
                            name = r.Article.Category.Name,
                            slug = r.Article.Category.Slug
                        },
                        source = new
                        {
                            id = r.Article.Source.Id,
                            name = r.Article.Source.Name,
                            slug = r.Article.Source.Slug
                        },
                        tags = r.Article.ArticleTags.Select(at => new
                        {
                            id = at.Tag.Id,
                            name = at.Tag.Name,
                            slug = at.Tag.Slug
                        }),
                        relevanceScore = r.RelevanceScore,
                        matchedFields = r.MatchedFields,
                        highlights = r.HighlightedSnippets,
                        metadata = new
                        {
                            isFeatured = r.Article.IsFeatured,
                            isBreaking = r.Article.IsBreaking,
                            isTrending = r.Article.IsTrending,
                            viewCount = r.Article.Metadata?.ViewCount ?? 0,
                            readingTime = r.Article.Metadata?.ReadingTimeMinutes ?? 0,
                            qualityScore = r.Article.Metadata?.QualityScore ?? 0
                        }
                    }),
                    facets = results.Facets
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing advanced search");
                return StatusCode(500, new { error = "Internal server error", message = ex.Message });
            }
        }

        /// <summary>
        /// Find similar articles to a given article
        /// </summary>
        /// <param name="articleId">Article ID</param>
        /// <param name="limit">Maximum similar articles (default: 10)</param>
        /// <returns>List of similar articles</returns>
        [HttpGet("similar/{articleId:int}")]
        public async Task<IActionResult> GetSimilarArticles(
            int articleId,
            [FromQuery, Range(1, 20)] int limit = 10)
        {
            try
            {
                var similarArticles = await _searchService.FindSimilarArticlesAsync(articleId, limit);
                
                var response = new
                {
                    articleId = articleId,
                    count = similarArticles.Count(),
                    articles = similarArticles.Select(a => new
                    {
                        id = a.Id,
                        title = a.Title,
                        summary = a.Summary,
                        author = a.Author,
                        publishedDate = a.PublishedDate,
                        url = a.Url,
                        imageUrl = a.ImageUrl,
                        category = new
                        {
                            id = a.Category.Id,
                            name = a.Category.Name,
                            slug = a.Category.Slug
                        },
                        source = new
                        {
                            id = a.Source.Id,
                            name = a.Source.Name,
                            slug = a.Source.Slug
                        },
                        tags = a.ArticleTags.Select(at => new
                        {
                            id = at.Tag.Id,
                            name = at.Tag.Name,
                            slug = at.Tag.Slug
                        })
                    })
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting similar articles for article {ArticleId}", articleId);
                return StatusCode(500, new { error = "Failed to get similar articles" });
            }
        }

        /// <summary>
        /// Record a search click for analytics
        /// </summary>
        /// <param name="request">Search click data</param>
        /// <returns>Success status</returns>
        [HttpPost("click")]
        public async Task<IActionResult> RecordSearchClick([FromBody] SearchClickRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _searchService.RecordSearchClickAsync(request.Query, request.ArticleId);
                
                return Ok(new { success = success });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording search click");
                return StatusCode(500, new { error = "Failed to record click" });
            }
        }

        /// <summary>
        /// Get search analytics and statistics
        /// </summary>
        /// <param name="days">Number of days to include (default: 30)</param>
        /// <returns>Search statistics</returns>
        [HttpGet("analytics")]
        public async Task<IActionResult> GetSearchAnalytics([FromQuery, Range(1, 365)] int days = 30)
        {
            try
            {
                var fromDate = DateTime.UtcNow.AddDays(-days);
                var toDate = DateTime.UtcNow;
                
                var stats = await _searchService.GetSearchStatsAsync(fromDate, toDate);
                var performance = await _searchService.GetSearchPerformanceReportAsync(days);
                
                var response = new
                {
                    period = new { from = fromDate, to = toDate, days = days },
                    statistics = stats,
                    performance = performance
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search analytics");
                return StatusCode(500, new { error = "Failed to get analytics" });
            }
        }

        #region Helper Methods

        private SearchFilters MapToSearchFilters(ApiAdvancedSearchRequest request)
        {
            var filters = new SearchFilters();

            if (request.CategoryIds?.Any() == true)
                filters.CategoryId = request.CategoryIds.First();

            if (request.SourceIds?.Any() == true)
                filters.SourceId = request.SourceIds.First();

            if (request.TagIds?.Any() == true)
                filters.TagIds = request.TagIds.ToList();

            if (!string.IsNullOrEmpty(request.Author))
                filters.Author = request.Author;

            if (request.FromDate.HasValue)
                filters.FromDate = request.FromDate.Value;

            if (request.ToDate.HasValue)
                filters.ToDate = request.ToDate.Value;

            if (request.IsFeatured.HasValue)
                filters.IsFeatured = request.IsFeatured.Value;

            if (request.IsBreaking.HasValue)
                filters.IsBreaking = request.IsBreaking.Value;

            if (request.IsTrending.HasValue)
                filters.IsTrending = request.IsTrending.Value;

            if (request.HasImages.HasValue)
                filters.HasImages = request.HasImages.Value;

            if (request.HasVideo.HasValue)
                filters.HasVideo = request.HasVideo.Value;

            if (request.MinReadingTime.HasValue)
                filters.MinReadingTime = request.MinReadingTime.Value;

            if (request.MaxReadingTime.HasValue)
                filters.MaxReadingTime = request.MaxReadingTime.Value;

            if (request.MinQualityScore.HasValue)
                filters.MinQualityScore = request.MinQualityScore.Value;

            return filters;
        }

        private SearchSortOptions MapToSortOptions(ApiAdvancedSearchRequest request)
        {
            return new SearchSortOptions
            {
                SortBy = request.SortBy ?? "Relevance",
                SortDescending = request.SortDescending ?? true,
                BoostFeatured = request.BoostFeatured ?? true,
                BoostRecent = request.BoostRecent ?? true
            };
        }

        #endregion
    }

    #region API Request Models

    public class ApiAdvancedSearchRequest
    {
        public string? Query { get; set; }
        
        [Range(1, int.MaxValue)]
        public int Page { get; set; } = 1;
        
        [Range(1, 100)]
        public int PageSize { get; set; } = 20;
        
        public List<int>? CategoryIds { get; set; }
        public List<int>? SourceIds { get; set; }
        public List<int>? TagIds { get; set; }
        
        [StringLength(100)]
        public string? Author { get; set; }
        
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool? IsFeatured { get; set; }
        public bool? IsBreaking { get; set; }
        public bool? IsTrending { get; set; }
        public bool? HasImages { get; set; }
        public bool? HasVideo { get; set; }
        
        [Range(0, 1000)]
        public int? MinReadingTime { get; set; }
        
        [Range(0, 1000)]
        public int? MaxReadingTime { get; set; }
        
        [Range(0, 1)]
        public decimal? MinQualityScore { get; set; }
        
        [StringLength(50)]
        public string? SortBy { get; set; }
        
        public bool? SortDescending { get; set; }
        public bool? BoostFeatured { get; set; }
        public bool? BoostRecent { get; set; }
    }

    public class SearchClickRequest
    {
        [Required]
        [StringLength(500)]
        public string Query { get; set; } = string.Empty;
        
        [Required]
        [Range(1, int.MaxValue)]
        public int ArticleId { get; set; }
    }

    #endregion
}