using Microsoft.AspNetCore.Mvc;
using NewsSite.Models;
using NewsSite.Services;

namespace NewsSite.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    public class CategoriesController : ControllerBase
    {
        private readonly ICategoryService _categoryService;
        private readonly IContentService _contentService;
        private readonly ILogger<CategoriesController> _logger;

        public CategoriesController(
            ICategoryService categoryService,
            IContentService contentService,
            ILogger<CategoriesController> logger)
        {
            _categoryService = categoryService;
            _contentService = contentService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetCategories([FromQuery] bool includeStats = true)
        {
            try
            {
                if (includeStats)
                {
                    var categoriesWithStats = await _categoryService.GetActiveCategoriesWithStatsAsync();
                    var response = categoriesWithStats.Select(c => new CategoryDto
                    {
                        Id = c.Category.Id,
                        Name = c.Category.Name,
                        Slug = c.Category.Slug,
                        Description = c.Category.Description,
                        Color = c.Category.Color,
                        Icon = c.Category.Icon,
                        ArticleCount = c.ArticleCount,
                        PublishedArticleCount = c.PublishedArticleCount,
                        IsActive = c.Category.IsActive,
                        DisplayOrder = c.Category.DisplayOrder
                    }).OrderBy(c => c.DisplayOrder);

                    return Ok(response);
                }
                else
                {
                    var categories = await _categoryService.GetActiveCategoriesAsync();
                    var response = categories.Select(c => new CategoryDto
                    {
                        Id = c.Id,
                        Name = c.Name,
                        Slug = c.Slug,
                        Description = c.Description,
                        Color = c.Color,
                        Icon = c.Icon,
                        IsActive = c.IsActive,
                        DisplayOrder = c.DisplayOrder
                    }).OrderBy(c => c.DisplayOrder);

                    return Ok(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving categories");
                return StatusCode(500, new { error = "Unable to retrieve categories" });
            }
        }

        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetById(int id, [FromQuery] bool includeStats = true)
        {
            try
            {
                if (includeStats)
                {
                    var categoryWithStats = await _categoryService.GetCategoryWithStatsAsync(id);
                    if (categoryWithStats == null)
                    {
                        return NotFound(new { error = "Category not found" });
                    }

                    var response = new CategoryDto
                    {
                        Id = categoryWithStats.Category.Id,
                        Name = categoryWithStats.Category.Name,
                        Slug = categoryWithStats.Category.Slug,
                        Description = categoryWithStats.Category.Description,
                        Color = categoryWithStats.Category.Color,
                        Icon = categoryWithStats.Category.Icon,
                        ArticleCount = categoryWithStats.ArticleCount,
                        PublishedArticleCount = categoryWithStats.PublishedArticleCount,
                        IsActive = categoryWithStats.Category.IsActive,
                        DisplayOrder = categoryWithStats.Category.DisplayOrder
                    };

                    return Ok(response);
                }
                else
                {
                    var category = await _categoryService.GetByIdAsync(id);
                    if (category == null)
                    {
                        return NotFound(new { error = "Category not found" });
                    }

                    var response = new CategoryDto
                    {
                        Id = category.Id,
                        Name = category.Name,
                        Slug = category.Slug,
                        Description = category.Description,
                        Color = category.Color,
                        Icon = category.Icon,
                        IsActive = category.IsActive,
                        DisplayOrder = category.DisplayOrder
                    };

                    return Ok(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category {CategoryId}", id);
                return StatusCode(500, new { error = "Unable to retrieve category" });
            }
        }

        [HttpGet("{slug}")]
        public async Task<IActionResult> GetBySlug(string slug, [FromQuery] bool includeStats = true)
        {
            try
            {
                if (includeStats)
                {
                    var categoryWithStats = await _categoryService.GetCategoryWithStatsBySlugAsync(slug);
                    if (categoryWithStats == null)
                    {
                        return NotFound(new { error = "Category not found" });
                    }

                    var response = new CategoryDto
                    {
                        Id = categoryWithStats.Category.Id,
                        Name = categoryWithStats.Category.Name,
                        Slug = categoryWithStats.Category.Slug,
                        Description = categoryWithStats.Category.Description,
                        Color = categoryWithStats.Category.Color,
                        Icon = categoryWithStats.Category.Icon,
                        ArticleCount = categoryWithStats.ArticleCount,
                        PublishedArticleCount = categoryWithStats.PublishedArticleCount,
                        IsActive = categoryWithStats.Category.IsActive,
                        DisplayOrder = categoryWithStats.Category.DisplayOrder
                    };

                    return Ok(response);
                }
                else
                {
                    var category = await _categoryService.GetBySlugAsync(slug);
                    if (category == null)
                    {
                        return NotFound(new { error = "Category not found" });
                    }

                    var response = new CategoryDto
                    {
                        Id = category.Id,
                        Name = category.Name,
                        Slug = category.Slug,
                        Description = category.Description,
                        Color = category.Color,
                        Icon = category.Icon,
                        IsActive = category.IsActive,
                        DisplayOrder = category.DisplayOrder
                    };

                    return Ok(response);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category by slug {Slug}", slug);
                return StatusCode(500, new { error = "Unable to retrieve category" });
            }
        }

        [HttpGet("{slug}/articles")]
        public async Task<IActionResult> GetCategoryArticles(
            string slug,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string sortBy = "PublishedDate",
            [FromQuery] bool sortDesc = true)
        {
            try
            {
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 20;

                var category = await _categoryService.GetBySlugAsync(slug);
                if (category == null)
                {
                    return NotFound(new { error = "Category not found" });
                }

                var result = await _categoryService.GetCategoryArticlesAsync(
                    category.Id, page, pageSize, ArticleStatus.Published);

                var response = new PaginatedArticleResult
                {
                    Articles = MapToDto(result.Items),
                    TotalCount = result.TotalCount,
                    Page = result.Page,
                    PageSize = result.PageSize
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for category {Slug}", slug);
                return StatusCode(500, new { error = "Unable to retrieve category articles" });
            }
        }

        [HttpGet("{id:int}/articles")]
        public async Task<IActionResult> GetCategoryArticlesById(
            int id,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string sortBy = "PublishedDate",
            [FromQuery] bool sortDesc = true)
        {
            try
            {
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 20;

                var result = await _categoryService.GetCategoryArticlesAsync(
                    id, page, pageSize, ArticleStatus.Published);

                var response = new PaginatedArticleResult
                {
                    Articles = MapToDto(result.Items),
                    TotalCount = result.TotalCount,
                    Page = result.Page,
                    PageSize = result.PageSize
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for category {CategoryId}", id);
                return StatusCode(500, new { error = "Unable to retrieve category articles" });
            }
        }

        [HttpGet("{slug}/articles/latest")]
        public async Task<IActionResult> GetLatestCategoryArticles(string slug, [FromQuery] int count = 10)
        {
            try
            {
                if (count < 1 || count > 50) count = 10;

                var category = await _categoryService.GetBySlugAsync(slug);
                if (category == null)
                {
                    return NotFound(new { error = "Category not found" });
                }

                var articles = await _categoryService.GetLatestCategoryArticlesAsync(category.Id, count);
                var response = MapToDto(articles);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving latest articles for category {Slug}", slug);
                return StatusCode(500, new { error = "Unable to retrieve latest category articles" });
            }
        }

        [HttpGet("{slug}/articles/featured")]
        public async Task<IActionResult> GetFeaturedCategoryArticles(string slug, [FromQuery] int count = 5)
        {
            try
            {
                if (count < 1 || count > 20) count = 5;

                var category = await _categoryService.GetBySlugAsync(slug);
                if (category == null)
                {
                    return NotFound(new { error = "Category not found" });
                }

                var articles = await _categoryService.GetFeaturedCategoryArticlesAsync(category.Id, count);
                var response = MapToDto(articles);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured articles for category {Slug}", slug);
                return StatusCode(500, new { error = "Unable to retrieve featured category articles" });
            }
        }

        [HttpGet("navigation")]
        public async Task<IActionResult> GetNavigationCategories()
        {
            try
            {
                var categories = await _categoryService.GetCategoriesForNavigationAsync();
                var response = categories.Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Slug = c.Slug,
                    Description = c.Description,
                    Color = c.Color,
                    Icon = c.Icon,
                    IsActive = c.IsActive,
                    DisplayOrder = c.DisplayOrder
                }).OrderBy(c => c.DisplayOrder);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving navigation categories");
                return StatusCode(500, new { error = "Unable to retrieve navigation categories" });
            }
        }

        [HttpGet("top")]
        public async Task<IActionResult> GetTopCategories([FromQuery] int count = 10, [FromQuery] int days = 30)
        {
            try
            {
                if (count < 1 || count > 50) count = 10;
                if (days < 1 || days > 365) days = 30;

                var categories = await _categoryService.GetTopCategoriesByArticleCountAsync(count, days);
                var response = categories.Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Slug = c.Slug,
                    Description = c.Description,
                    Color = c.Color,
                    Icon = c.Icon,
                    IsActive = c.IsActive,
                    DisplayOrder = c.DisplayOrder
                });

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving top categories");
                return StatusCode(500, new { error = "Unable to retrieve top categories" });
            }
        }

        private static IEnumerable<ArticleDto> MapToDto(IEnumerable<Article> articles)
        {
            return articles.Select(a => new ArticleDto
            {
                Id = a.Id,
                Title = a.Title,
                Slug = a.Slug,
                Summary = a.Summary,
                ImageUrl = a.ImageUrl,
                ImageAlt = a.ImageAlt,
                Author = a.Author,
                PublishedDate = a.PublishedDate,
                IsFeatured = a.IsFeatured,
                IsBreaking = a.IsBreaking,
                IsTrending = a.IsTrending,
                CategoryName = a.Category?.Name ?? "Uncategorized",
                CategorySlug = a.Category?.Slug ?? "uncategorized",
                CategoryColor = a.Category?.Color,
                CategoryIcon = a.Category?.Icon,
                SourceName = a.Source?.Name ?? "Unknown Source",
                ViewCount = a.ViewCount,
                ReadingTimeMinutes = a.Metadata?.ReadingTimeMinutes ?? CalculateReadingTime(a.Summary),
                TimeAgo = GetTimeAgo(a.PublishedDate)
            });
        }

        private static int CalculateReadingTime(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return 1;

            var wordCount = text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
            const int wordsPerMinute = 200; // Average reading speed
            return Math.Max(1, (int)Math.Ceiling((double)wordCount / wordsPerMinute));
        }

        private static string GetTimeAgo(DateTime publishedDate)
        {
            var timeSpan = DateTime.UtcNow - publishedDate;

            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} minutes ago";
            
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} hours ago";
            
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} days ago";
            
            if (timeSpan.TotalDays < 30)
                return $"{(int)(timeSpan.TotalDays / 7)} weeks ago";
            
            if (timeSpan.TotalDays < 365)
                return $"{(int)(timeSpan.TotalDays / 30)} months ago";
            
            return $"{(int)(timeSpan.TotalDays / 365)} years ago";
        }
    }
}