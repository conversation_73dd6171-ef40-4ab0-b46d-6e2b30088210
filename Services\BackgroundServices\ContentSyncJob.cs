using NewsSite.Data;
using Microsoft.EntityFrameworkCore;

namespace NewsSite.Services.BackgroundServices
{
    public class ContentSyncJob
    {
        private readonly IContentAggregationService _contentAggregationService;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ContentSyncJob> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public ContentSyncJob(
            IContentAggregationService contentAggregationService,
            ApplicationDbContext context,
            ILogger<ContentSyncJob> logger,
            IServiceScopeFactory serviceScopeFactory)
        {
            _contentAggregationService = contentAggregationService;
            _context = context;
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
        }

        /// <summary>
        /// Main content synchronization job
        /// </summary>
        public async Task ExecuteAsync()
        {
            try
            {
                _logger.LogInformation("Starting scheduled content synchronization");

                await _contentAggregationService.AggregateAllContentAsync();

                _logger.LogInformation("Content synchronization completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during scheduled content synchronization");
                throw;
            }
        }

        /// <summary>
        /// Cleanup old content based on retention policies
        /// </summary>
        public async Task CleanupOldContentAsync()
        {
            try
            {
                _logger.LogInformation("Starting content cleanup job");

                using var scope = _serviceScopeFactory.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                // Delete articles older than 6 months that haven't been viewed recently
                var cutoffDate = DateTime.UtcNow.AddMonths(-6);
                var oldArticles = await context.Articles
                    .Where(a => a.PublishedDate < cutoffDate && 
                               (a.LastViewedDate == null || a.LastViewedDate < DateTime.UtcNow.AddMonths(-1)))
                    .ToListAsync();

                if (oldArticles.Any())
                {
                    context.Articles.RemoveRange(oldArticles);
                    await context.SaveChangesAsync();

                    _logger.LogInformation("Cleaned up {Count} old articles", oldArticles.Count);
                }

                // Clean up orphaned tags (tags not associated with any articles)
                var orphanedTags = await context.Tags
                    .Where(t => !t.ArticleTags.Any())
                    .ToListAsync();

                if (orphanedTags.Any())
                {
                    context.Tags.RemoveRange(orphanedTags);
                    await context.SaveChangesAsync();

                    _logger.LogInformation("Cleaned up {Count} orphaned tags", orphanedTags.Count);
                }

                // Clean up old content metadata
                var oldMetadata = await context.ContentMetadata
                    .Where(cm => !context.Articles.Any(a => a.Id == cm.ArticleId))
                    .ToListAsync();

                if (oldMetadata.Any())
                {
                    context.ContentMetadata.RemoveRange(oldMetadata);
                    await context.SaveChangesAsync();

                    _logger.LogInformation("Cleaned up {Count} orphaned metadata records", oldMetadata.Count);
                }

                _logger.LogInformation("Content cleanup completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during content cleanup");
                throw;
            }
        }

        /// <summary>
        /// Calculate analytics data for trending content, popular sources, etc.
        /// </summary>
        public async Task CalculateAnalyticsAsync()
        {
            try
            {
                _logger.LogInformation("Starting analytics calculation job");

                using var scope = _serviceScopeFactory.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                // Update view counts and trending scores
                var recentArticles = await context.Articles
                    .Where(a => a.PublishedDate > DateTime.UtcNow.AddDays(-30))
                    .ToListAsync();

                foreach (var article in recentArticles)
                {
                    // Calculate trending score based on views, recency, and engagement
                    var daysSincePublished = (DateTime.UtcNow - article.PublishedDate).TotalDays;
                    var recencyFactor = Math.Max(0, 30 - daysSincePublished) / 30.0;
                    var viewFactor = Math.Log10(Math.Max(1, article.ViewCount)) / 10.0;

                    article.TrendingScore = (decimal)(recencyFactor * 0.6 + viewFactor * 0.4);
                }

                await context.SaveChangesAsync();

                // Update source statistics
                var sources = await context.Sources.Include(s => s.Articles).ToListAsync();
                foreach (var source in sources)
                {
                    var recentArticleCount = source.Articles.Count(a => a.PublishedDate > DateTime.UtcNow.AddDays(-7));
                    var avgViews = source.Articles.Any() ? source.Articles.Average(a => a.ViewCount) : 0;

                    // Update source configuration with statistics
                    if (source.Configuration != null)
                    {
                        source.Configuration.RecentArticleCount = recentArticleCount;
                        source.Configuration.AverageViews = (int)avgViews;
                    }
                }

                await context.SaveChangesAsync();

                _logger.LogInformation("Analytics calculation completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during analytics calculation");
                throw;
            }
        }

        /// <summary>
        /// Refresh content for specific high-priority sources
        /// </summary>
        public async Task RefreshPrioritySourcesAsync()
        {
            try
            {
                _logger.LogInformation("Starting priority sources refresh");

                using var scope = _serviceScopeFactory.CreateScope();
                var sourceService = scope.ServiceProvider.GetRequiredService<ISourceService>();

                var prioritySources = await sourceService.GetPrioritySourcesAsync();

                foreach (var source in prioritySources)
                {
                    await _contentAggregationService.AggregateContentFromSourceAsync(source.Id);
                    
                    // Add small delay between sources to avoid overwhelming APIs
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }

                _logger.LogInformation("Priority sources refresh completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during priority sources refresh");
                throw;
            }
        }
    }
}