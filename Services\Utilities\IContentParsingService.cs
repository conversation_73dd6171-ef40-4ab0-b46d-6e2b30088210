namespace NewsSite.Services.Utilities
{
    public interface IContentParsingService
    {
        /// <summary>
        /// Extracts clean text from HTML content
        /// </summary>
        Task<string> ExtractTextFromHtmlAsync(string htmlContent);

        /// <summary>
        /// Sanitizes HTML content by removing dangerous elements and scripts
        /// </summary>
        Task<string> SanitizeHtmlAsync(string htmlContent);

        /// <summary>
        /// Generates a summary from long text content
        /// </summary>
        Task<string> GenerateSummaryAsync(string content, int maxLength = 300);

        /// <summary>
        /// Extracts meta information from HTML (title, description, keywords, etc.)
        /// </summary>
        Task<HtmlMetaInfo> ExtractMetaInfoAsync(string htmlContent);

        /// <summary>
        /// Extracts all images from HTML content
        /// </summary>
        Task<List<string>> ExtractImagesAsync(string htmlContent);

        /// <summary>
        /// Extracts all links from HTML content
        /// </summary>
        Task<List<string>> ExtractLinksAsync(string htmlContent);

        /// <summary>
        /// Converts relative URLs to absolute URLs
        /// </summary>
        Task<string> ConvertRelativeUrlsAsync(string htmlContent, string baseUrl);

        /// <summary>
        /// Removes excessive whitespace and normalizes text
        /// </summary>
        Task<string> NormalizeTextAsync(string text);
    }

    public class HtmlMetaInfo
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Keywords { get; set; } = "";
        public string Author { get; set; } = "";
        public string Canonical { get; set; } = "";
        public string OgTitle { get; set; } = "";
        public string OgDescription { get; set; } = "";
        public string OgImage { get; set; } = "";
        public string TwitterTitle { get; set; } = "";
        public string TwitterDescription { get; set; } = "";
        public string TwitterImage { get; set; } = "";
        public DateTime? PublishedTime { get; set; }
        public DateTime? ModifiedTime { get; set; }
        public string Language { get; set; } = "";
    }
}