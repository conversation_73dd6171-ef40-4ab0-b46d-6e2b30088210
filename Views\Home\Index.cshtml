@model NewsSite.Models.HomeViewModel
@{
    ViewData["Title"] = "AI Frontiers - Latest AI News & Insights";
}

<!-- Hero Section -->
<section class="hero-section">
    <div class="container-fluid">
        <div class="hero-content">
            <h1 class="hero-title">AI Frontiers</h1>
            <p class="hero-subtitle">
                Your gateway to the latest breakthroughs in artificial intelligence. 
                Stay ahead with curated news, research, and innovations from the AI frontier.
            </p>
            <div class="d-flex gap-3 justify-content-center">
                <a href="#latest-articles" class="btn btn-primary btn-lg">
                    <i class="fas fa-rocket me-2"></i>Explore Latest
                </a>
                <a href="#categories" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-layer-group me-2"></i>Browse Categories
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Category Navigation -->
<section class="py-5" id="categories">
    <div class="container-fluid">
        <div class="text-center mb-5">
            <h2 class="gradient-text">Explore AI Categories</h2>
            <p class="text-secondary">Discover content tailored to your interests</p>
        </div>
        
        <div class="category-nav">
            <nav class="nav justify-content-center">
                <a class="nav-link active" href="#" data-category="all">
                    <i class="fas fa-globe"></i>
                    All Categories
                </a>
                <a class="nav-link" href="#" data-category="breaking">
                    <i class="fas fa-bolt" style="color: #ff4444;"></i>
                    Breaking News
                </a>
                @if (Model?.Categories?.Any() == true)
                {
                    @foreach (var category in Model.Categories.OrderBy(c => c.DisplayOrder).Take(6))
                    {
                        <a class="nav-link" href="#" data-category="@category.Slug">
                            @if (!string.IsNullOrEmpty(category.Icon))
                            {
                                <i class="@category.Icon" style="color: @(category.Color ?? "#6c757d");"></i>
                            }
                            else
                            {
                                <i class="fas fa-tag" style="color: @(category.Color ?? "#6c757d");"></i>
                            }
                            @category.Name
                            @if (category.PublishedArticleCount > 0)
                            {
                                <span class="badge badge-sm ms-1">@category.PublishedArticleCount</span>
                            }
                        </a>
                    }
                }
                else
                {
                    <!-- Fallback categories if no data is available -->
                    <a class="nav-link" href="#" data-category="research">
                        <i class="fas fa-flask" style="color: #4CAF50;"></i>
                        Research
                    </a>
                    <a class="nav-link" href="#" data-category="tools">
                        <i class="fas fa-tools" style="color: #9C27B0;"></i>
                        AI Tools
                    </a>
                }
            </nav>
        </div>
    </div>
</section>

<!-- Featured Articles Section -->
<section class="py-5 bg-dark-secondary" id="featured">
    <div class="container-fluid">
        <div class="row align-items-center mb-5">
            <div class="col">
                <h2 class="gradient-text">Featured Stories</h2>
                <p class="text-secondary">Hand-picked highlights from the AI world</p>
            </div>
            <div class="col-auto">
                <a href="#" class="btn btn-outline-primary">
                    <i class="fas fa-star me-2"></i>View All Featured
                </a>
            </div>
        </div>
        
        @if (ViewBag.HasError == true)
        {
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                @ViewBag.ErrorMessage
            </div>
        }

        @if (Model?.FeaturedArticles?.Any() == true)
        {
            <div class="row g-4">
                @{
                    var featuredArticles = Model.FeaturedArticles.Take(3).ToList();
                    var mainFeatured = featuredArticles.FirstOrDefault();
                    var otherFeatured = featuredArticles.Skip(1).Take(2).ToList();
                }

                @if (mainFeatured != null)
                {
                    <!-- Main Featured Article -->
                    <div class="col-lg-6">
                        <div class="card article-card h-100 glow">
                            <img src="@(mainFeatured.ImageUrl ?? "https://via.placeholder.com/600x300/161b22/00d4ff?text=" + Uri.EscapeDataString(mainFeatured.Title.Substring(0, Math.Min(mainFeatured.Title.Length, 20))))" 
                                 class="card-img-top" alt="@(mainFeatured.ImageAlt ?? mainFeatured.Title)">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <span class="article-category @(mainFeatured.IsBreaking ? "breaking" : mainFeatured.CategorySlug.ToLowerInvariant())" 
                                          style="@(!string.IsNullOrEmpty(mainFeatured.CategoryColor) ? $"background-color: {mainFeatured.CategoryColor};" : "")">
                                        @(mainFeatured.IsBreaking ? "Breaking" : mainFeatured.CategoryName)
                                    </span>
                                    <small class="text-muted">@mainFeatured.TimeAgo</small>
                                </div>
                                <h3 class="card-title">@mainFeatured.Title</h3>
                                <p class="card-text">@mainFeatured.Summary</p>
                                <div class="article-meta">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-circle me-2 text-muted"></i>
                                        <span class="text-muted small">@(mainFeatured.Author ?? mainFeatured.SourceName)</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="text-muted small">
                                            <i class="fas fa-eye me-1"></i>@mainFeatured.ViewCount views
                                        </span>
                                        <span class="text-muted small">
                                            <i class="fas fa-clock me-1"></i>@mainFeatured.ReadingTimeMinutes min read
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                @if (otherFeatured.Any())
                {
                    <!-- Other Featured Articles -->
                    <div class="col-lg-6">
                        <div class="row g-4">
                            @foreach (var article in otherFeatured)
                            {
                                <div class="col-12">
                                    <div class="card article-card">
                                        <div class="row g-0">
                                            <div class="col-md-4">
                                                <img src="@(article.ImageUrl ?? "https://via.placeholder.com/300x200/161b22/" + (article.CategoryColor?.TrimStart('#') ?? "6c757d") + "?text=" + Uri.EscapeDataString(article.CategoryName))" 
                                                     class="img-fluid rounded-start h-100" style="object-fit: cover;" alt="@(article.ImageAlt ?? article.Title)">
                                            </div>
                                            <div class="col-md-8">
                                                <div class="card-body h-100 d-flex flex-column">
                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                        <span class="article-category @article.CategorySlug.ToLowerInvariant()" 
                                                              style="@(!string.IsNullOrEmpty(article.CategoryColor) ? $"background-color: {article.CategoryColor};" : "")">
                                                            @article.CategoryName
                                                        </span>
                                                        <small class="text-muted">@article.TimeAgo</small>
                                                    </div>
                                                    <h5 class="card-title">@article.Title</h5>
                                                    <p class="card-text flex-grow-1">@(article.Summary.Length > 100 ? article.Summary.Substring(0, 100) + "..." : article.Summary)</p>
                                                    <div class="mt-auto">
                                                        <small class="text-muted">
                                                            @if (!string.IsNullOrEmpty(article.Author))
                                                            {
                                                                <i class="fas fa-user me-1"></i>@article.Author
                                                            }
                                                            else
                                                            {
                                                                <i class="fas fa-external-link-alt me-1"></i>@article.SourceName
                                                            }
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Featured Articles Available</h4>
                <p class="text-secondary">Check back soon for the latest AI news and insights.</p>
            </div>
        }
    </div>
</section>

<!-- Latest Articles Section -->
<section class="py-5" id="latest-articles">
    <div class="container-fluid">
        <div class="row align-items-center mb-5">
            <div class="col">
                <h2 class="gradient-text">Latest Articles</h2>
                <p class="text-secondary">Stay updated with the newest AI developments</p>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group" aria-label="Sort options">
                    <button type="button" class="btn btn-outline-primary active">
                        <i class="fas fa-clock me-1"></i>Recent
                    </button>
                    <button type="button" class="btn btn-outline-primary">
                        <i class="fas fa-fire me-1"></i>Trending
                    </button>
                    <button type="button" class="btn btn-outline-primary">
                        <i class="fas fa-heart me-1"></i>Popular
                    </button>
                </div>
            </div>
        </div>
        
        <div class="row g-4" id="articles-grid">
            @if (Model?.LatestArticles?.Any() == true)
            {
                @foreach (var article in Model.LatestArticles.Take(12))
                {
                    <div class="col-xl-3 col-lg-4 col-md-6">
                        <div class="article-card" data-article-id="@article.Id">
                            <img src="@(article.ImageUrl ?? "https://via.placeholder.com/400x200/161b22/" + (article.CategoryColor?.TrimStart('#') ?? "6c757d") + "?text=" + Uri.EscapeDataString(article.CategoryName))" 
                                 class="card-img-top" alt="@(article.ImageAlt ?? article.Title)">
                            <div class="card-body">
                                <h5 class="card-title">@article.Title</h5>
                                <p class="card-text">@(article.Summary.Length > 120 ? article.Summary.Substring(0, 120) + "..." : article.Summary)</p>
                                <div class="article-meta">
                                    <span class="article-date">@article.TimeAgo</span>
                                    <span class="article-category @article.CategorySlug.ToLowerInvariant()" 
                                          style="@(!string.IsNullOrEmpty(article.CategoryColor) ? $"background-color: {article.CategoryColor};" : "")">
                                        @article.CategoryName
                                    </span>
                                </div>
                                @if (article.ViewCount > 0 || article.ReadingTimeMinutes > 0)
                                {
                                    <div class="article-stats mt-2">
                                        @if (article.ViewCount > 0)
                                        {
                                            <small class="text-muted me-3">
                                                <i class="fas fa-eye me-1"></i>@article.ViewCount
                                            </small>
                                        }
                                        @if (article.ReadingTimeMinutes > 0)
                                        {
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>@article.ReadingTimeMinutes min
                                            </small>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Articles Available</h4>
                        <p class="text-secondary">Check back soon for the latest AI news and insights.</p>
                    </div>
                </div>
            }
        </div>
        
        <!-- Load More Button -->
        <div class="text-center mt-5">
            <button class="btn btn-outline-primary btn-lg" id="loadMoreBtn">
                <i class="fas fa-plus me-2"></i>Load More Articles
                <div class="spinner ms-2 d-none"></div>
            </button>
        </div>
    </div>
</section>

<!-- Newsletter Signup Section -->
<section class="py-5 bg-dark-secondary">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-6 text-center">
                <div class="card bg-dark-tertiary border-brand">
                    <div class="card-body p-5">
                        <i class="fas fa-envelope-open-text fa-3x text-primary-brand mb-4"></i>
                        <h3 class="gradient-text mb-3">Stay Ahead of the AI Curve</h3>
                        <p class="text-secondary mb-4">
                            Get the latest AI breakthroughs, research insights, and industry trends 
                            delivered directly to your inbox. Join thousands of AI enthusiasts and professionals.
                        </p>
                        <form class="row g-3 justify-content-center">
                            <div class="col-md-8">
                                <input type="email" class="form-control form-control-lg" placeholder="Enter your email address" required>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-paper-plane me-2"></i>Subscribe
                                </button>
                            </div>
                        </form>
                        <small class="text-muted mt-3 d-block">
                            <i class="fas fa-shield-alt me-1"></i>
                            No spam, unsubscribe anytime. We respect your privacy.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // API helper functions
        async function loadArticlesByCategory(category) {
            try {
                let endpoint = '/api/articles';
                if (category !== 'all') {
                    endpoint += `?category=${encodeURIComponent(category)}`;
                }
                
                const response = await fetch(endpoint);
                if (!response.ok) throw new Error('Failed to load articles');
                
                const data = await response.json();
                renderArticles(data.articles || data);
            } catch (error) {
                console.error('Error loading articles by category:', error);
                showErrorMessage('Failed to load articles for selected category');
            }
        }

        async function loadArticlesBySort(sortType) {
            try {
                let endpoint;
                switch (sortType) {
                    case 'trending':
                        endpoint = '/api/articles/trending';
                        break;
                    case 'popular':
                        endpoint = '/api/articles/popular';
                        break;
                    default:
                        endpoint = '/api/articles/latest';
                        break;
                }
                
                const response = await fetch(endpoint);
                if (!response.ok) throw new Error('Failed to load articles');
                
                const data = await response.json();
                renderArticles(data);
            } catch (error) {
                console.error('Error loading articles by sort:', error);
                showErrorMessage('Failed to load articles with selected sorting');
            }
        }

        async function loadMoreArticles(page) {
            try {
                const response = await fetch(`/api/articles?page=${page}&pageSize=12`);
                if (!response.ok) throw new Error('Failed to load more articles');
                
                const data = await response.json();
                appendArticles(data.articles || data);
                
                return data.hasNextPage || (data.articles && data.articles.length === 12);
            } catch (error) {
                console.error('Error loading more articles:', error);
                showErrorMessage('Failed to load more articles');
                return false;
            }
        }

        function renderArticles(articles) {
            const grid = $('#articles-grid');
            grid.empty();
            
            if (!articles || articles.length === 0) {
                grid.html(`
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Articles Found</h4>
                            <p class="text-secondary">Try selecting a different category or sort option.</p>
                        </div>
                    </div>
                `);
                return;
            }
            
            articles.forEach(article => {
                grid.append(createArticleCard(article));
            });
        }

        function appendArticles(articles) {
            const grid = $('#articles-grid');
            
            if (articles && articles.length > 0) {
                articles.forEach(article => {
                    grid.append(createArticleCard(article));
                });
            }
        }

        function createArticleCard(article) {
            const imageUrl = article.imageUrl || 
                `https://via.placeholder.com/400x200/161b22/${(article.categoryColor?.replace('#', '') || '6c757d')}?text=${encodeURIComponent(article.categoryName)}`;
            
            const summary = article.summary.length > 120 ? 
                article.summary.substring(0, 120) + '...' : 
                article.summary;
            
            const categoryStyle = article.categoryColor ? 
                `style="background-color: ${article.categoryColor};"` : '';
            
            let statsHtml = '';
            if (article.viewCount > 0 || article.readingTimeMinutes > 0) {
                statsHtml = '<div class="article-stats mt-2">';
                if (article.viewCount > 0) {
                    statsHtml += `<small class="text-muted me-3"><i class="fas fa-eye me-1"></i>${article.viewCount}</small>`;
                }
                if (article.readingTimeMinutes > 0) {
                    statsHtml += `<small class="text-muted"><i class="fas fa-clock me-1"></i>${article.readingTimeMinutes} min</small>`;
                }
                statsHtml += '</div>';
            }
            
            return `
                <div class="col-xl-3 col-lg-4 col-md-6">
                    <div class="article-card" data-article-id="${article.id}">
                        <img src="${imageUrl}" class="card-img-top" alt="${article.imageAlt || article.title}">
                        <div class="card-body">
                            <h5 class="card-title">${article.title}</h5>
                            <p class="card-text">${summary}</p>
                            <div class="article-meta">
                                <span class="article-date">${article.timeAgo}</span>
                                <span class="article-category ${article.categorySlug.toLowerCase()}" ${categoryStyle}>
                                    ${article.categoryName}
                                </span>
                            </div>
                            ${statsHtml}
                        </div>
                    </div>
                </div>
            `;
        }

        function showErrorMessage(message) {
            // You could implement a toast notification system here
            console.error(message);
        }

        $(document).ready(function() {
            // Category filtering
            $('.category-nav .nav-link').click(function(e) {
                e.preventDefault();
                
                // Update active state
                $('.category-nav .nav-link').removeClass('active');
                $(this).addClass('active');
                
                // Get category
                var category = $(this).data('category');
                
                // Add loading effect
                $(this).addClass('loading');
                $('#articles-grid').addClass('loading');
                
                // Load articles for selected category
                loadArticlesByCategory(category).finally(() => {
                    $(this).removeClass('loading');
                    $('#articles-grid').removeClass('loading');
                });
            });
            
            // Sort buttons
            $('.btn-group .btn').click(function() {
                $('.btn-group .btn').removeClass('active');
                $(this).addClass('active');
                
                var sortType = $(this).text().trim().toLowerCase();
                $('#articles-grid').addClass('loading');
                
                // Load articles based on sort type
                loadArticlesBySort(sortType).finally(() => {
                    $('#articles-grid').removeClass('loading');
                });
            });
            
            // Load more articles
            var currentPage = 1;
            $('#loadMoreBtn').click(function() {
                var btn = $(this);
                var spinner = btn.find('.spinner');
                
                btn.prop('disabled', true);
                spinner.removeClass('d-none');
                
                currentPage++;
                loadMoreArticles(currentPage).then(hasMore => {
                    if (!hasMore) {
                        btn.hide();
                    }
                }).catch(error => {
                    console.error('Error loading more articles:', error);
                    currentPage--; // Reset page on error
                }).finally(() => {
                    spinner.addClass('d-none');
                    btn.prop('disabled', false);
                });
            });
            
            // Newsletter signup
            $('form').submit(function(e) {
                e.preventDefault();
                var email = $(this).find('input[type="email"]').val();
                if (email) {
                    var btn = $(this).find('button[type="submit"]');
                    btn.html('<i class="fas fa-check me-2"></i>Subscribed!').addClass('btn-success').removeClass('btn-primary');
                    
                    setTimeout(() => {
                        btn.html('<i class="fas fa-paper-plane me-2"></i>Subscribe').removeClass('btn-success').addClass('btn-primary');
                        $(this).find('input[type="email"]').val('');
                    }, 3000);
                }
            });
            
            // Add scroll animations
            $(window).scroll(function() {
                $('.article-card').each(function() {
                    var elementTop = $(this).offset().top;
                    var elementBottom = elementTop + $(this).outerHeight();
                    var viewportTop = $(window).scrollTop();
                    var viewportBottom = viewportTop + $(window).height();
                    
                    if (elementBottom > viewportTop && elementTop < viewportBottom) {
                        $(this).addClass('animate-in');
                    }
                });
            });
        });
    </script>
    
    <style>
        .animate-in {
            animation: slideInUp 0.6s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .navbar-scrolled {
            background: var(--ai-dark-secondary) !important;
            box-shadow: var(--ai-shadow-md);
        }
    </style>
}