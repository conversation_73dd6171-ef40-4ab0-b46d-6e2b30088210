using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NewsSite.Data;
using NewsSite.Models;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace NewsSite.Services
{
    public class SearchService : ISearchService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SearchService> _logger;
        private readonly IMemoryCache _cache;
        private readonly HashSet<string> _stopWords;
        private const int DefaultCacheExpirationMinutes = 10;

        public SearchService(ApplicationDbContext context, ILogger<SearchService> logger, IMemoryCache cache)
        {
            _context = context;
            _logger = logger;
            _cache = cache;
            _stopWords = InitializeStopWords();
        }

        #region Core Search Functionality

        public async Task<SearchResultsPage> SearchAsync(string query, int page = 1, int pageSize = 20, SearchFilters? filters = null, SearchSortOptions? sortOptions = null)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var normalizedQuery = NormalizeQuery(query);
                var searchTerms = await ExtractSearchTermsAsync(normalizedQuery);
                
                var queryBuilder = BuildSearchQuery(searchTerms, filters);
                var totalCount = await queryBuilder.CountAsync();
                
                // Apply sorting
                var sortedQuery = ApplySorting(queryBuilder, sortOptions ?? new SearchSortOptions(), searchTerms);
                
                // Apply pagination
                var skip = (page - 1) * pageSize;
                var articles = await sortedQuery
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                // Calculate relevance scores and create search results
                var searchResults = new List<SearchResult>();
                foreach (var article in articles)
                {
                    var relevanceScore = await CalculateSearchRelevanceAsync(normalizedQuery, article);
                    var matchedFields = GetMatchedFields(article, searchTerms);
                    var highlights = GenerateHighlights(article, searchTerms);

                    searchResults.Add(new SearchResult
                    {
                        Article = article,
                        RelevanceScore = relevanceScore,
                        MatchedFields = matchedFields,
                        HighlightedSnippets = highlights,
                        Metadata = new Dictionary<string, object>
                        {
                            ["searchTerms"] = searchTerms,
                            ["matchCount"] = matchedFields.Count
                        }
                    });
                }

                stopwatch.Stop();

                // Record search analytics
                await RecordSearchAsync(query, totalCount, stopwatch.Elapsed, filters);

                // Get facets
                var facets = await GetSearchFacetsAsync(query, filters);

                return new SearchResultsPage
                {
                    Results = searchResults,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    Query = query,
                    Filters = filters,
                    SortOptions = sortOptions,
                    SearchDuration = stopwatch.Elapsed,
                    Facets = facets
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing search for query: {Query}", query);
                throw;
            }
        }

        public async Task<SearchResultsPage> AdvancedSearchAsync(string query, SearchFilters filters, SearchSortOptions sortOptions, int page = 1, int pageSize = 20)
        {
            return await SearchAsync(query, page, pageSize, filters, sortOptions);
        }

        public async Task<IEnumerable<Article>> QuickSearchAsync(string query, int maxResults = 10)
        {
            try
            {
                var cacheKey = $"quick_search_{query}_{maxResults}";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Article>? cached))
                {
                    return cached!;
                }

                var normalizedQuery = NormalizeQuery(query);
                var searchTerms = await ExtractSearchTermsAsync(normalizedQuery);
                
                var queryBuilder = BuildSearchQuery(searchTerms, null);
                var articles = await queryBuilder
                    .OrderByDescending(a => a.PublishedDate)
                    .Take(maxResults)
                    .ToListAsync();

                _cache.Set(cacheKey, articles, TimeSpan.FromMinutes(5));
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing quick search for query: {Query}", query);
                throw;
            }
        }

        #endregion

        #region Search Suggestions and Auto-complete

        public async Task<IEnumerable<SearchSuggestion>> GetSearchSuggestionsAsync(string partialQuery, int maxSuggestions = 10)
        {
            try
            {
                var suggestions = new List<SearchSuggestion>();
                var normalizedQuery = partialQuery.ToLowerInvariant().Trim();

                if (string.IsNullOrEmpty(normalizedQuery) || normalizedQuery.Length < 2)
                {
                    return suggestions;
                }

                // Get tag suggestions
                var tagSuggestions = await _context.Tags
                    .Where(t => t.IsActive && t.Name.ToLower().Contains(normalizedQuery))
                    .OrderByDescending(t => t.UsageCount)
                    .Take(maxSuggestions / 4)
                    .Select(t => new SearchSuggestion
                    {
                        Text = t.Name,
                        Type = "tag",
                        Count = t.UsageCount,
                        Relevance = CalculateStringRelevance(t.Name, normalizedQuery)
                    })
                    .ToListAsync();

                suggestions.AddRange(tagSuggestions);

                // Get category suggestions
                var categorySuggestions = await _context.Categories
                    .Where(c => c.IsActive && c.Name.ToLower().Contains(normalizedQuery))
                    .Take(maxSuggestions / 4)
                    .Select(c => new SearchSuggestion
                    {
                        Text = c.Name,
                        Type = "category",
                        Count = c.Articles.Count(a => a.Status == ArticleStatus.Published),
                        Relevance = CalculateStringRelevance(c.Name, normalizedQuery)
                    })
                    .ToListAsync();

                suggestions.AddRange(categorySuggestions);

                // Get source suggestions
                var sourceSuggestions = await _context.Sources
                    .Where(s => s.IsActive && s.Name.ToLower().Contains(normalizedQuery))
                    .Take(maxSuggestions / 4)
                    .Select(s => new SearchSuggestion
                    {
                        Text = s.Name,
                        Type = "source",
                        Count = s.Articles.Count(a => a.Status == ArticleStatus.Published),
                        Relevance = CalculateStringRelevance(s.Name, normalizedQuery)
                    })
                    .ToListAsync();

                suggestions.AddRange(sourceSuggestions);

                // Get author suggestions
                var authorSuggestions = await _context.Articles
                    .Where(a => a.Author != null && a.Author.ToLower().Contains(normalizedQuery) && a.Status == ArticleStatus.Published)
                    .GroupBy(a => a.Author)
                    .Select(g => new SearchSuggestion
                    {
                        Text = g.Key!,
                        Type = "author",
                        Count = g.Count(),
                        Relevance = CalculateStringRelevance(g.Key!, normalizedQuery)
                    })
                    .OrderByDescending(s => s.Count)
                    .Take(maxSuggestions / 4)
                    .ToListAsync();

                suggestions.AddRange(authorSuggestions);

                return suggestions
                    .OrderByDescending(s => s.Relevance)
                    .ThenByDescending(s => s.Count)
                    .Take(maxSuggestions)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search suggestions for query: {Query}", partialQuery);
                return new List<SearchSuggestion>();
            }
        }

        public async Task<IEnumerable<string>> GetQueryAutoCompleteAsync(string partialQuery, int maxSuggestions = 8)
        {
            try
            {
                var suggestions = await GetSearchSuggestionsAsync(partialQuery, maxSuggestions);
                return suggestions.Select(s => s.Text).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto-complete suggestions for query: {Query}", partialQuery);
                return new List<string>();
            }
        }

        public async Task<IEnumerable<SearchSuggestion>> GetPopularSearchesAsync(int count = 10, int days = 30)
        {
            try
            {
                // This would require a search log table in a real implementation
                // For now, return popular tags as search suggestions
                var popularTags = await _context.Tags
                    .Where(t => t.IsActive && t.UsageCount > 0)
                    .OrderByDescending(t => t.UsageCount)
                    .Take(count)
                    .Select(t => new SearchSuggestion
                    {
                        Text = t.Name,
                        Type = "popular",
                        Count = t.UsageCount,
                        Relevance = 1.0m
                    })
                    .ToListAsync();

                return popularTags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting popular searches");
                return new List<SearchSuggestion>();
            }
        }

        public async Task<IEnumerable<SearchSuggestion>> GetRelatedSearchesAsync(string query, int count = 5)
        {
            try
            {
                var searchTerms = await ExtractSearchTermsAsync(query);
                var suggestions = new List<SearchSuggestion>();

                // Find articles matching the query
                var matchingArticles = await BuildSearchQuery(searchTerms, null)
                    .Take(20)
                    .ToListAsync();

                // Get common tags from matching articles
                var commonTagIds = matchingArticles
                    .SelectMany(a => a.ArticleTags.Select(at => at.TagId))
                    .GroupBy(tagId => tagId)
                    .OrderByDescending(g => g.Count())
                    .Take(count)
                    .Select(g => g.Key)
                    .ToList();

                var relatedTags = await _context.Tags
                    .Where(t => commonTagIds.Contains(t.Id))
                    .Select(t => new SearchSuggestion
                    {
                        Text = t.Name,
                        Type = "related",
                        Count = t.UsageCount,
                        Relevance = 0.8m
                    })
                    .ToListAsync();

                return relatedTags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting related searches for query: {Query}", query);
                return new List<SearchSuggestion>();
            }
        }

        #endregion

        #region Faceted Search

        public async Task<Dictionary<string, object>> GetSearchFacetsAsync(string query, SearchFilters? filters = null)
        {
            try
            {
                var facets = new Dictionary<string, object>();

                var categoryFacets = await GetCategoryFacetsAsync(query, filters);
                var sourceFacets = await GetSourceFacetsAsync(query, filters);
                var tagFacets = await GetTagFacetsAsync(query, filters);
                var authorFacets = await GetAuthorFacetsAsync(query, filters);

                facets["categories"] = categoryFacets;
                facets["sources"] = sourceFacets;
                facets["tags"] = tagFacets;
                facets["authors"] = authorFacets;

                return facets;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search facets for query: {Query}", query);
                return new Dictionary<string, object>();
            }
        }

        public async Task<Dictionary<string, int>> GetCategoryFacetsAsync(string query, SearchFilters? filters = null)
        {
            try
            {
                var searchTerms = await ExtractSearchTermsAsync(query);
                var baseQuery = BuildSearchQuery(searchTerms, filters);

                return await baseQuery
                    .GroupBy(a => a.Category.Name)
                    .Select(g => new { Name = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .ToDictionaryAsync(x => x.Name, x => x.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting category facets");
                return new Dictionary<string, int>();
            }
        }

        public async Task<Dictionary<string, int>> GetSourceFacetsAsync(string query, SearchFilters? filters = null)
        {
            try
            {
                var searchTerms = await ExtractSearchTermsAsync(query);
                var baseQuery = BuildSearchQuery(searchTerms, filters);

                return await baseQuery
                    .GroupBy(a => a.Source.Name)
                    .Select(g => new { Name = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .ToDictionaryAsync(x => x.Name, x => x.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting source facets");
                return new Dictionary<string, int>();
            }
        }

        public async Task<Dictionary<string, int>> GetTagFacetsAsync(string query, SearchFilters? filters = null, int maxTags = 20)
        {
            try
            {
                var searchTerms = await ExtractSearchTermsAsync(query);
                var baseQuery = BuildSearchQuery(searchTerms, filters);

                var articleIds = await baseQuery.Select(a => a.Id).ToListAsync();

                return await _context.ArticleTags
                    .Include(at => at.Tag)
                    .Where(at => articleIds.Contains(at.ArticleId))
                    .GroupBy(at => at.Tag.Name)
                    .Select(g => new { Name = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(maxTags)
                    .ToDictionaryAsync(x => x.Name, x => x.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tag facets");
                return new Dictionary<string, int>();
            }
        }

        public async Task<Dictionary<string, int>> GetAuthorFacetsAsync(string query, SearchFilters? filters = null, int maxAuthors = 15)
        {
            try
            {
                var searchTerms = await ExtractSearchTermsAsync(query);
                var baseQuery = BuildSearchQuery(searchTerms, filters);

                return await baseQuery
                    .Where(a => a.Author != null)
                    .GroupBy(a => a.Author)
                    .Select(g => new { Name = g.Key!, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(maxAuthors)
                    .ToDictionaryAsync(x => x.Name, x => x.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting author facets");
                return new Dictionary<string, int>();
            }
        }

        #endregion

        #region Search Within Specific Contexts

        public async Task<SearchResultsPage> SearchInCategoryAsync(string query, int categoryId, int page = 1, int pageSize = 20, SearchSortOptions? sortOptions = null)
        {
            var filters = new SearchFilters { CategoryId = categoryId };
            return await SearchAsync(query, page, pageSize, filters, sortOptions);
        }

        public async Task<SearchResultsPage> SearchInSourceAsync(string query, int sourceId, int page = 1, int pageSize = 20, SearchSortOptions? sortOptions = null)
        {
            var filters = new SearchFilters { SourceId = sourceId };
            return await SearchAsync(query, page, pageSize, filters, sortOptions);
        }

        public async Task<SearchResultsPage> SearchWithTagsAsync(string query, IEnumerable<int> tagIds, int page = 1, int pageSize = 20, SearchSortOptions? sortOptions = null)
        {
            var filters = new SearchFilters { TagIds = tagIds.ToList() };
            return await SearchAsync(query, page, pageSize, filters, sortOptions);
        }

        public async Task<SearchResultsPage> SearchByAuthorAsync(string query, string author, int page = 1, int pageSize = 20, SearchSortOptions? sortOptions = null)
        {
            var filters = new SearchFilters { Author = author };
            return await SearchAsync(query, page, pageSize, filters, sortOptions);
        }

        #endregion

        #region Similar and Related Content

        public async Task<IEnumerable<Article>> FindSimilarArticlesAsync(int articleId, int maxResults = 10)
        {
            try
            {
                var article = await _context.Articles
                    .Include(a => a.ArticleTags)
                    .FirstOrDefaultAsync(a => a.Id == articleId);

                if (article == null) return new List<Article>();

                return await FindSimilarByContentAsync(article.Title, article.Summary, maxResults);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding similar articles for article {ArticleId}", articleId);
                return new List<Article>();
            }
        }

        public async Task<IEnumerable<Article>> FindSimilarByContentAsync(string title, string content, int maxResults = 10)
        {
            try
            {
                var searchTerms = await ExtractSearchTermsAsync($"{title} {content}");
                var searchQuery = BuildSearchQuery(searchTerms, new SearchFilters { Status = ArticleStatus.Published });

                return await searchQuery
                    .Take(maxResults)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding similar articles by content");
                return new List<Article>();
            }
        }

        public async Task<IEnumerable<Article>> FindSimilarByTagsAsync(IEnumerable<int> tagIds, int excludeArticleId = 0, int maxResults = 10)
        {
            try
            {
                var tagIdList = tagIds.ToList();

                return await _context.ArticleTags
                    .Include(at => at.Article)
                        .ThenInclude(a => a.Category)
                    .Include(at => at.Article)
                        .ThenInclude(a => a.Source)
                    .Where(at => tagIdList.Contains(at.TagId) && 
                                at.Article.Id != excludeArticleId &&
                                at.Article.Status == ArticleStatus.Published)
                    .Select(at => at.Article)
                    .Distinct()
                    .OrderByDescending(a => a.PublishedDate)
                    .Take(maxResults)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding similar articles by tags");
                return new List<Article>();
            }
        }

        #endregion

        #region Search Analytics and Tracking

        public async Task<bool> RecordSearchAsync(string query, int resultCount, TimeSpan searchDuration, SearchFilters? filters = null)
        {
            try
            {
                // In a real implementation, you would store this in a search analytics table
                _logger.LogInformation("Search recorded: Query='{Query}', Results={ResultCount}, Duration={Duration}ms", 
                                     query, resultCount, searchDuration.TotalMilliseconds);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording search analytics");
                return false;
            }
        }

        public async Task<bool> RecordSearchClickAsync(string query, int articleId)
        {
            try
            {
                // In a real implementation, you would store this in a search click tracking table
                _logger.LogInformation("Search click recorded: Query='{Query}', ArticleId={ArticleId}", query, articleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording search click");
                return false;
            }
        }

        public async Task<SearchAnalytics?> GetQueryAnalyticsAsync(string query)
        {
            try
            {
                // Mock implementation - in real app, query from analytics table
                return new SearchAnalytics
                {
                    Query = query,
                    SearchCount = 1,
                    ResultCount = 0,
                    FirstSearched = DateTime.UtcNow,
                    LastSearched = DateTime.UtcNow,
                    AverageSearchTime = TimeSpan.FromMilliseconds(100),
                    ClickThroughCount = 0,
                    ClickThroughRate = 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting query analytics for: {Query}", query);
                return null;
            }
        }

        public async Task<IEnumerable<SearchAnalytics>> GetTopQueriesAnalyticsAsync(int count = 20, int days = 30)
        {
            try
            {
                // Mock implementation
                return new List<SearchAnalytics>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top queries analytics");
                return new List<SearchAnalytics>();
            }
        }

        #endregion

        #region Trending and Popular Searches

        public async Task<IEnumerable<string>> GetTrendingQueriesAsync(int count = 10, int days = 7)
        {
            try
            {
                // Mock implementation - return popular tag names
                var popularTags = await _context.Tags
                    .Where(t => t.IsActive)
                    .OrderByDescending(t => t.UsageCount)
                    .Take(count)
                    .Select(t => t.Name)
                    .ToListAsync();

                return popularTags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting trending queries");
                return new List<string>();
            }
        }

        public async Task<IEnumerable<SearchSuggestion>> GetPopularQueriesByPeriodAsync(DateTime fromDate, DateTime toDate, int count = 10)
        {
            try
            {
                // Mock implementation
                return await GetPopularSearchesAsync(count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting popular queries by period");
                return new List<SearchSuggestion>();
            }
        }

        public async Task<Dictionary<string, int>> GetSearchVolumeByDateAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // Mock implementation
                var result = new Dictionary<string, int>();
                var currentDate = fromDate.Date;

                while (currentDate <= toDate.Date)
                {
                    result[currentDate.ToString("yyyy-MM-dd")] = new Random().Next(10, 100);
                    currentDate = currentDate.AddDays(1);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search volume by date");
                return new Dictionary<string, int>();
            }
        }

        #endregion

        #region Search Quality and Relevance

        public async Task<decimal> CalculateSearchRelevanceAsync(string query, Article article)
        {
            try
            {
                var searchTerms = await ExtractSearchTermsAsync(query);
                decimal score = 0;

                // Title match (highest weight)
                foreach (var term in searchTerms)
                {
                    if (article.Title.ToLowerInvariant().Contains(term.ToLowerInvariant()))
                    {
                        score += 0.4m;
                    }
                }

                // Summary match
                foreach (var term in searchTerms)
                {
                    if (article.Summary.ToLowerInvariant().Contains(term.ToLowerInvariant()))
                    {
                        score += 0.3m;
                    }
                }

                // Author match
                if (!string.IsNullOrEmpty(article.Author))
                {
                    foreach (var term in searchTerms)
                    {
                        if (article.Author.ToLowerInvariant().Contains(term.ToLowerInvariant()))
                        {
                            score += 0.2m;
                        }
                    }
                }

                // Tag match
                var articleTags = article.ArticleTags.Select(at => at.Tag.Name.ToLowerInvariant()).ToList();
                foreach (var term in searchTerms)
                {
                    if (articleTags.Any(tag => tag.Contains(term.ToLowerInvariant())))
                    {
                        score += 0.1m;
                    }
                }

                // Boost for featured/trending content
                if (article.IsFeatured) score *= 1.2m;
                if (article.IsTrending) score *= 1.1m;

                // Recent content boost
                var daysSincePublished = (DateTime.UtcNow - article.PublishedDate).Days;
                if (daysSincePublished <= 7) score *= 1.1m;

                return Math.Min(1.0m, score);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating search relevance");
                return 0;
            }
        }

        public async Task<IEnumerable<string>> ExtractSearchTermsAsync(string query)
        {
            if (string.IsNullOrWhiteSpace(query))
                return new List<string>();

            var terms = query
                .ToLowerInvariant()
                .Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
                .Where(term => term.Length > 2 && !_stopWords.Contains(term))
                .Distinct()
                .ToList();

            return terms;
        }

        public async Task<IEnumerable<string>> GetQueryExpansionsAsync(string query)
        {
            try
            {
                var expansions = new List<string>();
                var searchTerms = await ExtractSearchTermsAsync(query);

                // Find related tags
                foreach (var term in searchTerms)
                {
                    var relatedTags = await _context.Tags
                        .Where(t => t.IsActive && t.Name.ToLower().Contains(term))
                        .Select(t => t.Name)
                        .ToListAsync();

                    expansions.AddRange(relatedTags);
                }

                return expansions.Distinct().Take(10).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting query expansions for: {Query}", query);
                return new List<string>();
            }
        }

        public async Task<bool> UpdateSearchRankingAsync(string query, int articleId, int newRank)
        {
            try
            {
                // In a real implementation, you would update search ranking in an analytics table
                _logger.LogInformation("Search ranking updated: Query='{Query}', ArticleId={ArticleId}, Rank={Rank}", 
                                     query, articleId, newRank);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating search ranking");
                return false;
            }
        }

        #endregion

        #region Search Index Management

        public async Task<bool> ReindexArticleAsync(int articleId)
        {
            try
            {
                // In a real implementation with a search engine like Elasticsearch, 
                // you would update the search index here
                _logger.LogInformation("Reindexed article {ArticleId}", articleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reindexing article {ArticleId}", articleId);
                return false;
            }
        }

        public async Task<bool> RemoveFromIndexAsync(int articleId)
        {
            try
            {
                _logger.LogInformation("Removed article {ArticleId} from search index", articleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing article {ArticleId} from index", articleId);
                return false;
            }
        }

        public async Task<int> ReindexAllArticlesAsync()
        {
            try
            {
                var totalArticles = await _context.Articles.CountAsync();
                _logger.LogInformation("Reindexed {Count} articles", totalArticles);
                return totalArticles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reindexing all articles");
                return 0;
            }
        }

        public async Task<bool> OptimizeSearchIndexAsync()
        {
            try
            {
                // Optimization logic would go here
                _logger.LogInformation("Search index optimized");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing search index");
                return false;
            }
        }

        #endregion

        #region Export and Reporting

        public async Task<Dictionary<string, object>> GetSearchStatsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                return new Dictionary<string, object>
                {
                    ["totalSearches"] = 100,
                    ["uniqueQueries"] = 75,
                    ["averageResultsPerSearch"] = 15.5,
                    ["topQueries"] = await GetTrendingQueriesAsync(10),
                    ["period"] = new { from = fromDate, to = toDate }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search stats");
                return new Dictionary<string, object>();
            }
        }

        public async Task<IEnumerable<SearchAnalytics>> ExportSearchAnalyticsAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                // Mock implementation
                return new List<SearchAnalytics>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting search analytics");
                return new List<SearchAnalytics>();
            }
        }

        public async Task<Dictionary<string, object>> GetSearchPerformanceReportAsync(int days = 30)
        {
            try
            {
                return new Dictionary<string, object>
                {
                    ["averageSearchTime"] = "150ms",
                    ["searchVolume"] = await GetSearchVolumeByDateAsync(DateTime.UtcNow.AddDays(-days), DateTime.UtcNow),
                    ["topCategories"] = await GetCategoryFacetsAsync("", null),
                    ["reportGeneratedAt"] = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting search performance report");
                return new Dictionary<string, object>();
            }
        }

        #endregion

        #region Search Configuration and Cleanup

        public async Task<bool> AddSearchStopWordAsync(string word)
        {
            try
            {
                _stopWords.Add(word.ToLowerInvariant());
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding stop word: {Word}", word);
                return false;
            }
        }

        public async Task<bool> RemoveSearchStopWordAsync(string word)
        {
            try
            {
                return _stopWords.Remove(word.ToLowerInvariant());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing stop word: {Word}", word);
                return false;
            }
        }

        public async Task<IEnumerable<string>> GetSearchStopWordsAsync()
        {
            return _stopWords.ToList();
        }

        public async Task<bool> UpdateSearchWeightsAsync(Dictionary<string, decimal> fieldWeights)
        {
            try
            {
                // Store field weights in configuration
                _logger.LogInformation("Updated search field weights");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating search weights");
                return false;
            }
        }

        public async Task<int> CleanupOldSearchLogsAsync(int daysToKeep = 90)
        {
            try
            {
                // Clean up old search logs
                _logger.LogInformation("Cleaned up old search logs older than {Days} days", daysToKeep);
                return 0; // Mock return
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up old search logs");
                return 0;
            }
        }

        public async Task<int> ConsolidateSearchStatsAsync()
        {
            try
            {
                _logger.LogInformation("Consolidated search statistics");
                return 0; // Mock return
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error consolidating search stats");
                return 0;
            }
        }

        public async Task<bool> ValidateSearchIndexAsync()
        {
            try
            {
                _logger.LogInformation("Search index validation completed");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating search index");
                return false;
            }
        }

        #endregion

        #region Private Helper Methods

        private IQueryable<Article> BuildSearchQuery(IEnumerable<string> searchTerms, SearchFilters? filters)
        {
            var query = _context.Articles
                .Include(a => a.Category)
                .Include(a => a.Source)
                .Include(a => a.Metadata)
                .Include(a => a.ArticleTags)
                    .ThenInclude(at => at.Tag)
                .AsQueryable();

            // Apply search terms
            if (searchTerms.Any())
            {
                foreach (var term in searchTerms)
                {
                    query = query.Where(a =>
                        a.Title.ToLower().Contains(term) ||
                        a.Summary.ToLower().Contains(term) ||
                        (a.Author != null && a.Author.ToLower().Contains(term)) ||
                        a.ArticleTags.Any(at => at.Tag.Name.ToLower().Contains(term)));
                }
            }

            // Apply filters
            if (filters != null)
            {
                if (filters.CategoryId.HasValue)
                    query = query.Where(a => a.CategoryId == filters.CategoryId.Value);

                if (filters.SourceId.HasValue)
                    query = query.Where(a => a.SourceId == filters.SourceId.Value);

                if (filters.Status.HasValue)
                    query = query.Where(a => a.Status == filters.Status.Value);

                if (filters.IsFeatured.HasValue)
                    query = query.Where(a => a.IsFeatured == filters.IsFeatured.Value);

                if (filters.IsBreaking.HasValue)
                    query = query.Where(a => a.IsBreaking == filters.IsBreaking.Value);

                if (filters.IsTrending.HasValue)
                    query = query.Where(a => a.IsTrending == filters.IsTrending.Value);

                if (filters.FromDate.HasValue)
                    query = query.Where(a => a.PublishedDate >= filters.FromDate.Value);

                if (filters.ToDate.HasValue)
                    query = query.Where(a => a.PublishedDate <= filters.ToDate.Value);

                if (!string.IsNullOrEmpty(filters.Author))
                    query = query.Where(a => a.Author != null && a.Author.Contains(filters.Author));

                if (filters.TagIds?.Any() == true)
                    query = query.Where(a => a.ArticleTags.Any(at => filters.TagIds.Contains(at.TagId)));

                if (filters.HasImages.HasValue && filters.HasImages.Value)
                    query = query.Where(a => a.Metadata != null && a.Metadata.HasImages);

                if (filters.HasVideo.HasValue && filters.HasVideo.Value)
                    query = query.Where(a => a.Metadata != null && a.Metadata.HasVideo);

                if (filters.MinQualityScore.HasValue)
                    query = query.Where(a => a.Metadata != null && a.Metadata.QualityScore >= filters.MinQualityScore.Value);

                if (filters.MinReadingTime.HasValue)
                    query = query.Where(a => a.Metadata != null && a.Metadata.ReadingTimeMinutes >= filters.MinReadingTime.Value);

                if (filters.MaxReadingTime.HasValue)
                    query = query.Where(a => a.Metadata != null && a.Metadata.ReadingTimeMinutes <= filters.MaxReadingTime.Value);
            }

            return query;
        }

        private IOrderedQueryable<Article> ApplySorting(IQueryable<Article> query, SearchSortOptions sortOptions, IEnumerable<string> searchTerms)
        {
            return sortOptions.SortBy.ToLowerInvariant() switch
            {
                "relevance" => ApplyRelevanceSorting(query, searchTerms, sortOptions),
                "date" or "publisheddate" => sortOptions.SortDescending ? 
                    query.OrderByDescending(a => a.PublishedDate) : 
                    query.OrderBy(a => a.PublishedDate),
                "title" => sortOptions.SortDescending ? 
                    query.OrderByDescending(a => a.Title) : 
                    query.OrderBy(a => a.Title),
                "author" => sortOptions.SortDescending ? 
                    query.OrderByDescending(a => a.Author) : 
                    query.OrderBy(a => a.Author),
                "views" => sortOptions.SortDescending ? 
                    query.OrderByDescending(a => a.Metadata!.ViewCount) : 
                    query.OrderBy(a => a.Metadata!.ViewCount),
                _ => ApplyRelevanceSorting(query, searchTerms, sortOptions)
            };
        }

        private IOrderedQueryable<Article> ApplyRelevanceSorting(IQueryable<Article> query, IEnumerable<string> searchTerms, SearchSortOptions sortOptions)
        {
            var orderedQuery = query.OrderByDescending(a => a.PublishedDate); // Default fallback

            if (sortOptions.BoostFeatured)
            {
                orderedQuery = query.OrderByDescending(a => a.IsFeatured)
                    .ThenByDescending(a => a.IsTrending)
                    .ThenByDescending(a => a.Priority);
            }

            if (sortOptions.BoostRecent)
            {
                orderedQuery = (IOrderedQueryable<Article>)orderedQuery.ThenByDescending(a => a.PublishedDate);
            }

            return orderedQuery;
        }

        private List<string> GetMatchedFields(Article article, IEnumerable<string> searchTerms)
        {
            var matchedFields = new List<string>();

            foreach (var term in searchTerms)
            {
                if (article.Title.ToLowerInvariant().Contains(term.ToLowerInvariant()))
                    matchedFields.Add("title");

                if (article.Summary.ToLowerInvariant().Contains(term.ToLowerInvariant()))
                    matchedFields.Add("summary");

                if (!string.IsNullOrEmpty(article.Author) && article.Author.ToLowerInvariant().Contains(term.ToLowerInvariant()))
                    matchedFields.Add("author");

                if (article.ArticleTags.Any(at => at.Tag.Name.ToLowerInvariant().Contains(term.ToLowerInvariant())))
                    matchedFields.Add("tags");
            }

            return matchedFields.Distinct().ToList();
        }

        private List<string> GenerateHighlights(Article article, IEnumerable<string> searchTerms)
        {
            var highlights = new List<string>();

            foreach (var term in searchTerms)
            {
                // Simple highlighting - in production, use proper text processing
                var titleHighlight = HighlightText(article.Title, term);
                if (!string.IsNullOrEmpty(titleHighlight))
                    highlights.Add(titleHighlight);

                var summaryHighlight = HighlightText(TruncateText(article.Summary, 200), term);
                if (!string.IsNullOrEmpty(summaryHighlight))
                    highlights.Add(summaryHighlight);
            }

            return highlights.Take(3).ToList();
        }

        private string HighlightText(string text, string term)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(term))
                return string.Empty;

            var regex = new Regex($"({Regex.Escape(term)})", RegexOptions.IgnoreCase);
            return regex.Replace(text, "<mark>$1</mark>");
        }

        private string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength) + "...";
        }

        private string NormalizeQuery(string query)
        {
            if (string.IsNullOrWhiteSpace(query))
                return string.Empty;

            return query.Trim().ToLowerInvariant();
        }

        private decimal CalculateStringRelevance(string text, string query)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(query))
                return 0;

            var normalizedText = text.ToLowerInvariant();
            var normalizedQuery = query.ToLowerInvariant();

            if (normalizedText == normalizedQuery)
                return 1.0m;

            if (normalizedText.StartsWith(normalizedQuery))
                return 0.8m;

            if (normalizedText.Contains(normalizedQuery))
                return 0.6m;

            return 0.3m;
        }

        private static HashSet<string> InitializeStopWords()
        {
            return new HashSet<string>
            {
                "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by",
                "a", "an", "is", "are", "was", "were", "be", "been", "have", "has", "had",
                "do", "does", "did", "will", "would", "could", "should", "may", "might", "can",
                "this", "that", "these", "those", "i", "you", "he", "she", "it", "we", "they",
                "me", "him", "her", "us", "them", "my", "your", "his", "her", "its", "our", "their"
            };
        }

        #endregion
    }
}