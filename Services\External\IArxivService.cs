using NewsSite.Models;

namespace NewsSite.Services.External
{
    public interface IArxivService
    {
        /// <summary>
        /// Fetches research papers from arXiv based on source configuration
        /// </summary>
        Task<List<Article>> FetchPapersAsync(SourceConfiguration configuration);

        /// <summary>
        /// Searches arXiv for papers by query
        /// </summary>
        Task<List<Article>> SearchPapersAsync(string query, int maxResults = 25);

        /// <summary>
        /// Fetches recent papers from specific arXiv category
        /// </summary>
        Task<List<Article>> FetchByCategoryAsync(string category, int maxResults = 25);

        /// <summary>
        /// Gets detailed information about a specific arXiv paper
        /// </summary>
        Task<ArxivPaperInfo?> GetPaperInfoAsync(string arxivId);

        /// <summary>
        /// Validates arXiv paper ID format
        /// </summary>
        bool ValidateArxivId(string arxivId);
    }

    public class ArxivPaperInfo
    {
        public string Id { get; set; } = "";
        public string Title { get; set; } = "";
        public string Summary { get; set; } = "";
        public List<string> Authors { get; set; } = new();
        public List<string> Categories { get; set; } = new();
        public DateTime Published { get; set; }
        public DateTime Updated { get; set; }
        public string PdfUrl { get; set; } = "";
        public string AbstractUrl { get; set; } = "";
        public string Comment { get; set; } = "";
        public string JournalRef { get; set; } = "";
        public string DOI { get; set; } = "";
        public List<string> Links { get; set; } = new();
    }
}