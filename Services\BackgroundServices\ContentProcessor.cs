using NewsSite.Models;
using NewsSite.Services.Utilities;
using System.Text.RegularExpressions;
using HtmlAgilityPack;

namespace NewsSite.Services.BackgroundServices
{
    public class ContentProcessor : IContentProcessor
    {
        private readonly IDuplicateDetectionService _duplicateDetectionService;
        private readonly IContentService _contentService;
        private readonly ICategoryService _categoryService;
        private readonly ITagService _tagService;
        private readonly ISlugService _slugService;
        private readonly IContentParsingService _contentParsingService;
        private readonly ILogger<ContentProcessor> _logger;

        // Common technology and AI/ML keywords for categorization
        private static readonly Dictionary<string, string[]> CategoryKeywords = new()
        {
            ["artificial-intelligence"] = new[] { "ai", "artificial intelligence", "machine learning", "ml", "neural network", "deep learning", "llm", "gpt", "chatbot", "nlp", "computer vision" },
            ["technology"] = new[] { "tech", "software", "programming", "development", "coding", "api", "framework", "database", "cloud", "security", "cybersecurity" },
            ["research"] = new[] { "research", "study", "paper", "analysis", "experiment", "findings", "methodology", "peer review", "academic", "arxiv" },
            ["startup"] = new[] { "startup", "funding", "venture capital", "vc", "investment", "entrepreneur", "ipo", "unicorn", "series a", "series b" },
            ["general"] = new[] { "news", "update", "announcement", "release", "launch", "feature", "product" }
        };

        public ContentProcessor(
            IDuplicateDetectionService duplicateDetectionService,
            IContentService contentService,
            ICategoryService categoryService,
            ITagService tagService,
            ISlugService slugService,
            IContentParsingService contentParsingService,
            ILogger<ContentProcessor> logger)
        {
            _duplicateDetectionService = duplicateDetectionService;
            _contentService = contentService;
            _categoryService = categoryService;
            _tagService = tagService;
            _slugService = slugService;
            _contentParsingService = contentParsingService;
            _logger = logger;
        }

        public async Task<ProcessedArticle?> ProcessArticleAsync(Article article)
        {
            try
            {
                _logger.LogDebug("Processing article: {Title}", article.Title);

                // Check for duplicates first
                var isDuplicate = await _duplicateDetectionService.IsDuplicateAsync(article);
                if (isDuplicate)
                {
                    _logger.LogDebug("Article is duplicate, skipping: {Title}", article.Title);
                    return new ProcessedArticle
                    {
                        Article = article,
                        IsNew = false,
                        IsDuplicate = true,
                        ValidationResult = new ValidationResult { IsValid = false, Errors = new List<string> { "Duplicate content" } }
                    };
                }

                // Sanitize content
                var sanitizedArticle = await SanitizeArticleAsync(article);

                // Validate article
                var validationResult = await ValidateArticleAsync(sanitizedArticle);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("Article validation failed: {Title}. Errors: {Errors}", 
                        sanitizedArticle.Title, string.Join(", ", validationResult.Errors));
                    
                    // Don't process invalid articles unless they have minor issues
                    if (validationResult.Errors.Any(e => e.Contains("critical", StringComparison.OrdinalIgnoreCase)))
                    {
                        return null;
                    }
                }

                // Generate slug
                sanitizedArticle.Slug = await _slugService.GenerateUniqueSlugAsync(sanitizedArticle.Title);

                // Extract tags
                var extractedTags = await ExtractTagsAsync(sanitizedArticle);

                // Determine category
                var assignedCategory = await DetermineCategoryAsync(sanitizedArticle);

                // Save the article
                sanitizedArticle.CategoryId = assignedCategory?.Id;
                var savedArticle = await _contentService.CreateAsync(sanitizedArticle);

                // Associate tags
                if (extractedTags.Any())
                {
                    await _tagService.AssignTagsToArticleAsync(savedArticle.Id, extractedTags.Select(t => t.Id).ToList());
                }

                _logger.LogDebug("Successfully processed article: {Title}", savedArticle.Title);

                return new ProcessedArticle
                {
                    Article = savedArticle,
                    IsNew = true,
                    IsDuplicate = false,
                    ValidationResult = validationResult,
                    ExtractedTags = extractedTags,
                    AssignedCategory = assignedCategory
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing article: {Title}", article.Title);
                return null;
            }
        }

        public async Task<ValidationResult> ValidateArticleAsync(Article article)
        {
            var result = new ValidationResult { IsValid = true };

            // Required field validation
            if (string.IsNullOrWhiteSpace(article.Title))
            {
                result.Errors.Add("Title is required");
                result.IsValid = false;
            }

            if (string.IsNullOrWhiteSpace(article.Content))
            {
                result.Errors.Add("Content is required");
                result.IsValid = false;
            }

            if (string.IsNullOrWhiteSpace(article.Url))
            {
                result.Errors.Add("URL is required");
                result.IsValid = false;
            }
            else if (!Uri.TryCreate(article.Url, UriKind.Absolute, out _))
            {
                result.Errors.Add("Invalid URL format");
                result.IsValid = false;
            }

            // Content quality checks
            var qualityScore = await CalculateContentQualityAsync(article);
            result.QualityScore = qualityScore;

            if (qualityScore.Overall < 30)
            {
                result.Warnings.Add("Low content quality score");
            }

            if (article.Content.Length < 100)
            {
                result.Warnings.Add("Content is very short");
            }

            if (article.Content.Length > 50000)
            {
                result.Warnings.Add("Content is very long and may need truncation");
            }

            // Check for potential spam indicators
            if (HasSpamIndicators(article))
            {
                result.Errors.Add("Critical: Content appears to be spam");
                result.IsValid = false;
            }

            return result;
        }

        public async Task<Article> SanitizeArticleAsync(Article article)
        {
            // Sanitize HTML content
            if (!string.IsNullOrEmpty(article.Content))
            {
                article.Content = await _contentParsingService.SanitizeHtmlAsync(article.Content);
            }

            if (!string.IsNullOrEmpty(article.Summary))
            {
                article.Summary = await _contentParsingService.SanitizeHtmlAsync(article.Summary);
            }

            // Clean up title
            article.Title = article.Title?.Trim().Replace("\n", " ").Replace("\r", " ");
            if (!string.IsNullOrEmpty(article.Title))
            {
                // Remove excessive whitespace
                article.Title = Regex.Replace(article.Title, @"\s+", " ");
                
                // Limit title length
                if (article.Title.Length > 200)
                {
                    article.Title = article.Title.Substring(0, 197) + "...";
                }
            }

            // Ensure dates are valid
            if (article.PublishedDate == default)
            {
                article.PublishedDate = DateTime.UtcNow;
            }

            if (article.CreatedDate == default)
            {
                article.CreatedDate = DateTime.UtcNow;
            }

            return article;
        }

        public async Task<List<Tag>> ExtractTagsAsync(Article article)
        {
            var extractedTags = new List<Tag>();

            try
            {
                // Combine title and content for tag extraction
                var textForAnalysis = $"{article.Title} {article.Summary} {article.Content}".ToLower();

                // Extract technology-related keywords
                var keywords = ExtractKeywords(textForAnalysis);

                foreach (var keyword in keywords.Take(10)) // Limit to top 10 tags
                {
                    var existingTag = await _tagService.GetByNameAsync(keyword);
                    if (existingTag == null)
                    {
                        // Create new tag
                        var newTag = new Tag
                        {
                            Name = keyword,
                            Slug = await _slugService.GenerateSlugAsync(keyword),
                            CreatedDate = DateTime.UtcNow
                        };
                        existingTag = await _tagService.CreateAsync(newTag);
                    }

                    if (!extractedTags.Any(t => t.Name.Equals(keyword, StringComparison.OrdinalIgnoreCase)))
                    {
                        extractedTags.Add(existingTag);
                    }
                }

                return extractedTags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting tags for article: {Title}", article.Title);
                return extractedTags;
            }
        }

        public async Task<Category?> DetermineCategoryAsync(Article article)
        {
            try
            {
                var textForAnalysis = $"{article.Title} {article.Summary} {article.Content}".ToLower();

                // Score each category based on keyword matches
                var categoryScores = new Dictionary<string, int>();

                foreach (var categoryKeyword in CategoryKeywords)
                {
                    var score = 0;
                    foreach (var keyword in categoryKeyword.Value)
                    {
                        var keywordCount = CountKeywordOccurrences(textForAnalysis, keyword);
                        score += keywordCount * (keyword.Length > 10 ? 3 : keyword.Length > 5 ? 2 : 1); // Weight longer, more specific terms higher
                    }
                    categoryScores[categoryKeyword.Key] = score;
                }

                // Find the category with the highest score
                var bestCategory = categoryScores.OrderByDescending(cs => cs.Value).FirstOrDefault();
                
                if (bestCategory.Value > 0)
                {
                    var category = await _categoryService.GetBySlugAsync(bestCategory.Key);
                    if (category != null)
                    {
                        return category;
                    }
                }

                // Default to "general" category
                return await _categoryService.GetBySlugAsync("general");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error determining category for article: {Title}", article.Title);
                return await _categoryService.GetBySlugAsync("general");
            }
        }

        private async Task<ContentQualityScore> CalculateContentQualityAsync(Article article)
        {
            var score = new ContentQualityScore();

            // Content length score (0-25 points)
            var contentLength = article.Content?.Length ?? 0;
            score.ContentLength = contentLength switch
            {
                < 100 => 5,
                < 500 => 15,
                < 2000 => 25,
                < 10000 => 20,
                _ => 10
            };

            // Basic readability score (0-25 points)
            score.ReadabilityScore = CalculateReadabilityScore(article.Content);

            // Relevance score based on keywords (0-25 points)
            score.RelevanceScore = CalculateRelevanceScore(article);

            // Structure and media score (0-25 points)
            var structureScore = 0;
            
            if (!string.IsNullOrEmpty(article.ImageUrl))
            {
                score.HasImages = true;
                structureScore += 10;
            }

            if (HasValidLinks(article.Content))
            {
                score.HasValidLinks = true;
                structureScore += 10;
            }

            if (HasGoodStructure(article.Content))
            {
                structureScore += 5;
            }

            score.Overall = score.ContentLength + score.ReadabilityScore + score.RelevanceScore + structureScore;

            return score;
        }

        private int CalculateReadabilityScore(string? content)
        {
            if (string.IsNullOrEmpty(content))
                return 0;

            // Simple readability metrics
            var sentences = content.Split('.', '!', '?').Length;
            var words = content.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
            
            if (sentences == 0) return 5;

            var avgWordsPerSentence = (double)words / sentences;
            
            return avgWordsPerSentence switch
            {
                < 10 => 25,  // Very readable
                < 20 => 20,  // Good readability
                < 30 => 15,  // Moderate readability
                < 40 => 10,  // Poor readability
                _ => 5       // Very poor readability
            };
        }

        private int CalculateRelevanceScore(Article article)
        {
            var textForAnalysis = $"{article.Title} {article.Summary} {article.Content}".ToLower();
            
            var relevantKeywords = CategoryKeywords.Values.SelectMany(k => k).ToArray();
            var matchCount = relevantKeywords.Count(keyword => textForAnalysis.Contains(keyword));
            
            return Math.Min(25, matchCount * 2);
        }

        private bool HasValidLinks(string? content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            var doc = new HtmlDocument();
            doc.LoadHtml(content);
            
            var links = doc.DocumentNode.SelectNodes("//a[@href]");
            return links?.Any(link => Uri.TryCreate(link.GetAttributeValue("href", ""), UriKind.Absolute, out _)) == true;
        }

        private bool HasGoodStructure(string? content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            var doc = new HtmlDocument();
            doc.LoadHtml(content);
            
            // Check for headings, paragraphs, lists
            return doc.DocumentNode.SelectNodes("//h1|//h2|//h3|//p|//ul|//ol")?.Count > 3;
        }

        private bool HasSpamIndicators(Article article)
        {
            var content = $"{article.Title} {article.Content}".ToLower();
            
            var spamIndicators = new[]
            {
                "buy now", "click here", "limited time", "act now", "free money",
                "guaranteed", "risk free", "no questions asked", "call now"
            };

            var spamCount = spamIndicators.Count(indicator => content.Contains(indicator));
            
            // If content has multiple spam indicators or excessive capitalization, mark as spam
            return spamCount >= 3 || HasExcessiveCapitalization(article.Title);
        }

        private bool HasExcessiveCapitalization(string? text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            var upperCount = text.Count(char.IsUpper);
            var totalLetters = text.Count(char.IsLetter);
            
            return totalLetters > 0 && (double)upperCount / totalLetters > 0.5;
        }

        private List<string> ExtractKeywords(string text)
        {
            var keywords = new List<string>();
            
            // Extract all keywords from our category definitions
            var allKeywords = CategoryKeywords.Values.SelectMany(k => k).ToList();
            
            foreach (var keyword in allKeywords)
            {
                if (text.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    keywords.Add(keyword);
                }
            }

            // Also extract common technical terms using regex
            var technicalTerms = Regex.Matches(text, @"\b[A-Z]{2,}|[a-z]+\.[a-z]+|[a-z]+\-[a-z]+\b")
                .Cast<Match>()
                .Select(m => m.Value.ToLower())
                .Where(term => term.Length > 2 && term.Length < 20)
                .Distinct()
                .Take(5);

            keywords.AddRange(technicalTerms);

            return keywords.Distinct().OrderBy(k => k).ToList();
        }

        private int CountKeywordOccurrences(string text, string keyword)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(keyword))
                return 0;

            var count = 0;
            var index = 0;
            
            while ((index = text.IndexOf(keyword, index, StringComparison.OrdinalIgnoreCase)) != -1)
            {
                count++;
                index += keyword.Length;
            }
            
            return count;
        }
    }
}