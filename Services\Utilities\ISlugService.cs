namespace NewsSite.Services.Utilities
{
    public interface ISlugService
    {
        /// <summary>
        /// Generates a URL-friendly slug from text
        /// </summary>
        Task<string> GenerateSlugAsync(string text);

        /// <summary>
        /// Generates a unique slug by checking against existing slugs
        /// </summary>
        Task<string> GenerateUniqueSlugAsync(string text);

        /// <summary>
        /// Validates if a slug is properly formatted
        /// </summary>
        bool IsValidSlug(string slug);

        /// <summary>
        /// Cleans and normalizes text for slug generation
        /// </summary>
        string CleanTextForSlug(string text);

        /// <summary>
        /// Checks if a slug already exists in the database
        /// </summary>
        Task<bool> SlugExistsAsync(string slug);
    }
}