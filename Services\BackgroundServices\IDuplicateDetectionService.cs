using NewsSite.Models;

namespace NewsSite.Services.BackgroundServices
{
    public interface IDuplicateDetectionService
    {
        /// <summary>
        /// Checks if an article is a duplicate of existing content
        /// </summary>
        Task<bool> IsDuplicateAsync(Article article);

        /// <summary>
        /// Finds potential duplicate articles based on similarity
        /// </summary>
        Task<List<Article>> FindPotentialDuplicatesAsync(Article article);

        /// <summary>
        /// Calculates similarity score between two articles
        /// </summary>
        Task<double> CalculateSimilarityScoreAsync(Article article1, Article article2);

        /// <summary>
        /// Generates content hash for an article
        /// </summary>
        string GenerateContentHash(Article article);

        /// <summary>
        /// Finds duplicate articles in the database and marks them
        /// </summary>
        Task<int> DetectAndMarkDuplicatesAsync();
    }
}