@model SearchIndexViewModel
@{
    ViewData["Title"] = "Search AI Frontiers";
    ViewData["Description"] = "Search across all AI content including articles, research papers, tools, and videos";
}

<div class="search-page">
    <!-- Hero Search Section -->
    <div class="search-hero">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h1 class="search-title">Discover AI Knowledge</h1>
                    <p class="search-subtitle">Search across articles, research papers, tools, and videos from the AI community</p>
                    
                    <!-- Main Search Form -->
                    <form method="get" action="@Url.Action("Index")" class="search-form">
                        <div class="search-input-group">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" 
                                       name="q" 
                                       id="main-search" 
                                       class="form-control search-input" 
                                       placeholder="What AI topic interests you today?"
                                       autocomplete="off"
                                       data-toggle="search-suggestions">
                                <div id="search-suggestions" class="search-suggestions d-none"></div>
                            </div>
                            <button type="submit" class="btn btn-primary search-btn">
                                <i class="fas fa-search"></i>
                                <span class="d-none d-sm-inline">Search</span>
                            </button>
                        </div>
                        
                        <!-- Quick Filters -->
                        <div class="quick-filters">
                            <span class="filter-label">Quick filters:</span>
                            @foreach (var category in Model.Categories.Take(4))
                            {
                                <a href="@Url.Action("Category", new { categoryId = category.Id })" class="filter-chip">
                                    @category.Name
                                </a>
                            }
                            <a href="@Url.Action("Advanced")" class="filter-chip advanced-link">
                                <i class="fas fa-sliders-h"></i> Advanced
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Popular & Trending Section -->
    <div class="search-suggestions-section">
        <div class="container">
            <div class="row">
                <!-- Popular Searches -->
                <div class="col-lg-4 mb-4">
                    <div class="suggestion-card">
                        <h3 class="suggestion-title">
                            <i class="fas fa-fire text-danger"></i>
                            Popular Searches
                        </h3>
                        <div class="suggestion-list">
                            @foreach (var search in Model.PopularSearches.Take(8))
                            {
                                <a href="@Url.Action("Index", new { q = search.Text })" class="suggestion-item">
                                    <span class="suggestion-text">@search.Text</span>
                                    <span class="suggestion-count">@search.Count.ToString("N0")</span>
                                </a>
                            }
                        </div>
                    </div>
                </div>

                <!-- Trending Queries -->
                <div class="col-lg-4 mb-4">
                    <div class="suggestion-card">
                        <h3 class="suggestion-title">
                            <i class="fas fa-trending-up text-success"></i>
                            Trending Now
                        </h3>
                        <div class="suggestion-list">
                            @foreach (var query in Model.TrendingQueries.Take(8))
                            {
                                <a href="@Url.Action("Index", new { q = query })" class="suggestion-item">
                                    <span class="suggestion-text">@query</span>
                                    <span class="suggestion-badge trending">Hot</span>
                                </a>
                            }
                        </div>
                    </div>
                </div>

                <!-- Categories -->
                <div class="col-lg-4 mb-4">
                    <div class="suggestion-card">
                        <h3 class="suggestion-title">
                            <i class="fas fa-th-large text-info"></i>
                            Browse Categories
                        </h3>
                        <div class="category-grid">
                            @foreach (var category in Model.Categories)
                            {
                                <a href="@Url.Action("Category", new { categoryId = category.Id })" class="category-item">
                                    <i class="@category.IconClass"></i>
                                    <span>@category.Name</span>
                                    <small>@category.ArticleCount articles</small>
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Searches -->
    @if (Model.RecentSearches.Any())
    {
        <div class="recent-searches-section">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="recent-searches">
                            <h4 class="recent-title">
                                <i class="fas fa-history"></i>
                                Recent Searches
                            </h4>
                            <div class="recent-items">
                                @foreach (var search in Model.RecentSearches)
                                {
                                    <a href="@Url.Action("Index", new { q = search })" class="recent-item">
                                        <i class="fas fa-clock"></i>
                                        @search
                                    </a>
                                }
                                <button class="btn btn-link clear-recent" onclick="clearRecentSearches()">
                                    <i class="fas fa-trash-alt"></i>
                                    Clear History
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Search Tips -->
    <div class="search-tips-section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="search-tips">
                        <h4 class="tips-title">
                            <i class="fas fa-lightbulb"></i>
                            Search Tips
                        </h4>
                        <div class="tips-grid">
                            <div class="tip-item">
                                <i class="fas fa-quote-left"></i>
                                <strong>Use quotes</strong> for exact phrases: "machine learning"
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-plus"></i>
                                <strong>Combine terms</strong> with AND: neural AND networks
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-minus"></i>
                                <strong>Exclude terms</strong> with NOT: AI NOT fiction
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-search-plus"></i>
                                <strong>Wildcards</strong> with *: algorith* finds algorithm, algorithms
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-filter"></i>
                                <strong>Use filters</strong> to narrow results by category, date, or source
                            </div>
                            <div class="tip-item">
                                <i class="fas fa-cog"></i>
                                <strong>Try advanced search</strong> for complex queries and filters
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/search.js"></script>
    <script>
        // Initialize search functionality
        $(document).ready(function() {
            Search.init({
                searchInput: '#main-search',
                suggestionsContainer: '#search-suggestions',
                apiBaseUrl: '@Url.Content("~/api/")'
            });
        });

        function clearRecentSearches() {
            if (confirm('Clear your search history?')) {
                $.post('@Url.Action("ClearRecentSearches")', function() {
                    $('.recent-searches-section').fadeOut();
                });
            }
        }
    </script>
}