using NewsSite.Models;

namespace NewsSite.Services
{
    public class TagWithStats
    {
        public Tag Tag { get; set; } = null!;
        public int ArticleCount { get; set; }
        public int PublishedArticleCount { get; set; }
        public DateTime? LastUsedDate { get; set; }
        public bool IsPopular { get; set; }
    }

    public class TagSuggestion
    {
        public string TagName { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public decimal Confidence { get; set; }
        public bool AlreadyExists { get; set; }
        public int? ExistingTagId { get; set; }
    }

    public interface ITagService
    {
        // Basic CRUD operations
        Task<Tag?> GetByIdAsync(int id);
        Task<Tag?> GetBySlugAsync(string slug);
        Task<IEnumerable<Tag>> GetAllTagsAsync();
        Task<IEnumerable<Tag>> GetActiveTagsAsync();
        Task<Tag> CreateTagAsync(Tag tag);
        Task<Tag> UpdateTagAsync(Tag tag);
        Task<bool> DeleteTagAsync(int id);

        // Tag with statistics
        Task<TagWithStats?> GetTagWithStatsAsync(int id);
        Task<TagWithStats?> GetTagWithStatsBySlugAsync(string slug);
        Task<IEnumerable<TagWithStats>> GetTagsWithStatsAsync();
        Task<IEnumerable<TagWithStats>> GetActiveTagsWithStatsAsync();

        // Popular and trending tags
        Task<IEnumerable<Tag>> GetPopularTagsAsync(int count = 20);
        Task<IEnumerable<Tag>> GetTrendingTagsAsync(int days = 7, int count = 15);
        Task<IEnumerable<Tag>> GetMostUsedTagsAsync(int count = 50);
        Task<IEnumerable<Tag>> GetRecentlyUsedTagsAsync(int days = 30, int count = 25);

        // Tag usage tracking
        Task<bool> IncrementTagUsageAsync(int tagId);
        Task<bool> DecrementTagUsageAsync(int tagId);
        Task<bool> UpdateTagUsageCountAsync(int tagId, int newCount);
        Task<bool> RecalculateTagUsageCountAsync(int tagId);
        Task<int> RecalculateAllTagUsageCountsAsync();

        // Article-tag relationships
        Task<IEnumerable<Tag>> GetTagsForArticleAsync(int articleId);
        Task<IEnumerable<ArticleTag>> GetArticleTagsAsync(int articleId);
        Task<bool> AddTagToArticleAsync(int articleId, int tagId, bool isSystemGenerated = false);
        Task<bool> RemoveTagFromArticleAsync(int articleId, int tagId);
        Task<bool> UpdateArticleTagsAsync(int articleId, IEnumerable<int> tagIds, bool isSystemGenerated = false);
        Task<bool> ClearArticleTagsAsync(int articleId);

        // Tag-based article retrieval
        Task<PaginatedResult<Article>> GetArticlesWithTagAsync(int tagId, int page = 1, int pageSize = 20, ArticleStatus? status = null);
        Task<PaginatedResult<Article>> GetArticlesWithTagBySlugAsync(string slug, int page = 1, int pageSize = 20, ArticleStatus? status = null);
        Task<IEnumerable<Article>> GetLatestArticlesWithTagAsync(int tagId, int count = 10);
        Task<PaginatedResult<Article>> GetArticlesWithAnyTagsAsync(IEnumerable<int> tagIds, int page = 1, int pageSize = 20, ArticleStatus? status = null);
        Task<PaginatedResult<Article>> GetArticlesWithAllTagsAsync(IEnumerable<int> tagIds, int page = 1, int pageSize = 20, ArticleStatus? status = null);

        // Auto-tagging and suggestions
        Task<IEnumerable<TagSuggestion>> SuggestTagsForContentAsync(string title, string content, int maxSuggestions = 10);
        Task<IEnumerable<Tag>> GetRelatedTagsAsync(int tagId, int count = 10);
        Task<IEnumerable<Tag>> GetSimilarTagsAsync(string tagName, int count = 5);
        Task<bool> AutoTagArticleAsync(int articleId, string title, string content);

        // Tag management
        Task<bool> SetTagActiveStatusAsync(int tagId, bool isActive);
        Task<bool> MergeTagsAsync(int sourceTagId, int targetTagId);
        Task<bool> SplitTagAsync(int tagId, IEnumerable<string> newTagNames);
        Task<int> CleanupUnusedTagsAsync();
        Task<int> CleanupDuplicateTagsAsync();

        // Search and filtering
        Task<IEnumerable<Tag>> SearchTagsAsync(string query);
        Task<IEnumerable<Tag>> GetTagsByColorAsync(string color);
        Task<IEnumerable<Tag>> GetSystemGeneratedTagsAsync();
        Task<IEnumerable<Tag>> GetManuallyCreatedTagsAsync();
        Task<IEnumerable<int>> GetTagIdsByNamesAsync(IEnumerable<string> tagNames);

        // Tag analytics
        Task<Dictionary<int, int>> GetTagUsageCountsAsync(IEnumerable<int> tagIds);
        Task<Dictionary<string, int>> GetTagUsageByDateRangeAsync(int tagId, DateTime fromDate, DateTime toDate);
        Task<IEnumerable<Tag>> GetTagsUsedInCategoryAsync(int categoryId, int count = 20);
        Task<IEnumerable<Tag>> GetTagsUsedBySourceAsync(int sourceId, int count = 20);

        // Validation
        Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null);
        Task<bool> ValidateTagAsync(Tag tag);
        Task<bool> CanDeleteTagAsync(int tagId);

        // Bulk operations
        Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> tagIds, bool isActive);
        Task<int> BulkDeleteUnusedTagsAsync(int minUsageCount = 0);
        Task<int> BulkUpdateTagColorsAsync(Dictionary<int, string> tagColorMap);
        Task<bool> BulkAddTagsToArticleAsync(int articleId, IEnumerable<string> tagNames, bool isSystemGenerated = false);

        // Import/Export
        Task<IEnumerable<Tag>> ImportTagsAsync(IEnumerable<string> tagNames, bool isSystemGenerated = false);
        Task<Dictionary<string, object>> ExportTagDataAsync();
    }
}