@model SearchResultsViewModel
@{
    ViewData["Title"] = string.IsNullOrEmpty(Model.Query) ? "Search Results" : $"Search Results for \"{Model.Query}\"";
    ViewData["Description"] = $"Found {Model.Results.TotalCount:N0} results for your search";
}

<div class="search-results-page">
    <!-- Search Header -->
    <div class="search-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <!-- Search Bar -->
                    <form method="get" action="@Url.Action("Index")" class="search-form-compact">
                        <div class="search-input-group">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" 
                                       name="q" 
                                       value="@Model.Query" 
                                       class="form-control search-input" 
                                       placeholder="Search AI Frontiers..."
                                       autocomplete="off"
                                       data-toggle="search-suggestions">
                                <div id="search-suggestions" class="search-suggestions d-none"></div>
                            </div>
                            <button type="submit" class="btn btn-primary search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>

                    <!-- Search Context & Stats -->
                    <div class="search-context">
                        <div class="search-stats">
                            <span class="results-count">
                                <strong>@Model.Results.TotalCount.ToString("N0")</strong> results
                                @if (!string.IsNullOrEmpty(Model.SearchContext))
                                {
                                    <span class="search-context-text">@Model.SearchContext</span>
                                }
                            </span>
                            <span class="search-time">
                                (@Model.Results.SearchDuration.TotalMilliseconds.ToString("F0")ms)
                            </span>
                        </div>

                        <!-- Active Filters -->
                        @if (Model.IsAdvancedSearch && Model.AdvancedFilters != null)
                        {
                            <div class="active-filters">
                                @if (Model.AdvancedFilters.CategoryId.HasValue && Model.SelectedCategory != null)
                                {
                                    <span class="filter-tag">
                                        <i class="fas fa-folder"></i>
                                        @Model.SelectedCategory.Name
                                        <button type="button" class="filter-remove" onclick="removeFilter('category')">×</button>
                                    </span>
                                }
                                @if (Model.AdvancedFilters.SourceId.HasValue && Model.SelectedSource != null)
                                {
                                    <span class="filter-tag">
                                        <i class="fas fa-rss"></i>
                                        @Model.SelectedSource.Name
                                        <button type="button" class="filter-remove" onclick="removeFilter('source')">×</button>
                                    </span>
                                }
                                @if (!string.IsNullOrEmpty(Model.SelectedAuthor))
                                {
                                    <span class="filter-tag">
                                        <i class="fas fa-user"></i>
                                        @Model.SelectedAuthor
                                        <button type="button" class="filter-remove" onclick="removeFilter('author')">×</button>
                                    </span>
                                }
                                @if (Model.SelectedTags?.Any() == true)
                                {
                                    @foreach (var tag in Model.SelectedTags)
                                    {
                                        <span class="filter-tag">
                                            <i class="fas fa-tag"></i>
                                            @tag
                                            <button type="button" class="filter-remove" onclick="removeFilter('tag', '@tag')">×</button>
                                        </span>
                                    }
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Content -->
    <div class="search-content">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Filters -->
                <div class="col-lg-3 col-md-4">
                    <div class="search-sidebar">
                        <!-- Sort Options -->
                        <div class="filter-section">
                            <h5 class="filter-title">Sort Results</h5>
                            <div class="sort-options">
                                @foreach (var option in Model.SortOptions)
                                {
                                    <label class="sort-option">
                                        <input type="radio" name="sort" value="@option.Value" @(option.IsDefault ? "checked" : "")>
                                        <span class="sort-label">@option.Label</span>
                                    </label>
                                }
                            </div>
                        </div>

                        <!-- Category Facets -->
                        @if (Model.Results.Facets.ContainsKey("categories"))
                        {
                            var categories = (Dictionary<string, int>)Model.Results.Facets["categories"];
                            if (categories.Any())
                            {
                                <div class="filter-section">
                                    <h5 class="filter-title">
                                        <i class="fas fa-folder"></i>
                                        Categories
                                    </h5>
                                    <div class="filter-list">
                                        @foreach (var category in categories.Take(8))
                                        {
                                            <label class="filter-item">
                                                <input type="checkbox" name="categories" value="@category.Key">
                                                <span class="filter-label">@category.Key</span>
                                                <span class="filter-count">@category.Value.ToString("N0")</span>
                                            </label>
                                        }
                                        @if (categories.Count > 8)
                                        {
                                            <button class="btn btn-link show-more-filters" data-target="categories">
                                                Show @((categories.Count - 8).ToString("N0")) more...
                                            </button>
                                        }
                                    </div>
                                </div>
                            }
                        }

                        <!-- Source Facets -->
                        @if (Model.Results.Facets.ContainsKey("sources"))
                        {
                            var sources = (Dictionary<string, int>)Model.Results.Facets["sources"];
                            if (sources.Any())
                            {
                                <div class="filter-section">
                                    <h5 class="filter-title">
                                        <i class="fas fa-rss"></i>
                                        Sources
                                    </h5>
                                    <div class="filter-list">
                                        @foreach (var source in sources.Take(6))
                                        {
                                            <label class="filter-item">
                                                <input type="checkbox" name="sources" value="@source.Key">
                                                <span class="filter-label">@source.Key</span>
                                                <span class="filter-count">@source.Value.ToString("N0")</span>
                                            </label>
                                        }
                                        @if (sources.Count > 6)
                                        {
                                            <button class="btn btn-link show-more-filters" data-target="sources">
                                                Show @((sources.Count - 6).ToString("N0")) more...
                                            </button>
                                        }
                                    </div>
                                </div>
                            }
                        }

                        <!-- Tag Facets -->
                        @if (Model.Results.Facets.ContainsKey("tags"))
                        {
                            var tags = (Dictionary<string, int>)Model.Results.Facets["tags"];
                            if (tags.Any())
                            {
                                <div class="filter-section">
                                    <h5 class="filter-title">
                                        <i class="fas fa-tags"></i>
                                        Popular Tags
                                    </h5>
                                    <div class="tag-cloud">
                                        @foreach (var tag in tags.Take(12))
                                        {
                                            <a href="@Url.Action("Tags", new { tags = tag.Key, q = Model.Query })" 
                                               class="tag-item" 
                                               data-count="@tag.Value">
                                                @tag.Key
                                            </a>
                                        }
                                    </div>
                                </div>
                            }
                        }

                        <!-- Author Facets -->
                        @if (Model.Results.Facets.ContainsKey("authors"))
                        {
                            var authors = (Dictionary<string, int>)Model.Results.Facets["authors"];
                            if (authors.Any())
                            {
                                <div class="filter-section">
                                    <h5 class="filter-title">
                                        <i class="fas fa-users"></i>
                                        Authors
                                    </h5>
                                    <div class="filter-list">
                                        @foreach (var author in authors.Take(5))
                                        {
                                            <a href="@Url.Action("Author", new { author = author.Key, q = Model.Query })" 
                                               class="filter-item author-item">
                                                <span class="filter-label">@author.Key</span>
                                                <span class="filter-count">@author.Value.ToString("N0")</span>
                                            </a>
                                        }
                                    </div>
                                </div>
                            }
                        }

                        <!-- Advanced Search Link -->
                        <div class="filter-section">
                            <a href="@Url.Action("Advanced", new { q = Model.Query })" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-sliders-h"></i>
                                Advanced Search
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Main Results -->
                <div class="col-lg-9 col-md-8">
                    <div class="search-results">
                        <!-- View Toggle -->
                        <div class="results-header">
                            <div class="view-toggle">
                                <button class="btn btn-sm view-btn active" data-view="list">
                                    <i class="fas fa-list"></i>
                                    List
                                </button>
                                <button class="btn btn-sm view-btn" data-view="grid">
                                    <i class="fas fa-th"></i>
                                    Grid
                                </button>
                                <button class="btn btn-sm view-btn" data-view="compact">
                                    <i class="fas fa-align-justify"></i>
                                    Compact
                                </button>
                            </div>
                        </div>

                        <!-- Results List -->
                        @if (Model.Results.Results.Any())
                        {
                            <div class="results-container" data-view="list">
                                @foreach (var result in Model.Results.Results)
                                {
                                    <article class="search-result-card">
                                        <div class="result-content">
                                            <div class="result-header">
                                                <h3 class="result-title">
                                                    <a href="@result.Article.Url" 
                                                       onclick="trackSearchClick('@Model.Query', @result.Article.Id)">
                                                        @Html.Raw(result.HighlightedSnippets.FirstOrDefault(h => h.Contains("<mark>")) ?? result.Article.Title)
                                                    </a>
                                                </h3>
                                                <div class="result-meta">
                                                    <span class="result-source">
                                                        <i class="fas fa-rss"></i>
                                                        <a href="@Url.Action("Source", new { sourceId = result.Article.Source.Id })">
                                                            @result.Article.Source.Name
                                                        </a>
                                                    </span>
                                                    @if (!string.IsNullOrEmpty(result.Article.Author))
                                                    {
                                                        <span class="result-author">
                                                            <i class="fas fa-user"></i>
                                                            <a href="@Url.Action("Author", new { author = result.Article.Author })">
                                                                @result.Article.Author
                                                            </a>
                                                        </span>
                                                    }
                                                    <span class="result-date">
                                                        <i class="fas fa-calendar"></i>
                                                        @result.Article.PublishedDate.ToString("MMM dd, yyyy")
                                                    </span>
                                                    <span class="result-category">
                                                        <i class="fas fa-folder"></i>
                                                        <a href="@Url.Action("Category", new { categoryId = result.Article.Category.Id })">
                                                            @result.Article.Category.Name
                                                        </a>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="result-body">
                                                @if (!string.IsNullOrEmpty(result.Article.ImageUrl))
                                                {
                                                    <div class="result-image">
                                                        <img src="@result.Article.ImageUrl" alt="@result.Article.Title" loading="lazy">
                                                    </div>
                                                }
                                                
                                                <div class="result-text">
                                                    <p class="result-summary">
                                                        @Html.Raw(result.HighlightedSnippets.LastOrDefault(h => h.Contains("<mark>")) ?? 
                                                                  (result.Article.Summary.Length > 200 ? 
                                                                   result.Article.Summary.Substring(0, 200) + "..." : 
                                                                   result.Article.Summary))
                                                    </p>
                                                    
                                                    @if (result.Article.ArticleTags.Any())
                                                    {
                                                        <div class="result-tags">
                                                            @foreach (var tag in result.Article.ArticleTags.Take(5))
                                                            {
                                                                <a href="@Url.Action("Tags", new { tags = tag.Tag.Name, q = Model.Query })" 
                                                                   class="tag-badge">
                                                                    @tag.Tag.Name
                                                                </a>
                                                            }
                                                        </div>
                                                    }
                                                </div>
                                            </div>

                                            <div class="result-footer">
                                                <div class="result-stats">
                                                    @if (result.Article.Metadata != null)
                                                    {
                                                        <span class="stat-item">
                                                            <i class="fas fa-eye"></i>
                                                            @result.Article.Metadata.ViewCount.ToString("N0") views
                                                        </span>
                                                        <span class="stat-item">
                                                            <i class="fas fa-clock"></i>
                                                            @result.Article.Metadata.ReadingTimeMinutes min read
                                                        </span>
                                                    }
                                                    <span class="relevance-score" title="Relevance Score">
                                                        <i class="fas fa-star"></i>
                                                        @result.RelevanceScore.ToString("P0")
                                                    </span>
                                                </div>

                                                <div class="result-badges">
                                                    @if (result.Article.IsFeatured)
                                                    {
                                                        <span class="badge badge-featured">Featured</span>
                                                    }
                                                    @if (result.Article.IsBreaking)
                                                    {
                                                        <span class="badge badge-breaking">Breaking</span>
                                                    }
                                                    @if (result.Article.IsTrending)
                                                    {
                                                        <span class="badge badge-trending">Trending</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </article>
                                }
                            </div>

                            <!-- Pagination -->
                            @if (Model.Results.TotalPages > 1)
                            {
                                <nav aria-label="Search results pagination" class="mt-4">
                                    <div class="pagination-wrapper">
                                        <ul class="pagination justify-content-center">
                                            @if (Model.Results.HasPreviousPage)
                                            {
                                                <li class="page-item">
                                                    <a class="page-link" href="@Url.Action("Index", new { q = Model.Query, page = Model.Results.Page - 1, size = Model.Results.PageSize })">
                                                        <i class="fas fa-chevron-left"></i>
                                                        Previous
                                                    </a>
                                                </li>
                                            }

                                            @for (int i = Math.Max(1, Model.Results.Page - 2); i <= Math.Min(Model.Results.TotalPages, Model.Results.Page + 2); i++)
                                            {
                                                <li class="page-item @(i == Model.Results.Page ? "active" : "")">
                                                    <a class="page-link" href="@Url.Action("Index", new { q = Model.Query, page = i, size = Model.Results.PageSize })">
                                                        @i
                                                    </a>
                                                </li>
                                            }

                                            @if (Model.Results.HasNextPage)
                                            {
                                                <li class="page-item">
                                                    <a class="page-link" href="@Url.Action("Index", new { q = Model.Query, page = Model.Results.Page + 1, size = Model.Results.PageSize })">
                                                        Next
                                                        <i class="fas fa-chevron-right"></i>
                                                    </a>
                                                </li>
                                            }
                                        </ul>
                                        
                                        <div class="pagination-info">
                                            Showing @((Model.Results.Page - 1) * Model.Results.PageSize + 1)-@(Math.Min(Model.Results.Page * Model.Results.PageSize, Model.Results.TotalCount)) 
                                            of @Model.Results.TotalCount.ToString("N0") results
                                        </div>
                                    </div>
                                </nav>
                            }
                        }
                        else
                        {
                            <!-- No Results -->
                            <div class="no-results">
                                <div class="no-results-icon">
                                    <i class="fas fa-search-minus"></i>
                                </div>
                                <h3>No results found</h3>
                                <p>Your search <strong>"@Model.Query"</strong> didn't match any articles.</p>
                                
                                <div class="no-results-suggestions">
                                    <h5>Try:</h5>
                                    <ul>
                                        <li>Checking your spelling</li>
                                        <li>Using different keywords</li>
                                        <li>Using more general terms</li>
                                        <li>Removing some filters</li>
                                    </ul>
                                </div>

                                <div class="no-results-actions">
                                    <a href="@Url.Action("Index")" class="btn btn-primary">
                                        <i class="fas fa-home"></i>
                                        Back to Search
                                    </a>
                                    <a href="@Url.Action("Advanced", new { q = Model.Query })" class="btn btn-outline-secondary">
                                        <i class="fas fa-sliders-h"></i>
                                        Try Advanced Search
                                    </a>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/search.js"></script>
    <script>
        // Initialize search results functionality
        $(document).ready(function() {
            SearchResults.init({
                apiBaseUrl: '@Url.Content("~/api/")',
                currentQuery: '@Model.Query'
            });
        });

        function trackSearchClick(query, articleId) {
            // Track click for analytics
            $.post('@Url.Content("~/api/search/click")', {
                query: query,
                articleId: articleId
            });
        }

        function removeFilter(type, value) {
            // Remove filter and reload results
            var url = new URL(window.location);
            
            switch(type) {
                case 'category':
                    url.searchParams.delete('category');
                    break;
                case 'source':
                    url.searchParams.delete('source');
                    break;
                case 'author':
                    url.searchParams.delete('author');
                    break;
                case 'tag':
                    var tags = url.searchParams.get('tags');
                    if (tags) {
                        var tagArray = tags.split(',').filter(t => t !== value);
                        if (tagArray.length > 0) {
                            url.searchParams.set('tags', tagArray.join(','));
                        } else {
                            url.searchParams.delete('tags');
                        }
                    }
                    break;
            }
            
            window.location.href = url.toString();
        }
    </script>
}