{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=aspnet-NewsSite-2fab4969-655f-47ca-bd8e-9cdce7d8ac54;Trusted_Connection=True;MultipleActiveResultSets=true"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Hangfire": "Warning"
    }
  },
  "AllowedHosts": "*",
  
  // External API Configuration
  "YouTube": {
    "ApiKey": "PLACEHOLDER_YOUTUBE_API_KEY"
  },
  "GitHub": {
    "ApiToken": null
  },
  
  // Background Job Configuration
  "Hangfire": {
    "DashboardPath": "/hangfire",
    "ServerName": "NewsSite-ContentAggregator",
    "WorkerCount": 2
  },
  
  // Content Aggregation Settings
  "ContentAggregation": {
    "DefaultUpdateIntervalMinutes": 60,
    "MaxConcurrentRequests": 10,
    "RequestTimeoutSeconds": 30,
    "MaxRetryAttempts": 3,
    "RateLimitDelayMs": 100
  }
}
