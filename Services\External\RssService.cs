using NewsSite.Models;
using NewsSite.Services.Utilities;
using System.ServiceModel.Syndication;
using System.Xml;
using HtmlAgilityPack;

namespace NewsSite.Services.External
{
    public class RssService : IRssService
    {
        private readonly IHttpService _httpService;
        private readonly IContentParsingService _contentParsingService;
        private readonly ILogger<RssService> _logger;

        public RssService(
            IHttpService httpService,
            IContentParsingService contentParsingService,
            ILogger<RssService> logger)
        {
            _httpService = httpService;
            _contentParsingService = contentParsingService;
            _logger = logger;
        }

        public async Task<List<Article>> FetchArticlesAsync(string rssUrl)
        {
            var articles = new List<Article>();

            try
            {
                _logger.LogDebug("Fetching RSS feed: {RssUrl}", rssUrl);

                var feedContent = await _httpService.GetStringAsync(rssUrl);
                if (string.IsNullOrEmpty(feedContent))
                {
                    _logger.LogWarning("Empty RSS feed content from: {RssUrl}", rssUrl);
                    return articles;
                }

                using var stringReader = new StringReader(feedContent);
                using var xmlReader = XmlReader.Create(stringReader);

                var feed = SyndicationFeed.Load(xmlReader);
                if (feed?.Items == null)
                {
                    _logger.LogWarning("No items found in RSS feed: {RssUrl}", rssUrl);
                    return articles;
                }

                foreach (var item in feed.Items.Take(50)) // Limit to latest 50 items
                {
                    try
                    {
                        var article = await ConvertSyndicationItemToArticle(item, rssUrl);
                        if (article != null)
                        {
                            articles.Add(article);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing RSS item: {Title}", item.Title?.Text ?? "Unknown");
                    }
                }

                _logger.LogInformation("Successfully fetched {Count} articles from RSS feed: {RssUrl}", articles.Count, rssUrl);
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching RSS feed: {RssUrl}", rssUrl);
                return articles;
            }
        }

        public async Task<bool> ValidateRssFeedAsync(string rssUrl)
        {
            try
            {
                _logger.LogDebug("Validating RSS feed: {RssUrl}", rssUrl);

                var feedContent = await _httpService.GetStringAsync(rssUrl);
                if (string.IsNullOrEmpty(feedContent))
                {
                    return false;
                }

                using var stringReader = new StringReader(feedContent);
                using var xmlReader = XmlReader.Create(stringReader);

                var feed = SyndicationFeed.Load(xmlReader);
                
                // Basic validation - check if feed has required elements
                return feed != null && 
                       !string.IsNullOrEmpty(feed.Title?.Text) && 
                       feed.Items?.Any() == true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating RSS feed: {RssUrl}", rssUrl);
                return false;
            }
        }

        public async Task<RssFeedInfo?> GetFeedInfoAsync(string rssUrl)
        {
            try
            {
                _logger.LogDebug("Getting feed info: {RssUrl}", rssUrl);

                var feedContent = await _httpService.GetStringAsync(rssUrl);
                if (string.IsNullOrEmpty(feedContent))
                {
                    return null;
                }

                using var stringReader = new StringReader(feedContent);
                using var xmlReader = XmlReader.Create(stringReader);

                var feed = SyndicationFeed.Load(xmlReader);
                if (feed == null)
                {
                    return null;
                }

                return new RssFeedInfo
                {
                    Title = feed.Title?.Text ?? "",
                    Description = feed.Description?.Text ?? "",
                    Link = feed.Links?.FirstOrDefault()?.Uri?.ToString() ?? "",
                    LastBuildDate = feed.LastUpdatedTime.DateTime,
                    Language = feed.Language ?? "",
                    Generator = feed.Generator ?? "",
                    ItemCount = feed.Items?.Count() ?? 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting feed info: {RssUrl}", rssUrl);
                return null;
            }
        }

        public async Task<List<Article>> FetchFromMultipleFeedsAsync(List<string> rssUrls)
        {
            var allArticles = new List<Article>();
            
            try
            {
                _logger.LogInformation("Fetching from {Count} RSS feeds", rssUrls.Count);

                var tasks = rssUrls.Select(async url =>
                {
                    try
                    {
                        return await FetchArticlesAsync(url);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error fetching from RSS feed: {Url}", url);
                        return new List<Article>();
                    }
                });

                var results = await Task.WhenAll(tasks);
                
                foreach (var articles in results)
                {
                    allArticles.AddRange(articles);
                }

                _logger.LogInformation("Successfully fetched {Count} total articles from {FeedCount} feeds", 
                    allArticles.Count, rssUrls.Count);

                return allArticles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching from multiple RSS feeds");
                return allArticles;
            }
        }

        private async Task<Article?> ConvertSyndicationItemToArticle(SyndicationItem item, string sourceUrl)
        {
            try
            {
                var article = new Article
                {
                    Title = item.Title?.Text ?? "",
                    Url = item.Links?.FirstOrDefault()?.Uri?.ToString() ?? "",
                    PublishedDate = item.PublishDate.DateTime != DateTime.MinValue ? 
                        item.PublishDate.DateTime : DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow
                };

                // Extract content
                var content = "";
                if (item.Content is TextSyndicationContent textContent)
                {
                    content = textContent.Text;
                }
                else if (item.Summary != null)
                {
                    content = item.Summary.Text;
                }

                // Clean and process content
                if (!string.IsNullOrEmpty(content))
                {
                    article.Content = await _contentParsingService.ExtractTextFromHtmlAsync(content);
                    article.Summary = await _contentParsingService.GenerateSummaryAsync(article.Content, 200);
                }

                // Extract author
                if (item.Authors?.Any() == true)
                {
                    article.Author = item.Authors.First().Name;
                }

                // Extract categories/tags
                if (item.Categories?.Any() == true)
                {
                    // Store categories as comma-separated string for now
                    var categories = string.Join(", ", item.Categories.Select(c => c.Name));
                    // This will be processed later by the content processor
                }

                // Try to extract image from content
                article.ImageUrl = await ExtractImageFromContent(content);

                // Validate required fields
                if (string.IsNullOrWhiteSpace(article.Title) || 
                    string.IsNullOrWhiteSpace(article.Url))
                {
                    _logger.LogWarning("RSS item missing required fields, skipping");
                    return null;
                }

                return article;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting syndication item to article");
                return null;
            }
        }

        private async Task<string?> ExtractImageFromContent(string content)
        {
            try
            {
                if (string.IsNullOrEmpty(content))
                    return null;

                var doc = new HtmlDocument();
                doc.LoadHtml(content);

                // Look for img tags
                var imgNode = doc.DocumentNode.SelectSingleNode("//img[@src]");
                if (imgNode != null)
                {
                    var src = imgNode.GetAttributeValue("src", "");
                    if (!string.IsNullOrEmpty(src) && Uri.TryCreate(src, UriKind.Absolute, out _))
                    {
                        return src;
                    }
                }

                // Look for media:content or media:thumbnail (RSS media extensions)
                var mediaNodes = doc.DocumentNode.SelectNodes("//media:content[@url] | //media:thumbnail[@url]");
                if (mediaNodes?.Any() == true)
                {
                    var url = mediaNodes.First().GetAttributeValue("url", "");
                    if (!string.IsNullOrEmpty(url) && Uri.TryCreate(url, UriKind.Absolute, out _))
                    {
                        return url;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting image from content");
                return null;
            }
        }
    }
}