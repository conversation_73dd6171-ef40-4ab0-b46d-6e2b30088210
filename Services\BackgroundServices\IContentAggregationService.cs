namespace NewsSite.Services.BackgroundServices
{
    public interface IContentAggregationService
    {
        /// <summary>
        /// Aggregates content from all configured sources
        /// </summary>
        Task AggregateAllContentAsync();

        /// <summary>
        /// Aggregates content from a specific source
        /// </summary>
        Task AggregateContentFromSourceAsync(int sourceId);

        /// <summary>
        /// Gets the status of the last aggregation run
        /// </summary>
        Task<AggregationStatus> GetLastAggregationStatusAsync();
    }

    public class AggregationStatus
    {
        public DateTime LastRun { get; set; }
        public bool IsRunning { get; set; }
        public int TotalItemsProcessed { get; set; }
        public int NewItemsAdded { get; set; }
        public int DuplicatesSkipped { get; set; }
        public int ErrorsEncountered { get; set; }
        public string? LastError { get; set; }
        public TimeSpan Duration { get; set; }
    }
}