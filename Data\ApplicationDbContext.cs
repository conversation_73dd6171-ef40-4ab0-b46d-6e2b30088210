﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using NewsSite.Models;

namespace NewsSite.Data
{
    public class ApplicationDbContext : IdentityDbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // DbSets for all models
        public DbSet<Article> Articles { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Source> Sources { get; set; }
        public DbSet<SourceConfiguration> SourceConfigurations { get; set; }
        public DbSet<Tag> Tags { get; set; }
        public DbSet<ArticleTag> ArticleTags { get; set; }
        public DbSet<ContentMetadata> ContentMetadata { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            ConfigureArticle(modelBuilder);
            ConfigureCategory(modelBuilder);
            ConfigureSource(modelBuilder);
            ConfigureSourceConfiguration(modelBuilder);
            ConfigureTag(modelBuilder);
            ConfigureArticleTag(modelBuilder);
            ConfigureContentMetadata(modelBuilder);
            SeedData(modelBuilder);
        }

        private void ConfigureArticle(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Article>(entity =>
            {
                // Primary key
                entity.HasKey(e => e.Id);

                // Properties
                entity.Property(e => e.Title).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Slug).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Summary).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.OriginalUrl).IsRequired().HasMaxLength(2000);
                entity.Property(e => e.ImageUrl).HasMaxLength(2000);
                entity.Property(e => e.ImageAlt).HasMaxLength(500);
                entity.Property(e => e.Author).HasMaxLength(200);
                entity.Property(e => e.ExternalId).HasMaxLength(200);
                entity.Property(e => e.ExternalData).HasMaxLength(500);
                entity.Property(e => e.MetaTitle).HasMaxLength(200);
                entity.Property(e => e.MetaDescription).HasMaxLength(500);
                entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100).HasDefaultValue("System");
                entity.Property(e => e.ModifiedBy).HasMaxLength(100);
                entity.Property(e => e.DeletedBy).HasMaxLength(100);

                // Relationships
                entity.HasOne(e => e.Category)
                    .WithMany(c => c.Articles)
                    .HasForeignKey(e => e.CategoryId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Source)
                    .WithMany(s => s.Articles)
                    .HasForeignKey(e => e.SourceId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Metadata)
                    .WithOne(m => m.Article)
                    .HasForeignKey<ContentMetadata>(m => m.ArticleId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Indexes for performance
                entity.HasIndex(e => e.PublishedDate).HasDatabaseName("IX_Articles_PublishedDate");
                entity.HasIndex(e => e.CategoryId).HasDatabaseName("IX_Articles_CategoryId");
                entity.HasIndex(e => e.SourceId).HasDatabaseName("IX_Articles_SourceId");
                entity.HasIndex(e => new { e.Status, e.IsDeleted }).HasDatabaseName("IX_Articles_Status_IsDeleted");
                entity.HasIndex(e => e.Slug).IsUnique().HasDatabaseName("IX_Articles_Slug");
                entity.HasIndex(e => e.ExternalId).HasDatabaseName("IX_Articles_ExternalId");
                entity.HasIndex(e => new { e.IsFeatured, e.PublishedDate }).HasDatabaseName("IX_Articles_Featured_PublishedDate");
                entity.HasIndex(e => new { e.IsTrending, e.PublishedDate }).HasDatabaseName("IX_Articles_Trending_PublishedDate");

                // Soft delete filter
                entity.HasQueryFilter(e => !e.IsDeleted);
            });
        }

        private void ConfigureCategory(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Category>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Slug).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Color).HasMaxLength(20);
                entity.Property(e => e.Icon).HasMaxLength(50);

                // Indexes
                entity.HasIndex(e => e.Slug).IsUnique().HasDatabaseName("IX_Categories_Slug");
                entity.HasIndex(e => new { e.IsActive, e.DisplayOrder }).HasDatabaseName("IX_Categories_Active_DisplayOrder");
            });
        }

        private void ConfigureSource(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Source>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Url).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.ApiKey).HasMaxLength(200);
                entity.Property(e => e.ApiSecret).HasMaxLength(200);
                entity.Property(e => e.ContentFilterKeywords).HasMaxLength(1000);
                entity.Property(e => e.LastErrorMessage).HasMaxLength(1000);
                entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ModifiedBy).HasMaxLength(100);

                // One-to-one with SourceConfiguration
                entity.HasOne(e => e.Configuration)
                    .WithOne(c => c.Source)
                    .HasForeignKey<SourceConfiguration>(c => c.SourceId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Indexes
                entity.HasIndex(e => new { e.IsActive, e.Type }).HasDatabaseName("IX_Sources_Active_Type");
                entity.HasIndex(e => e.LastSyncDate).HasDatabaseName("IX_Sources_LastSyncDate");
            });
        }

        private void ConfigureSourceConfiguration(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<SourceConfiguration>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.CustomHeaders).HasMaxLength(1000);
                entity.Property(e => e.XPathSelectors).HasMaxLength(2000);
                entity.Property(e => e.NotificationEmails).HasMaxLength(500);

                // Indexes
                entity.HasIndex(e => e.SourceId).IsUnique().HasDatabaseName("IX_SourceConfigurations_SourceId");
                entity.HasIndex(e => new { e.Priority, e.UpdateIntervalMinutes }).HasDatabaseName("IX_SourceConfigurations_Priority_UpdateInterval");
            });
        }

        private void ConfigureTag(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Tag>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Slug).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Color).HasMaxLength(20);

                // Indexes
                entity.HasIndex(e => e.Slug).IsUnique().HasDatabaseName("IX_Tags_Slug");
                entity.HasIndex(e => new { e.IsActive, e.UsageCount }).HasDatabaseName("IX_Tags_Active_UsageCount");
            });
        }

        private void ConfigureArticleTag(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ArticleTag>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Relationships
                entity.HasOne(e => e.Article)
                    .WithMany(a => a.ArticleTags)
                    .HasForeignKey(e => e.ArticleId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Tag)
                    .WithMany(t => t.ArticleTags)
                    .HasForeignKey(e => e.TagId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Composite unique index
                entity.HasIndex(e => new { e.ArticleId, e.TagId }).IsUnique().HasDatabaseName("IX_ArticleTags_ArticleId_TagId");
            });
        }

        private void ConfigureContentMetadata(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ContentMetadata>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.ContentHash).IsRequired().HasMaxLength(64);
                entity.Property(e => e.UrlHash).HasMaxLength(64);
                entity.Property(e => e.Language).HasMaxLength(10);
                entity.Property(e => e.MetaKeywords).HasMaxLength(1000);
                entity.Property(e => e.MetaDescription).HasMaxLength(500);
                entity.Property(e => e.QualityScore).HasColumnType("decimal(5,2)");
                entity.Property(e => e.TrendingScore).HasColumnType("decimal(10,4)");

                // Self-referencing relationship for duplicates
                entity.HasOne(e => e.OriginalArticle)
                    .WithMany()
                    .HasForeignKey(e => e.OriginalArticleId)
                    .OnDelete(DeleteBehavior.NoAction);

                // Indexes for duplicate detection and performance
                entity.HasIndex(e => e.ArticleId).IsUnique().HasDatabaseName("IX_ContentMetadata_ArticleId");
                entity.HasIndex(e => e.ContentHash).HasDatabaseName("IX_ContentMetadata_ContentHash");
                entity.HasIndex(e => e.UrlHash).HasDatabaseName("IX_ContentMetadata_UrlHash");
                entity.HasIndex(e => new { e.IsDuplicate, e.OriginalArticleId }).HasDatabaseName("IX_ContentMetadata_Duplicate_Original");
                entity.HasIndex(e => new { e.TrendingScore, e.TrendingCalculatedDate }).HasDatabaseName("IX_ContentMetadata_Trending");
            });
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed predefined categories
            modelBuilder.Entity<Category>().HasData(
                new Category
                {
                    Id = 1,
                    Name = "Breaking AI News",
                    Description = "Latest breaking news and developments in artificial intelligence",
                    Slug = "breaking-ai-news",
                    Color = "#ff4444",
                    Icon = "fas fa-bolt",
                    DisplayOrder = 1,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                },
                new Category
                {
                    Id = 2,
                    Name = "YouTube AI Discoveries",
                    Description = "Curated AI content from top YouTube channels and creators",
                    Slug = "youtube-ai-discoveries",
                    Color = "#ff0000",
                    Icon = "fab fa-youtube",
                    DisplayOrder = 2,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                },
                new Category
                {
                    Id = 3,
                    Name = "Research & Papers",
                    Description = "Academic research, papers, and scientific breakthroughs in AI",
                    Slug = "research-papers",
                    Color = "#4CAF50",
                    Icon = "fas fa-flask",
                    DisplayOrder = 3,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                },
                new Category
                {
                    Id = 4,
                    Name = "Agentic Platforms",
                    Description = "AI agents, autonomous systems, and intelligent platforms",
                    Slug = "agentic-platforms",
                    Color = "#2196F3",
                    Icon = "fas fa-robot",
                    DisplayOrder = 4,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                },
                new Category
                {
                    Id = 5,
                    Name = "AI Development Tools",
                    Description = "Tools, frameworks, and resources for AI developers",
                    Slug = "ai-development-tools",
                    Color = "#9C27B0",
                    Icon = "fas fa-tools",
                    DisplayOrder = 5,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                },
                new Category
                {
                    Id = 6,
                    Name = "Trending Open Source",
                    Description = "Popular open-source AI projects and repositories",
                    Slug = "trending-open-source",
                    Color = "#FF9800",
                    Icon = "fab fa-github",
                    DisplayOrder = 6,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                }
            );

            // Seed common tags
            modelBuilder.Entity<Tag>().HasData(
                new Tag { Id = 1, Name = "Machine Learning", Slug = "machine-learning", Color = "#4CAF50", IsActive = true, CreatedDate = DateTime.UtcNow },
                new Tag { Id = 2, Name = "Deep Learning", Slug = "deep-learning", Color = "#2196F3", IsActive = true, CreatedDate = DateTime.UtcNow },
                new Tag { Id = 3, Name = "Natural Language Processing", Slug = "nlp", Color = "#FF9800", IsActive = true, CreatedDate = DateTime.UtcNow },
                new Tag { Id = 4, Name = "Computer Vision", Slug = "computer-vision", Color = "#9C27B0", IsActive = true, CreatedDate = DateTime.UtcNow },
                new Tag { Id = 5, Name = "Large Language Models", Slug = "llm", Color = "#FF5722", IsActive = true, CreatedDate = DateTime.UtcNow },
                new Tag { Id = 6, Name = "AI Ethics", Slug = "ai-ethics", Color = "#607D8B", IsActive = true, CreatedDate = DateTime.UtcNow },
                new Tag { Id = 7, Name = "Generative AI", Slug = "generative-ai", Color = "#E91E63", IsActive = true, CreatedDate = DateTime.UtcNow },
                new Tag { Id = 8, Name = "Robotics", Slug = "robotics", Color = "#795548", IsActive = true, CreatedDate = DateTime.UtcNow }
            );
        }
    }
}
