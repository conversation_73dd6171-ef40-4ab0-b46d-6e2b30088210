# AI Frontiers - Azure Deployment Guide

This guide provides comprehensive instructions for deploying the AI Frontiers platform to Microsoft Azure using Azure Web App and Azure SQL Database.

## Prerequisites

### Required Tools
- Azure CLI or Azure PowerShell
- .NET 8.0 SDK
- SQL Server Management Studio (optional)
- Git
- Visual Studio Code or Visual Studio

### Azure Resources Required
- Azure Subscription with Contributor access
- Resource Group creation permissions
- Azure SQL Database permissions
- Azure Key Vault permissions
- Azure App Service permissions

## Architecture Overview

The AI Frontiers platform uses the following Azure services:

### Core Services
- **Azure App Service**: Web application hosting with auto-scaling
- **Azure SQL Database**: Managed relational database
- **Azure Key Vault**: Secure secret and key management
- **Application Insights**: Application performance monitoring

### Supporting Services
- **Azure Storage Account**: Data protection keys and file storage
- **Azure CDN**: Content delivery network for static assets
- **Managed Identity**: Secure service-to-service authentication

## Deployment Options

### Option 1: Automated Deployment via GitHub Actions (Recommended)

1. **Set up GitHub Secrets**:
   ```
   AZURE_CREDENTIALS              # Service Principal JSON
   AZURE_SUBSCRIPTION_ID          # Azure Subscription ID
   SQL_ADMIN_PASSWORD            # Secure SQL admin password
   YOUTUBE_API_KEY               # YouTube Data API key
   GITHUB_API_TOKEN              # GitHub API token (optional)
   APPLICATION_INSIGHTS_CONNECTION_STRING  # App Insights connection string
   AZURE_SQL_CONNECTION_STRING   # Complete Azure SQL connection string
   ```

2. **Trigger Deployment**:
   - Push to `main` branch for production deployment
   - Push to `develop` branch for staging deployment

3. **Monitor Deployment**:
   - Check GitHub Actions workflow status
   - Verify health checks pass
   - Confirm application accessibility

### Option 2: Manual Deployment via PowerShell

1. **Run the deployment script**:
   ```powershell
   .\Deploy-ToAzure.ps1 -Environment "prod" -SubscriptionId "your-subscription-id" -ResourceGroupName "rg-aifrontiers-prod" -SqlAdminPassword (ConvertTo-SecureString "YourSecurePassword123!" -AsPlainText -Force)
   ```

2. **Deployment stages**:
   - Infrastructure provisioning
   - Database migration
   - Application deployment
   - Health verification

### Option 3: Manual Step-by-Step Deployment

#### Step 1: Deploy Infrastructure

```bash
# Login to Azure
az login

# Create resource group
az group create --name rg-aifrontiers-prod --location "East US 2"

# Deploy ARM template
az deployment group create \
  --resource-group rg-aifrontiers-prod \
  --template-file azure-infrastructure.json \
  --parameters azure-infrastructure.parameters.json \
  --parameters sqlAdminPassword="YourSecurePassword123!"
```

#### Step 2: Configure Key Vault

```bash
# Set API keys in Key Vault
az keyvault secret set --vault-name aifrontiers-kv-prod --name "YouTube-ApiKey" --value "your-youtube-api-key"
az keyvault secret set --vault-name aifrontiers-kv-prod --name "GitHub-ApiToken" --value "your-github-token"

# Grant access to Web App managed identity
WEBAPP_PRINCIPAL_ID=$(az webapp identity show --name aifrontiers-web-prod --resource-group rg-aifrontiers-prod --query principalId --output tsv)
az keyvault set-policy --name aifrontiers-kv-prod --object-id $WEBAPP_PRINCIPAL_ID --secret-permissions get list
```

#### Step 3: Deploy Database

```bash
# Run EF Core migrations
dotnet ef database update --connection "your-azure-sql-connection-string"

# Run optimization script
sqlcmd -S aifrontiers-sql-prod.database.windows.net -d aifrontiers-db-prod -U aifrontiers-admin -P YourSecurePassword123! -i deploy-database.sql
```

#### Step 4: Deploy Application

```bash
# Build and publish
dotnet publish --configuration Release --output ./publish

# Deploy to Azure
az webapp deploy --resource-group rg-aifrontiers-prod --name aifrontiers-web-prod --src-path ./publish.zip --type zip
```

## Configuration Details

### Environment-Specific Settings

#### Production (appsettings.Production.json)
- Connection strings from Azure Key Vault
- Application Insights enabled
- Security headers enforced
- Rate limiting enabled
- Performance optimizations active

#### Required App Settings
```json
{
  "ASPNETCORE_ENVIRONMENT": "Production",
  "KeyVault__VaultUri": "https://aifrontiers-kv-prod.vault.azure.net/",
  "ApplicationInsights__ConnectionString": "from-key-vault"
}
```

### Database Configuration

#### Connection String Format
```
Server=tcp:aifrontiers-sql-prod.database.windows.net,1433;Initial Catalog=aifrontiers-db-prod;Persist Security Info=False;User ID=aifrontiers-admin;Password=YourPassword;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=60;
```

#### Performance Settings
- Query Store enabled for performance monitoring
- Automatic tuning enabled
- Optimized indexes for common queries
- Full-text search enabled for article content

### Security Configuration

#### Network Security
- HTTPS enforced (HTTP redirects to HTTPS)
- Security headers implemented
- CORS properly configured
- Rate limiting active

#### Data Protection
- Keys stored in Azure Key Vault
- Managed Identity for service authentication
- SQL connection encrypted
- Sensitive data logging disabled in production

#### API Security
- API keys stored in Key Vault
- Rate limiting per IP address
- Request validation enabled
- SQL injection protection active

## Monitoring and Maintenance

### Health Monitoring
- Health check endpoint: `/health`
- Application Insights automatic monitoring
- Custom performance counters
- SQL Database monitoring

### Logging
- Structured logging with Serilog
- Application Insights integration
- Azure diagnostics enabled
- Log retention configured

### Backup Strategy
- Azure SQL Database automated backups
- Point-in-time recovery available
- Cross-region backup replication
- Application settings backup

### Performance Optimization
- Response compression enabled
- Static file caching (30 days)
- CDN for static assets
- Database query optimization
- Memory caching implemented

## Troubleshooting

### Common Issues

#### Deployment Failures
1. **ARM Template Errors**: Check parameter values and resource naming conflicts
2. **SQL Connection Issues**: Verify firewall rules and connection strings
3. **Key Vault Access**: Ensure managed identity permissions are set correctly

#### Runtime Issues
1. **Application Won't Start**: Check Application Insights logs and health endpoint
2. **Database Connection Fails**: Verify connection string and SQL server accessibility
3. **API Keys Not Loading**: Check Key Vault configuration and access policies

#### Performance Issues
1. **Slow Database Queries**: Review Query Store and missing index recommendations
2. **High Memory Usage**: Check memory cache configuration and limits
3. **Slow Page Load**: Verify CDN configuration and static file caching

### Diagnostic Commands

```bash
# Check application logs
az webapp log tail --name aifrontiers-web-prod --resource-group rg-aifrontiers-prod

# Check health endpoint
curl https://aifrontiers-web-prod.azurewebsites.net/health

# View deployment logs
az webapp deployment log show --name aifrontiers-web-prod --resource-group rg-aifrontiers-prod
```

## Scaling and Optimization

### Auto-Scaling
- App Service Plan auto-scaling rules
- Scale based on CPU and memory usage
- Scale out during peak hours
- Scale in during low traffic periods

### Performance Tuning
- Database performance recommendations
- Query optimization analysis
- Application Insights performance data
- CDN cache hit ratio monitoring

### Cost Optimization
- Right-size App Service Plan
- Optimize SQL Database tier
- Review Azure Advisor recommendations
- Monitor and adjust scaling rules

## Security Best Practices

### Access Control
- Use managed identities where possible
- Implement least privilege access
- Regular access reviews
- Multi-factor authentication enabled

### Data Protection
- Encrypt data at rest and in transit
- Regular security assessments
- Vulnerability scanning
- Compliance monitoring

### Monitoring
- Security event monitoring
- Failed authentication tracking
- Unusual activity alerts
- Regular security reviews

## Support and Maintenance

### Regular Tasks
- Monitor application performance
- Review security logs
- Update dependencies
- Database maintenance
- Backup verification

### Update Process
1. Test changes in staging environment
2. Deploy via CI/CD pipeline
3. Verify health checks
4. Monitor for issues
5. Rollback if necessary

### Contact Information
- Azure Support: Use Azure Portal support tickets
- Application Issues: Check Application Insights and logs
- Emergency: Follow incident response procedures

---

For additional support or questions about the deployment process, refer to the Azure documentation or contact the development team.