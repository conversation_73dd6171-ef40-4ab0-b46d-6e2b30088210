using Microsoft.Extensions.Caching.Memory;
using NewsSite.Services.External;
using NewsSite.Models;

namespace NewsSite.Services.BackgroundServices
{
    public class ContentAggregationService : IContentAggregationService
    {
        private readonly ISourceService _sourceService;
        private readonly IRssService _rssService;
        private readonly IYouTubeService _youTubeService;
        private readonly IGitHubService _gitHubService;
        private readonly IArxivService _arxivService;
        private readonly IContentProcessor _contentProcessor;
        private readonly ILogger<ContentAggregationService> _logger;
        private readonly IMemoryCache _cache;

        private static readonly SemaphoreSlim _aggregationSemaphore = new(1, 1);
        private const string LAST_AGGREGATION_KEY = "last_aggregation_status";

        public ContentAggregationService(
            ISourceService sourceService,
            IRssService rssService,
            IYouTubeService youTubeService,
            IGitHubService gitHubService,
            IArxivService arxivService,
            IContentProcessor contentProcessor,
            ILogger<ContentAggregationService> logger,
            IMemoryCache cache)
        {
            _sourceService = sourceService;
            _rssService = rssService;
            _youTubeService = youTubeService;
            _gitHubService = gitHubService;
            _arxivService = arxivService;
            _contentProcessor = contentProcessor;
            _logger = logger;
            _cache = cache;
        }

        public async Task AggregateAllContentAsync()
        {
            if (!await _aggregationSemaphore.WaitAsync(TimeSpan.FromSeconds(5)))
            {
                _logger.LogWarning("Content aggregation already in progress, skipping this run");
                return;
            }

            var startTime = DateTime.UtcNow;
            var status = new AggregationStatus
            {
                LastRun = startTime,
                IsRunning = true
            };

            _cache.Set(LAST_AGGREGATION_KEY, status, TimeSpan.FromHours(24));

            try
            {
                _logger.LogInformation("Starting content aggregation for all sources");

                var sources = await _sourceService.GetActiveSourcesAsync();
                var tasks = new List<Task>();

                foreach (var source in sources)
                {
                    // Check if source should be updated based on its configuration
                    if (ShouldUpdateSource(source))
                    {
                        tasks.Add(ProcessSourceAsync(source, status));
                    }
                }

                await Task.WhenAll(tasks);

                status.Duration = DateTime.UtcNow - startTime;
                status.IsRunning = false;

                _logger.LogInformation(
                    "Content aggregation completed. Processed: {TotalItems}, New: {NewItems}, Duplicates: {Duplicates}, Errors: {Errors}",
                    status.TotalItemsProcessed, status.NewItemsAdded, status.DuplicatesSkipped, status.ErrorsEncountered);
            }
            catch (Exception ex)
            {
                status.IsRunning = false;
                status.LastError = ex.Message;
                status.ErrorsEncountered++;
                _logger.LogError(ex, "Error during content aggregation");
                throw;
            }
            finally
            {
                _cache.Set(LAST_AGGREGATION_KEY, status, TimeSpan.FromHours(24));
                _aggregationSemaphore.Release();
            }
        }

        public async Task AggregateContentFromSourceAsync(int sourceId)
        {
            _logger.LogInformation("Starting content aggregation for source {SourceId}", sourceId);

            var source = await _sourceService.GetByIdAsync(sourceId);
            if (source == null)
            {
                _logger.LogWarning("Source {SourceId} not found", sourceId);
                return;
            }

            var status = new AggregationStatus
            {
                LastRun = DateTime.UtcNow,
                IsRunning = true
            };

            await ProcessSourceAsync(source, status);
        }

        public async Task<AggregationStatus> GetLastAggregationStatusAsync()
        {
            return _cache.Get<AggregationStatus>(LAST_AGGREGATION_KEY) ?? new AggregationStatus();
        }

        private async Task ProcessSourceAsync(Source source, AggregationStatus status)
        {
            try
            {
                _logger.LogDebug("Processing source: {SourceName} ({SourceType})", source.Name, source.SourceType);

                var articles = source.SourceType.ToLower() switch
                {
                    "rss" => await _rssService.FetchArticlesAsync(source.Url),
                    "youtube" => await _youTubeService.FetchVideosAsync(source.Configuration),
                    "github" => await _gitHubService.FetchTrendingRepositoriesAsync(source.Configuration),
                    "arxiv" => await _arxivService.FetchPapersAsync(source.Configuration),
                    _ => throw new NotSupportedException($"Source type {source.SourceType} is not supported")
                };

                foreach (var article in articles)
                {
                    try
                    {
                        var processedArticle = await _contentProcessor.ProcessArticleAsync(article);
                        if (processedArticle != null)
                        {
                            status.TotalItemsProcessed++;
                            if (processedArticle.IsNew)
                            {
                                status.NewItemsAdded++;
                            }
                            else
                            {
                                status.DuplicatesSkipped++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        status.ErrorsEncountered++;
                        _logger.LogError(ex, "Error processing article: {Title}", article.Title);
                    }
                }

                // Update source last sync time
                source.LastSyncDate = DateTime.UtcNow;
                await _sourceService.UpdateAsync(source);
            }
            catch (Exception ex)
            {
                status.ErrorsEncountered++;
                status.LastError = ex.Message;
                _logger.LogError(ex, "Error processing source {SourceId}: {SourceName}", source.Id, source.Name);
            }
        }

        private bool ShouldUpdateSource(Source source)
        {
            if (!source.IsActive || source.Configuration == null)
                return false;

            var updateInterval = source.Configuration.UpdateIntervalMinutes ?? 60;
            var lastSync = source.LastSyncDate ?? DateTime.MinValue;

            return DateTime.UtcNow - lastSync >= TimeSpan.FromMinutes(updateInterval);
        }
    }
}