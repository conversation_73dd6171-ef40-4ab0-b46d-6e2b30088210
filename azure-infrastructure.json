{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"appName": {"type": "string", "defaultValue": "aifrontiers", "metadata": {"description": "Base name for all resources"}}, "environment": {"type": "string", "defaultValue": "prod", "allowedValues": ["dev", "staging", "prod"], "metadata": {"description": "Environment name"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for all resources"}}, "sqlAdminUsername": {"type": "string", "metadata": {"description": "SQL Server admin username"}}, "sqlAdminPassword": {"type": "securestring", "metadata": {"description": "SQL Server admin password"}}, "appServicePlanTier": {"type": "string", "defaultValue": "Standard", "allowedValues": ["Basic", "Standard", "Premium", "PremiumV2", "PremiumV3"], "metadata": {"description": "App Service Plan tier"}}, "appServicePlanSize": {"type": "string", "defaultValue": "S1", "metadata": {"description": "App Service Plan size"}}, "sqlDatabaseTier": {"type": "string", "defaultValue": "Standard", "allowedValues": ["Basic", "Standard", "Premium"], "metadata": {"description": "SQL Database tier"}}, "sqlDatabaseSize": {"type": "string", "defaultValue": "S2", "metadata": {"description": "SQL Database size"}}}, "variables": {"appServicePlanName": "[concat(parameters('appName'), '-asp-', parameters('environment'))]", "webAppName": "[concat(parameters('appName'), '-web-', parameters('environment'))]", "sqlServerName": "[concat(parameters('appName'), '-sql-', parameters('environment'))]", "sqlDatabaseName": "[concat(parameters('appName'), '-db-', parameters('environment'))]", "keyVaultName": "[concat(parameters('appName'), '-kv-', parameters('environment'))]", "storageAccountName": "[concat(replace(parameters('appName'), '-', ''), 'st', parameters('environment'))]", "applicationInsightsName": "[concat(parameters('appName'), '-ai-', parameters('environment'))]", "cdnProfileName": "[concat(parameters('appName'), '-cdn-', parameters('environment'))]", "cdnEndpointName": "[concat(parameters('appName'), '-endpoint-', parameters('environment'))]"}, "resources": [{"type": "Microsoft.Storage/storageAccounts", "apiVersion": "2021-09-01", "name": "[variables('storageAccountName')]", "location": "[parameters('location')]", "sku": {"name": "Standard_LRS"}, "kind": "StorageV2", "properties": {"accessTier": "Hot", "supportsHttpsTrafficOnly": true, "minimumTlsVersion": "TLS1_2", "allowBlobPublicAccess": false, "allowSharedKeyAccess": true, "networkAcls": {"bypass": "AzureServices", "defaultAction": "Allow"}}}, {"type": "Microsoft.Storage/storageAccounts/blobServices", "apiVersion": "2021-09-01", "name": "[concat(variables('storageAccountName'), '/default')]", "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', variables('storageAccountName'))]"], "properties": {"deleteRetentionPolicy": {"enabled": true, "days": 30}}}, {"type": "Microsoft.Storage/storageAccounts/blobServices/containers", "apiVersion": "2021-09-01", "name": "[concat(variables('storageAccountName'), '/default/dataprotection')]", "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts/blobServices', variables('storageAccountName'), 'default')]"], "properties": {"publicAccess": "None"}}, {"type": "Microsoft.KeyVault/vaults", "apiVersion": "2021-11-01-preview", "name": "[variables('keyVaultName')]", "location": "[parameters('location')]", "properties": {"sku": {"family": "A", "name": "standard"}, "tenantId": "[subscription().tenantId]", "accessPolicies": [], "enabledForDeployment": false, "enabledForDiskEncryption": false, "enabledForTemplateDeployment": true, "enableSoftDelete": true, "softDeleteRetentionInDays": 30, "enableRbacAuthorization": true, "networkAcls": {"bypass": "AzureServices", "defaultAction": "Allow"}}}, {"type": "Microsoft.Sql/servers", "apiVersion": "2021-11-01", "name": "[variables('sqlServerName')]", "location": "[parameters('location')]", "properties": {"administratorLogin": "[parameters('sqlAdminUsername')]", "administratorLoginPassword": "[parameters('sqlAdminPassword')]", "version": "12.0", "minimalTlsVersion": "1.2", "publicNetworkAccess": "Enabled"}}, {"type": "Microsoft.Sql/servers/firewallRules", "apiVersion": "2021-11-01", "name": "[concat(variables('sqlServerName'), '/AllowAzureServices')]", "dependsOn": ["[resourceId('Microsoft.Sql/servers', variables('sqlServerName'))]"], "properties": {"startIpAddress": "0.0.0.0", "endIpAddress": "0.0.0.0"}}, {"type": "Microsoft.Sql/servers/databases", "apiVersion": "2021-11-01", "name": "[concat(variables('sqlServerName'), '/', variables('sqlDatabaseName'))]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Sql/servers', variables('sqlServerName'))]"], "sku": {"name": "[parameters('sqlDatabaseSize')]", "tier": "[parameters('sqlDatabaseTier')]"}, "properties": {"collation": "SQL_Latin1_General_CP1_CI_AS", "maxSizeBytes": 268435456000, "catalogCollation": "SQL_Latin1_General_CP1_CI_AS", "zoneRedundant": false, "readScale": "Disabled", "requestedBackupStorageRedundancy": "Geo", "isLedgerOn": false}}, {"type": "Microsoft.Insights/components", "apiVersion": "2020-02-02", "name": "[variables('applicationInsightsName')]", "location": "[parameters('location')]", "kind": "web", "properties": {"Application_Type": "web", "Request_Source": "rest", "RetentionInDays": 90, "publicNetworkAccessForIngestion": "Enabled", "publicNetworkAccessForQuery": "Enabled"}}, {"type": "Microsoft.Web/serverfarms", "apiVersion": "2021-03-01", "name": "[variables('appServicePlanName')]", "location": "[parameters('location')]", "sku": {"name": "[parameters('appServicePlanSize')]", "tier": "[parameters('appServicePlanTier')]"}, "properties": {"targetWorkerCount": 1, "targetWorkerSizeId": 0}}, {"type": "Microsoft.Web/sites", "apiVersion": "2021-03-01", "name": "[variables('webAppName')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Web/serverfarms', variables('appServicePlanName'))]", "[resourceId('Microsoft.Sql/servers/databases', variables('sqlServerName'), variables('sqlDatabaseName'))]", "[resourceId('Microsoft.KeyVault/vaults', variables('keyVaultName'))]", "[resourceId('Microsoft.Insights/components', variables('applicationInsightsName'))]", "[resourceId('Microsoft.Storage/storageAccounts', variables('storageAccountName'))]"], "identity": {"type": "SystemAssigned"}, "properties": {"serverFarmId": "[resourceId('Microsoft.Web/serverfarms', variables('appServicePlanName'))]", "httpsOnly": true, "siteConfig": {"netFrameworkVersion": "v8.0", "metadata": [{"name": "CURRENT_STACK", "value": "dotnet"}], "alwaysOn": true, "ftpsState": "Disabled", "minTlsVersion": "1.2", "scmMinTlsVersion": "1.2", "http20Enabled": true, "appSettings": [{"name": "ASPNETCORE_ENVIRONMENT", "value": "Production"}, {"name": "WEBSITE_HTTPLOGGING_RETENTION_DAYS", "value": "7"}, {"name": "APPINSIGHTS_INSTRUMENTATIONKEY", "value": "[reference(resourceId('Microsoft.Insights/components', variables('applicationInsightsName'))).InstrumentationKey]"}, {"name": "APPLICATIONINSIGHTS_CONNECTION_STRING", "value": "[reference(resourceId('Microsoft.Insights/components', variables('applicationInsightsName'))).ConnectionString]"}, {"name": "Key<PERSON><PERSON>__<PERSON><PERSON><PERSON><PERSON>", "value": "[reference(resourceId('Microsoft.KeyVault/vaults', variables('keyVaultName'))).vaultUri]"}], "connectionStrings": [{"name": "DefaultConnection", "connectionString": "[concat('Server=tcp:', reference(resourceId('Microsoft.Sql/servers', variables('sqlServerName'))).fullyQualifiedDomainName, ',1433;Initial Catalog=', variables('sqlDatabaseName'), ';Persist Security Info=False;User ID=', parameters('sqlAdminUsername'), ';Password=', parameters('sqlAdminPassword'), ';MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=60;')]", "type": "SQLAzure"}, {"name": "DataProtectionStorage", "connectionString": "[concat('DefaultEndpointsProtocol=https;AccountName=', variables('storageAccountName'), ';AccountKey=', listKeys(resourceId('Microsoft.Storage/storageAccounts', variables('storageAccountName')), '2021-09-01').keys[0].value, ';EndpointSuffix=core.windows.net')]", "type": "Custom"}]}}}, {"type": "Microsoft.Cdn/profiles", "apiVersion": "2021-06-01", "name": "[variables('cdnProfileName')]", "location": "Global", "sku": {"name": "Standard_Microsoft"}, "properties": {}}, {"type": "Microsoft.Cdn/profiles/endpoints", "apiVersion": "2021-06-01", "name": "[concat(variables('cdnProfileName'), '/', variables('cdnEndpointName'))]", "location": "Global", "dependsOn": ["[resourceId('Microsoft.Cdn/profiles', variables('cdnProfileName'))]", "[resourceId('Microsoft.Web/sites', variables('webAppName'))]"], "properties": {"originHostHeader": "[reference(resourceId('Microsoft.Web/sites', variables('webAppName'))).defaultHostName]", "isHttpAllowed": false, "isHttpsAllowed": true, "queryStringCachingBehavior": "IgnoreQueryString", "origins": [{"name": "origin1", "properties": {"hostName": "[reference(resourceId('Microsoft.Web/sites', variables('webAppName'))).defaultHostName]", "httpPort": 80, "httpsPort": 443, "priority": 1, "weight": 1000, "enabled": true}}], "deliveryPolicy": {"rules": [{"name": "Static<PERSON><PERSON>nt", "order": 1, "conditions": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameters": {"operator": "BeginsWith", "matchValues": ["/css/", "/js/", "/images/", "/lib/"]}}], "actions": [{"name": "CacheExpiration", "parameters": {"cacheBehavior": "Override", "cacheType": "All", "cacheDuration": "30.00:00:00"}}]}]}}}], "outputs": {"webAppName": {"type": "string", "value": "[variables('webAppName')]"}, "webAppUrl": {"type": "string", "value": "[concat('https://', reference(resourceId('Microsoft.Web/sites', variables('webAppName'))).defaultHostName)]"}, "sqlServerFqdn": {"type": "string", "value": "[reference(resourceId('Microsoft.Sql/servers', variables('sqlServerName'))).fullyQualifiedDomainName]"}, "keyVaultName": {"type": "string", "value": "[variables('keyVaultName')]"}, "keyVaultUri": {"type": "string", "value": "[reference(resourceId('Microsoft.KeyVault/vaults', variables('keyVaultName'))).vaultUri]"}, "applicationInsightsInstrumentationKey": {"type": "string", "value": "[reference(resourceId('Microsoft.Insights/components', variables('applicationInsightsName'))).InstrumentationKey]"}, "cdnEndpointUrl": {"type": "string", "value": "[concat('https://', reference(resourceId('Microsoft.Cdn/profiles/endpoints', variables('cdnProfileName'), variables('cdnEndpointName'))).hostName)]"}}}