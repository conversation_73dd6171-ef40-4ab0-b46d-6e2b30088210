using NewsSite.Models;

namespace NewsSite.Services
{
    public class ArticleSearchCriteria
    {
        public string? Keywords { get; set; }
        public int? CategoryId { get; set; }
        public int? SourceId { get; set; }
        public ArticleStatus? Status { get; set; }
        public bool? IsFeatured { get; set; }
        public bool? IsBreaking { get; set; }
        public bool? IsTrending { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<int>? TagIds { get; set; }
        public string? Author { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortBy { get; set; } = "PublishedDate";
        public bool SortDescending { get; set; } = true;
    }

    public class PaginatedResult<T>
    {
        public IEnumerable<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => Page > 1;
        public bool HasNextPage => Page < TotalPages;
    }

    public interface IContentService
    {
        // Basic CRUD operations
        Task<Article?> GetByIdAsync(int id, bool includeDeleted = false);
        Task<Article?> GetBySlugAsync(string slug, bool includeDeleted = false);
        Task<PaginatedResult<Article>> GetArticlesAsync(ArticleSearchCriteria criteria);
        Task<Article> CreateArticleAsync(Article article);
        Task<Article> UpdateArticleAsync(Article article);
        Task<bool> DeleteArticleAsync(int id, string deletedBy);
        Task<bool> RestoreArticleAsync(int id);

        // Special article collections
        Task<IEnumerable<Article>> GetFeaturedArticlesAsync(int count = 10);
        Task<IEnumerable<Article>> GetBreakingNewsAsync(int count = 5);
        Task<IEnumerable<Article>> GetTrendingArticlesAsync(int count = 10);
        Task<IEnumerable<Article>> GetLatestArticlesAsync(int count = 20);
        Task<PaginatedResult<Article>> GetArticlesByCategoryAsync(int categoryId, int page = 1, int pageSize = 20);
        Task<PaginatedResult<Article>> GetArticlesBySourceAsync(int sourceId, int page = 1, int pageSize = 20);

        // Content management
        Task<bool> SetFeaturedStatusAsync(int articleId, bool isFeatured);
        Task<bool> SetBreakingNewsStatusAsync(int articleId, bool isBreaking);
        Task<bool> SetTrendingStatusAsync(int articleId, bool isTrending);
        Task<bool> UpdateArticleStatusAsync(int articleId, ArticleStatus status);
        Task<bool> UpdateArticlePriorityAsync(int articleId, int priority);

        // Duplicate detection
        Task<Article?> FindDuplicateAsync(string contentHash);
        Task<Article?> FindDuplicateByUrlAsync(string urlHash);
        Task<IEnumerable<Article>> GetDuplicatesAsync(int originalArticleId);
        Task<bool> MarkAsDuplicateAsync(int articleId, int originalArticleId);

        // Content analysis
        Task<bool> UpdateContentMetadataAsync(int articleId, ContentMetadata metadata);
        Task<bool> IncrementViewCountAsync(int articleId);
        Task<bool> IncrementClickThroughCountAsync(int articleId);
        Task<bool> UpdateEngagementMetricsAsync(int articleId, int shareCount, int likeCount, int commentCount);

        // Bulk operations
        Task<int> BulkUpdateStatusAsync(IEnumerable<int> articleIds, ArticleStatus status);
        Task<int> BulkDeleteAsync(IEnumerable<int> articleIds, string deletedBy);
        Task<int> BulkAssignCategoryAsync(IEnumerable<int> articleIds, int categoryId);

        // Search and filtering
        Task<PaginatedResult<Article>> SearchArticlesAsync(string query, ArticleSearchCriteria? criteria = null);
        Task<IEnumerable<Article>> GetRelatedArticlesAsync(int articleId, int count = 5);
        Task<IEnumerable<Article>> GetPopularArticlesAsync(int days = 7, int count = 10);

        // Statistics
        Task<int> GetTotalArticleCountAsync();
        Task<int> GetArticleCountByStatusAsync(ArticleStatus status);
        Task<int> GetArticleCountByCategoryAsync(int categoryId);
        Task<Dictionary<string, int>> GetArticleCountByDateRangeAsync(DateTime fromDate, DateTime toDate);

        // Validation
        Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null);
        Task<bool> ValidateArticleAsync(Article article);
    }
}