using System.ComponentModel.DataAnnotations;

namespace NewsSite.Models
{
    public class ArticleTag
    {
        public int Id { get; set; }

        [Required]
        public int ArticleId { get; set; }

        [Required]
        public int TagId { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public bool IsSystemGenerated { get; set; } = false; // Auto-tagged vs manually tagged

        // Navigation properties
        public virtual Article Article { get; set; } = null!;
        public virtual Tag Tag { get; set; } = null!;
    }
}