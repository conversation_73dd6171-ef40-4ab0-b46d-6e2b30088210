using NewsSite.Models;

namespace NewsSite.Services
{
    public class CategoryWithStats
    {
        public Category Category { get; set; } = null!;
        public int ArticleCount { get; set; }
        public int PublishedArticleCount { get; set; }
        public DateTime? LastArticleDate { get; set; }
    }

    public interface ICategoryService
    {
        // Basic CRUD operations
        Task<Category?> GetByIdAsync(int id);
        Task<Category?> GetBySlugAsync(string slug);
        Task<IEnumerable<Category>> GetAllCategoriesAsync();
        Task<IEnumerable<Category>> GetActiveCategoriesAsync();
        Task<Category> CreateCategoryAsync(Category category);
        Task<Category> UpdateCategoryAsync(Category category);
        Task<bool> DeleteCategoryAsync(int id);

        // Category with statistics
        Task<CategoryWithStats?> GetCategoryWithStatsAsync(int id);
        Task<CategoryWithStats?> GetCategoryWithStatsBySlugAsync(string slug);
        Task<IEnumerable<CategoryWithStats>> GetCategoriesWithStatsAsync();
        Task<IEnumerable<CategoryWithStats>> GetActiveCategoriesWithStatsAsync();

        // Category-specific article operations
        Task<PaginatedResult<Article>> GetCategoryArticlesAsync(int categoryId, int page = 1, int pageSize = 20, ArticleStatus? status = null);
        Task<PaginatedResult<Article>> GetCategoryArticlesBySlugAsync(string slug, int page = 1, int pageSize = 20, ArticleStatus? status = null);
        Task<PaginatedResult<Article>> GetCategoryArticlesWithFiltersAsync(int categoryId, int page, int pageSize, string sortBy, bool sortDesc, Dictionary<string, object> filters, ArticleStatus? status = null);
        Task<IEnumerable<Article>> GetLatestCategoryArticlesAsync(int categoryId, int count = 10);
        Task<IEnumerable<Article>> GetFeaturedCategoryArticlesAsync(int categoryId, int count = 5);

        // Category management
        Task<bool> SetCategoryActiveStatusAsync(int categoryId, bool isActive);
        Task<bool> UpdateCategoryDisplayOrderAsync(int categoryId, int displayOrder);
        Task<bool> ReorderCategoriesAsync(Dictionary<int, int> categoryDisplayOrders);

        // Statistics and analytics
        Task<int> GetArticleCountByCategoryAsync(int categoryId, ArticleStatus? status = null);
        Task<Dictionary<int, int>> GetArticleCountsForAllCategoriesAsync(ArticleStatus? status = null);
        Task<IEnumerable<Category>> GetTopCategoriesByArticleCountAsync(int count = 10, int days = 30);
        Task<Dictionary<string, int>> GetCategoryArticleCountByDateRangeAsync(int categoryId, DateTime fromDate, DateTime toDate);

        // Category navigation and hierarchy
        Task<IEnumerable<Category>> GetCategoriesForNavigationAsync();
        Task<Category?> GetCategoryForBreadcrumbAsync(string slug);

        // Validation
        Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null);
        Task<bool> ValidateCategoryAsync(Category category);
        Task<bool> CanDeleteCategoryAsync(int categoryId);

        // Bulk operations
        Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryIds, bool isActive);
        Task<int> BulkReorderCategoriesAsync(Dictionary<int, int> reorderMap);

        // Search and filtering
        Task<IEnumerable<Category>> SearchCategoriesAsync(string query);
        Task<IEnumerable<Category>> GetCategoriesByColorAsync(string color);
    }
}