@model NewsSite.Areas.Admin.Models.CreateSourceViewModel
@{
    ViewData["Title"] = "Add New Content Source";
    ViewData["Subtitle"] = "Configure a new source for content aggregation";
    Layout = "_AdminLayout";
}

<div class="row">
    <div class="col-12">
        <div class="admin-card">
            <div class="card-body">
                <form asp-action="Create" method="post" id="createSourceForm">
                    @Html.AntiForgeryToken()
                    
                    <div class="row">
                        <!-- Basic Configuration -->
                        <div class="col-xl-8">
                            <div class="admin-card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-cog me-2"></i>
                                        Basic Configuration
                                    </h5>
                                    
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label asp-for="Name" class="form-label"></label>
                                            <input asp-for="Name" class="form-control" placeholder="Enter source name">
                                            <span asp-validation-for="Name" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label asp-for="Type" class="form-label"></label>
                                            <select asp-for="Type" class="form-select" id="sourceType" onchange="updateSourceTypeFields()">
                                                <option value="">Select source type</option>
                                                @foreach (var type in Enum.GetValues<NewsSite.Models.SourceType>())
                                                {
                                                    <option value="@type">@type</option>
                                                }
                                            </select>
                                            <span asp-validation-for="Type" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-12">
                                            <label asp-for="Url" class="form-label"></label>
                                            <input asp-for="Url" class="form-control" placeholder="Enter source URL">
                                            <div class="form-text" id="urlHelp">
                                                Examples: https://feeds.example.com/rss.xml, https://youtube.com/channel/UCxxxx, https://github.com/user/repo
                                            </div>
                                            <span asp-validation-for="Url" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-12">
                                            <label asp-for="Description" class="form-label"></label>
                                            <textarea asp-for="Description" class="form-control" rows="3" placeholder="Optional description of the source"></textarea>
                                            <span asp-validation-for="Description" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Authentication & API Settings -->
                            <div class="admin-card mb-4" id="authSection" style="display: none;">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-key me-2"></i>
                                        Authentication & API Settings
                                    </h5>
                                    
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input asp-for="RequiresAuthentication" class="form-check-input" id="requiresAuth" onchange="toggleAuthFields()">
                                                <label asp-for="RequiresAuthentication" class="form-check-label"></label>
                                            </div>
                                        </div>
                                        
                                        <div id="authFields" style="display: none;">
                                            <div class="col-md-6">
                                                <label asp-for="ApiKey" class="form-label"></label>
                                                <input asp-for="ApiKey" class="form-control" type="password" placeholder="Enter API key">
                                                <span asp-validation-for="ApiKey" class="text-danger"></span>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label asp-for="ApiSecret" class="form-label"></label>
                                                <input asp-for="ApiSecret" class="form-control" type="password" placeholder="Enter API secret (if required)">
                                                <span asp-validation-for="ApiSecret" class="text-danger"></span>
                                            </div>
                                            
                                            <div class="col-12">
                                                <label asp-for="ApiSettingsJson" class="form-label"></label>
                                                <textarea asp-for="ApiSettingsJson" class="form-control" rows="3" placeholder='{"key": "value", "param": "setting"}'></textarea>
                                                <div class="form-text">Additional API settings as JSON (optional)</div>
                                                <span asp-validation-for="ApiSettingsJson" class="text-danger"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Content Processing Settings -->
                            <div class="admin-card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-filter me-2"></i>
                                        Content Processing Settings
                                    </h5>
                                    
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label asp-for="UpdateIntervalMinutes" class="form-label"></label>
                                            <div class="input-group">
                                                <input asp-for="UpdateIntervalMinutes" class="form-control" min="1" max="1440">
                                                <span class="input-group-text">minutes</span>
                                            </div>
                                            <span asp-validation-for="UpdateIntervalMinutes" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label asp-for="MaxArticlesPerSync" class="form-label"></label>
                                            <input asp-for="MaxArticlesPerSync" class="form-control" min="1" max="500">
                                            <span asp-validation-for="MaxArticlesPerSync" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label asp-for="Priority" class="form-label"></label>
                                            <select asp-for="Priority" class="form-select">
                                                <option value="1">1 - Very Low</option>
                                                <option value="2">2 - Low</option>
                                                <option value="3">3 - Below Normal</option>
                                                <option value="4">4 - Normal</option>
                                                <option value="5" selected>5 - Normal (Default)</option>
                                                <option value="6">6 - Above Normal</option>
                                                <option value="7">7 - High</option>
                                                <option value="8">8 - Very High</option>
                                                <option value="9">9 - Critical</option>
                                                <option value="10">10 - Maximum</option>
                                            </select>
                                            <span asp-validation-for="Priority" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label asp-for="ArticleRetentionDays" class="form-label"></label>
                                            <div class="input-group">
                                                <input asp-for="ArticleRetentionDays" class="form-control" min="1" max="365">
                                                <span class="input-group-text">days</span>
                                            </div>
                                            <span asp-validation-for="ArticleRetentionDays" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-12">
                                            <label asp-for="ContentFromDate" class="form-label"></label>
                                            <input asp-for="ContentFromDate" class="form-control" type="date">
                                            <div class="form-text">Only sync content published after this date (optional)</div>
                                            <span asp-validation-for="ContentFromDate" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Content Filtering -->
                            <div class="admin-card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-search me-2"></i>
                                        Content Filtering & Quality Control
                                    </h5>
                                    
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input asp-for="EnableContentFiltering" class="form-check-input">
                                                <label asp-for="EnableContentFiltering" class="form-check-label"></label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-12">
                                            <label asp-for="ContentFilterKeywords" class="form-label"></label>
                                            <textarea asp-for="ContentFilterKeywords" class="form-control" rows="2" 
                                                      placeholder="AI, machine learning, artificial intelligence, neural networks"></textarea>
                                            <div class="form-text">Keywords to filter content (comma-separated)</div>
                                            <span asp-validation-for="ContentFilterKeywords" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label asp-for="MinContentLength" class="form-label"></label>
                                            <div class="input-group">
                                                <input asp-for="MinContentLength" class="form-control" min="10" max="10000">
                                                <span class="input-group-text">characters</span>
                                            </div>
                                            <span asp-validation-for="MinContentLength" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label asp-for="MaxSummaryLength" class="form-label"></label>
                                            <div class="input-group">
                                                <input asp-for="MaxSummaryLength" class="form-control" min="50" max="2000">
                                                <span class="input-group-text">characters</span>
                                            </div>
                                            <span asp-validation-for="MaxSummaryLength" class="text-danger"></span>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input asp-for="RequireImages" class="form-check-input">
                                                <label asp-for="RequireImages" class="form-check-label"></label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input asp-for="UseAiSummarization" class="form-check-input">
                                                <label asp-for="UseAiSummarization" class="form-check-label"></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Summary & Actions -->
                        <div class="col-xl-4">
                            <div class="admin-card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-toggle-on me-2"></i>
                                        Source Status
                                    </h5>
                                    
                                    <div class="form-check form-switch mb-3">
                                        <input asp-for="IsActive" class="form-check-input" id="isActiveSwitch">
                                        <label asp-for="IsActive" class="form-check-label">
                                            <strong id="statusLabel">Active</strong>
                                        </label>
                                        <div class="form-text">Source will start syncing immediately when active</div>
                                    </div>
                                </div>
                            </div>

                            <div class="admin-card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-cogs me-2"></i>
                                        Processing Options
                                    </h5>
                                    
                                    <div class="form-check mb-2">
                                        <input asp-for="AutoCategorize" class="form-check-input">
                                        <label asp-for="AutoCategorize" class="form-check-label"></label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input asp-for="AutoGenerateTags" class="form-check-input">
                                        <label asp-for="AutoGenerateTags" class="form-check-label"></label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input asp-for="EnableDuplicateDetection" class="form-check-input">
                                        <label asp-for="EnableDuplicateDetection" class="form-check-label"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="admin-card mb-4">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-bell me-2"></i>
                                        Notifications
                                    </h5>
                                    
                                    <div class="form-check mb-2">
                                        <input asp-for="NotifyOnErrors" class="form-check-input">
                                        <label asp-for="NotifyOnErrors" class="form-check-label"></label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input asp-for="NotifyOnSuccess" class="form-check-input">
                                        <label asp-for="NotifyOnSuccess" class="form-check-label"></label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label asp-for="NotificationEmails" class="form-label"></label>
                                        <textarea asp-for="NotificationEmails" class="form-control" rows="2" 
                                                  placeholder="<EMAIL>, <EMAIL>"></textarea>
                                        <div class="form-text">Comma-separated email addresses</div>
                                        <span asp-validation-for="NotificationEmails" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="admin-card">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-server me-2"></i>
                                        Rate Limiting
                                    </h5>
                                    
                                    <div class="mb-3">
                                        <label asp-for="MaxRequestsPerHour" class="form-label"></label>
                                        <div class="input-group">
                                            <input asp-for="MaxRequestsPerHour" class="form-control" min="1" max="1000">
                                            <span class="input-group-text">req/hour</span>
                                        </div>
                                        <span asp-validation-for="MaxRequestsPerHour" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a href="/admin/sources" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        Back to Sources
                                    </a>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-info" onclick="testConfiguration()">
                                        <i class="fas fa-vial me-2"></i>
                                        Test Configuration
                                    </button>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-2"></i>
                                        Create Source
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            updateSourceTypeFields();
            toggleAuthFields();
            
            // Update status label based on switch
            const isActiveSwitch = document.getElementById('isActiveSwitch');
            const statusLabel = document.getElementById('statusLabel');
            
            isActiveSwitch.addEventListener('change', function() {
                statusLabel.textContent = this.checked ? 'Active' : 'Inactive';
                statusLabel.className = this.checked ? 'text-success' : 'text-muted';
            });
            
            // Initial status
            statusLabel.textContent = isActiveSwitch.checked ? 'Active' : 'Inactive';
            statusLabel.className = isActiveSwitch.checked ? 'text-success' : 'text-muted';
        });

        function updateSourceTypeFields() {
            const sourceType = document.getElementById('sourceType').value;
            const authSection = document.getElementById('authSection');
            const urlHelp = document.getElementById('urlHelp');
            
            // Show/hide authentication section based on source type
            const requiresAuthTypes = ['YouTubeChannel', 'TwitterAccount'];
            if (requiresAuthTypes.includes(sourceType)) {
                authSection.style.display = 'block';
                document.getElementById('requiresAuth').checked = true;
                toggleAuthFields();
            } else {
                authSection.style.display = sourceType ? 'block' : 'none';
            }
            
            // Update URL help text based on source type
            const urlExamples = {
                'RssFeed': 'Example: https://feeds.example.com/rss.xml',
                'YouTubeChannel': 'Example: https://youtube.com/channel/UCxxxx or https://youtube.com/@channelname',
                'GitHubRepository': 'Example: https://github.com/user/repo or https://api.github.com/repos/user/repo',
                'ArXivCategory': 'Example: https://arxiv.org/list/cs.AI/recent or http://export.arxiv.org/api/query?search_query=cat:cs.AI',
                'TwitterAccount': 'Example: https://twitter.com/username',
                'Website': 'Example: https://example.com/news'
            };
            
            urlHelp.textContent = urlExamples[sourceType] || 'Enter the URL for your content source';
        }

        function toggleAuthFields() {
            const requiresAuth = document.getElementById('requiresAuth').checked;
            const authFields = document.getElementById('authFields');
            authFields.style.display = requiresAuth ? 'block' : 'none';
        }

        async function testConfiguration() {
            const form = document.getElementById('createSourceForm');
            const formData = new FormData(form);
            
            // Basic validation
            const name = formData.get('Name');
            const url = formData.get('Url');
            const type = formData.get('Type');
            
            if (!name || !url || !type) {
                showAlert('warning', 'Please fill in the required fields (Name, URL, Type) before testing.');
                return;
            }
            
            const button = event.target;
            const originalHtml = button.innerHTML;
            
            try {
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testing...';
                button.disabled = true;
                
                // Simulate test - in real implementation, this would test the actual connection
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Simulate different test results
                const testResults = [
                    { success: true, message: 'Connection successful! Found 15 recent articles.' },
                    { success: false, message: 'Connection failed: Invalid URL or server not responding.' },
                    { success: true, message: 'Connection successful with warnings: API key recommended for better rate limits.' }
                ];
                
                const result = testResults[Math.floor(Math.random() * testResults.length)];
                
                if (result.success) {
                    showAlert('success', result.message);
                } else {
                    showAlert('danger', result.message);
                }
                
            } catch (error) {
                showAlert('danger', 'Test failed: ' + error.message);
            } finally {
                button.innerHTML = originalHtml;
                button.disabled = false;
            }
        }

        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            const container = document.querySelector('.admin-content');
            container.insertAdjacentHTML('afterbegin', alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 8000);
        }

        // Form validation
        document.getElementById('createSourceForm').addEventListener('submit', function(e) {
            const requiredFields = ['Name', 'Type', 'Url'];
            let isValid = true;
            
            requiredFields.forEach(field => {
                const input = document.querySelector(`[name="${field}"]`);
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showAlert('danger', 'Please fill in all required fields.');
            }
        });
    </script>

    <style>
        .form-check-input:checked {
            background-color: var(--admin-primary);
            border-color: var(--admin-primary);
        }
        
        .form-switch .form-check-input:checked {
            background-color: var(--admin-success);
            border-color: var(--admin-success);
        }
        
        .is-invalid {
            border-color: var(--admin-danger) !important;
        }
        
        .card-title {
            color: var(--admin-text-primary);
            font-weight: 600;
        }
    </style>
}