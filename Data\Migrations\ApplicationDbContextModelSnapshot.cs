﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NewsSite.Data;

#nullable disable

namespace NewsSite.Data.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.18")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("NewsSite.Models.Article", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Author")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasDefaultValue("System");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExternalData")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ExternalId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ImageAlt")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ImageUrl")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<bool>("IsBreaking")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTrending")
                        .HasColumnType("bit");

                    b.Property<string>("MetaDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("MetaTitle")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("OriginalPublishedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OriginalUrl")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<DateTime>("PublishedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("SourceId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("IX_Articles_CategoryId");

                    b.HasIndex("ExternalId")
                        .HasDatabaseName("IX_Articles_ExternalId");

                    b.HasIndex("PublishedDate")
                        .HasDatabaseName("IX_Articles_PublishedDate");

                    b.HasIndex("Slug")
                        .IsUnique()
                        .HasDatabaseName("IX_Articles_Slug");

                    b.HasIndex("SourceId")
                        .HasDatabaseName("IX_Articles_SourceId");

                    b.HasIndex("IsFeatured", "PublishedDate")
                        .HasDatabaseName("IX_Articles_Featured_PublishedDate");

                    b.HasIndex("IsTrending", "PublishedDate")
                        .HasDatabaseName("IX_Articles_Trending_PublishedDate");

                    b.HasIndex("Status", "IsDeleted")
                        .HasDatabaseName("IX_Articles_Status_IsDeleted");

                    b.ToTable("Articles");
                });

            modelBuilder.Entity("NewsSite.Models.ArticleTag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsSystemGenerated")
                        .HasColumnType("bit");

                    b.Property<int>("TagId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TagId");

                    b.HasIndex("ArticleId", "TagId")
                        .IsUnique()
                        .HasDatabaseName("IX_ArticleTags_ArticleId_TagId");

                    b.ToTable("ArticleTags");
                });

            modelBuilder.Entity("NewsSite.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Color")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<string>("Icon")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Slug")
                        .IsUnique()
                        .HasDatabaseName("IX_Categories_Slug");

                    b.HasIndex("IsActive", "DisplayOrder")
                        .HasDatabaseName("IX_Categories_Active_DisplayOrder");

                    b.ToTable("Categories");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Color = "#ff4444",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4548),
                            Description = "Latest breaking news and developments in artificial intelligence",
                            DisplayOrder = 1,
                            Icon = "fas fa-bolt",
                            IsActive = true,
                            Name = "Breaking AI News",
                            Slug = "breaking-ai-news"
                        },
                        new
                        {
                            Id = 2,
                            Color = "#ff0000",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4551),
                            Description = "Curated AI content from top YouTube channels and creators",
                            DisplayOrder = 2,
                            Icon = "fab fa-youtube",
                            IsActive = true,
                            Name = "YouTube AI Discoveries",
                            Slug = "youtube-ai-discoveries"
                        },
                        new
                        {
                            Id = 3,
                            Color = "#4CAF50",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4553),
                            Description = "Academic research, papers, and scientific breakthroughs in AI",
                            DisplayOrder = 3,
                            Icon = "fas fa-flask",
                            IsActive = true,
                            Name = "Research & Papers",
                            Slug = "research-papers"
                        },
                        new
                        {
                            Id = 4,
                            Color = "#2196F3",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4555),
                            Description = "AI agents, autonomous systems, and intelligent platforms",
                            DisplayOrder = 4,
                            Icon = "fas fa-robot",
                            IsActive = true,
                            Name = "Agentic Platforms",
                            Slug = "agentic-platforms"
                        },
                        new
                        {
                            Id = 5,
                            Color = "#9C27B0",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4557),
                            Description = "Tools, frameworks, and resources for AI developers",
                            DisplayOrder = 5,
                            Icon = "fas fa-tools",
                            IsActive = true,
                            Name = "AI Development Tools",
                            Slug = "ai-development-tools"
                        },
                        new
                        {
                            Id = 6,
                            Color = "#FF9800",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4559),
                            Description = "Popular open-source AI projects and repositories",
                            DisplayOrder = 6,
                            Icon = "fab fa-github",
                            IsActive = true,
                            Name = "Trending Open Source",
                            Slug = "trending-open-source"
                        });
                });

            modelBuilder.Entity("NewsSite.Models.ContentMetadata", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<int>("ClickThroughCount")
                        .HasColumnType("int");

                    b.Property<int>("CommentCount")
                        .HasColumnType("int");

                    b.Property<string>("ContentHash")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ExternalComments")
                        .HasColumnType("int");

                    b.Property<int?>("ExternalLikes")
                        .HasColumnType("int");

                    b.Property<int?>("ExternalShares")
                        .HasColumnType("int");

                    b.Property<bool>("HasImages")
                        .HasColumnType("bit");

                    b.Property<bool>("HasVideo")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDuplicate")
                        .HasColumnType("bit");

                    b.Property<string>("Language")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime?>("LastClickDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("LikeCount")
                        .HasColumnType("int");

                    b.Property<string>("MetaDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("MetaKeywords")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("OriginalArticleId")
                        .HasColumnType("int");

                    b.Property<decimal>("QualityScore")
                        .HasColumnType("decimal(5,2)");

                    b.Property<int>("ReadingTimeMinutes")
                        .HasColumnType("int");

                    b.Property<int>("ShareCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TrendingCalculatedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("TrendingScore")
                        .HasColumnType("decimal(10,4)");

                    b.Property<string>("UrlHash")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<int>("ViewCount")
                        .HasColumnType("int");

                    b.Property<int>("WordCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ArticleId")
                        .IsUnique()
                        .HasDatabaseName("IX_ContentMetadata_ArticleId");

                    b.HasIndex("ContentHash")
                        .HasDatabaseName("IX_ContentMetadata_ContentHash");

                    b.HasIndex("OriginalArticleId");

                    b.HasIndex("UrlHash")
                        .HasDatabaseName("IX_ContentMetadata_UrlHash");

                    b.HasIndex("IsDuplicate", "OriginalArticleId")
                        .HasDatabaseName("IX_ContentMetadata_Duplicate_Original");

                    b.HasIndex("TrendingScore", "TrendingCalculatedDate")
                        .HasDatabaseName("IX_ContentMetadata_Trending");

                    b.ToTable("ContentMetadata");
                });

            modelBuilder.Entity("NewsSite.Models.Source", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ApiKey")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ApiSecret")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("ConsecutiveFailures")
                        .HasColumnType("int");

                    b.Property<string>("ContentFilterKeywords")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EnableContentFiltering")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LastErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("LastSuccessfulSyncDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LastSyncDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("MaxRequestsPerHour")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("RequiresAuthentication")
                        .HasColumnType("bit");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.HasKey("Id");

                    b.HasIndex("LastSyncDate")
                        .HasDatabaseName("IX_Sources_LastSyncDate");

                    b.HasIndex("IsActive", "Type")
                        .HasDatabaseName("IX_Sources_Active_Type");

                    b.ToTable("Sources");
                });

            modelBuilder.Entity("NewsSite.Models.SourceConfiguration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleRetentionDays")
                        .HasColumnType("int");

                    b.Property<bool>("AutoCategorize")
                        .HasColumnType("bit");

                    b.Property<bool>("AutoGenerateTags")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ContentFromDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomHeaders")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool>("EnableDuplicateDetection")
                        .HasColumnType("bit");

                    b.Property<int>("MaxArticlesPerSync")
                        .HasColumnType("int");

                    b.Property<int>("MaxSummaryLength")
                        .HasColumnType("int");

                    b.Property<int>("MinContentLength")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NotificationEmails")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("NotifyOnErrors")
                        .HasColumnType("bit");

                    b.Property<bool>("NotifyOnSuccess")
                        .HasColumnType("bit");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<bool>("RequireImages")
                        .HasColumnType("bit");

                    b.Property<int>("SourceId")
                        .HasColumnType("int");

                    b.Property<int>("UpdateIntervalMinutes")
                        .HasColumnType("int");

                    b.Property<bool>("UseAiSummarization")
                        .HasColumnType("bit");

                    b.Property<string>("XPathSelectors")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.HasKey("Id");

                    b.HasIndex("SourceId")
                        .IsUnique()
                        .HasDatabaseName("IX_SourceConfigurations_SourceId");

                    b.HasIndex("Priority", "UpdateIntervalMinutes")
                        .HasDatabaseName("IX_SourceConfigurations_Priority_UpdateInterval");

                    b.ToTable("SourceConfigurations");
                });

            modelBuilder.Entity("NewsSite.Models.Tag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Color")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSystemGenerated")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UsageCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Slug")
                        .IsUnique()
                        .HasDatabaseName("IX_Tags_Slug");

                    b.HasIndex("IsActive", "UsageCount")
                        .HasDatabaseName("IX_Tags_Active_UsageCount");

                    b.ToTable("Tags");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Color = "#4CAF50",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4716),
                            IsActive = true,
                            IsSystemGenerated = false,
                            Name = "Machine Learning",
                            Slug = "machine-learning",
                            UsageCount = 0
                        },
                        new
                        {
                            Id = 2,
                            Color = "#2196F3",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4718),
                            IsActive = true,
                            IsSystemGenerated = false,
                            Name = "Deep Learning",
                            Slug = "deep-learning",
                            UsageCount = 0
                        },
                        new
                        {
                            Id = 3,
                            Color = "#FF9800",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4720),
                            IsActive = true,
                            IsSystemGenerated = false,
                            Name = "Natural Language Processing",
                            Slug = "nlp",
                            UsageCount = 0
                        },
                        new
                        {
                            Id = 4,
                            Color = "#9C27B0",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4722),
                            IsActive = true,
                            IsSystemGenerated = false,
                            Name = "Computer Vision",
                            Slug = "computer-vision",
                            UsageCount = 0
                        },
                        new
                        {
                            Id = 5,
                            Color = "#FF5722",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4723),
                            IsActive = true,
                            IsSystemGenerated = false,
                            Name = "Large Language Models",
                            Slug = "llm",
                            UsageCount = 0
                        },
                        new
                        {
                            Id = 6,
                            Color = "#607D8B",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4725),
                            IsActive = true,
                            IsSystemGenerated = false,
                            Name = "AI Ethics",
                            Slug = "ai-ethics",
                            UsageCount = 0
                        },
                        new
                        {
                            Id = 7,
                            Color = "#E91E63",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4726),
                            IsActive = true,
                            IsSystemGenerated = false,
                            Name = "Generative AI",
                            Slug = "generative-ai",
                            UsageCount = 0
                        },
                        new
                        {
                            Id = 8,
                            Color = "#795548",
                            CreatedDate = new DateTime(2025, 8, 5, 2, 3, 31, 266, DateTimeKind.Utc).AddTicks(4728),
                            IsActive = true,
                            IsSystemGenerated = false,
                            Name = "Robotics",
                            Slug = "robotics",
                            UsageCount = 0
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("NewsSite.Models.Article", b =>
                {
                    b.HasOne("NewsSite.Models.Category", "Category")
                        .WithMany("Articles")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("NewsSite.Models.Source", "Source")
                        .WithMany("Articles")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Source");
                });

            modelBuilder.Entity("NewsSite.Models.ArticleTag", b =>
                {
                    b.HasOne("NewsSite.Models.Article", "Article")
                        .WithMany("ArticleTags")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NewsSite.Models.Tag", "Tag")
                        .WithMany("ArticleTags")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Article");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("NewsSite.Models.ContentMetadata", b =>
                {
                    b.HasOne("NewsSite.Models.Article", "Article")
                        .WithOne("Metadata")
                        .HasForeignKey("NewsSite.Models.ContentMetadata", "ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NewsSite.Models.Article", "OriginalArticle")
                        .WithMany()
                        .HasForeignKey("OriginalArticleId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Article");

                    b.Navigation("OriginalArticle");
                });

            modelBuilder.Entity("NewsSite.Models.SourceConfiguration", b =>
                {
                    b.HasOne("NewsSite.Models.Source", "Source")
                        .WithOne("Configuration")
                        .HasForeignKey("NewsSite.Models.SourceConfiguration", "SourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Source");
                });

            modelBuilder.Entity("NewsSite.Models.Article", b =>
                {
                    b.Navigation("ArticleTags");

                    b.Navigation("Metadata");
                });

            modelBuilder.Entity("NewsSite.Models.Category", b =>
                {
                    b.Navigation("Articles");
                });

            modelBuilder.Entity("NewsSite.Models.Source", b =>
                {
                    b.Navigation("Articles");

                    b.Navigation("Configuration");
                });

            modelBuilder.Entity("NewsSite.Models.Tag", b =>
                {
                    b.Navigation("ArticleTags");
                });
#pragma warning restore 612, 618
        }
    }
}
