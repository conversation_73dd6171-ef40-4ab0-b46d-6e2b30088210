/* _content/NewsSite/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-q59lcvf1t0] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-q59lcvf1t0] {
  color: #0077cc;
}

.btn-primary[b-q59lcvf1t0] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-q59lcvf1t0], .nav-pills .show > .nav-link[b-q59lcvf1t0] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-q59lcvf1t0] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-q59lcvf1t0] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-q59lcvf1t0] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-q59lcvf1t0] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-q59lcvf1t0] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
