using NewsSite.Models;
using NewsSite.Services.Utilities;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Web;

namespace NewsSite.Services.External
{
    public class GitHubService : IGitHubService
    {
        private readonly IHttpService _httpService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<GitHubService> _logger;

        private const string GITHUB_API_BASE_URL = "https://api.github.com";
        private readonly string? _apiToken;

        public GitHubService(
            IHttpService httpService,
            IConfiguration configuration,
            ILogger<GitHubService> logger)
        {
            _httpService = httpService;
            _configuration = configuration;
            _logger = logger;
            
            // Get API token from configuration (optional for public repositories)
            _apiToken = _configuration["GitHub:ApiToken"];
            
            if (string.IsNullOrEmpty(_apiToken))
            {
                _logger.LogInformation("GitHub API token not configured. Using unauthenticated requests (lower rate limits apply).");
            }
        }

        public async Task<List<Article>> FetchTrendingRepositoriesAsync(SourceConfiguration configuration)
        {
            var articles = new List<Article>();

            try
            {
                if (configuration.ApiSettings == null)
                {
                    _logger.LogWarning("GitHub source configuration missing API settings");
                    return articles;
                }

                var sourceType = configuration.ApiSettings.GetValueOrDefault("type", "trending");
                
                switch (sourceType.ToLower())
                {
                    case "trending":
                        var language = configuration.ApiSettings.GetValueOrDefault("language", "");
                        var topic = configuration.ApiSettings.GetValueOrDefault("topic", "");
                        
                        if (!string.IsNullOrEmpty(topic))
                        {
                            articles = await FetchTrendingByTopicAsync(topic, configuration.MaxItems ?? 25);
                        }
                        else
                        {
                            articles = await FetchTrendingRepositoriesInternalAsync(language, configuration.MaxItems ?? 25);
                        }
                        break;
                        
                    case "search":
                        var query = configuration.ApiSettings.GetValueOrDefault("query", "");
                        var searchLanguage = configuration.ApiSettings.GetValueOrDefault("language", "");
                        if (!string.IsNullOrEmpty(query))
                        {
                            articles = await SearchRepositoriesAsync(query, searchLanguage, "stars", configuration.MaxItems ?? 25);
                        }
                        break;
                        
                    case "releases":
                        var owner = configuration.ApiSettings.GetValueOrDefault("owner", "");
                        var repo = configuration.ApiSettings.GetValueOrDefault("repo", "");
                        if (!string.IsNullOrEmpty(owner) && !string.IsNullOrEmpty(repo))
                        {
                            articles = await FetchRepositoryReleasesAsync(owner, repo, configuration.MaxItems ?? 10);
                        }
                        break;
                        
                    default:
                        _logger.LogWarning("Unsupported GitHub source type: {SourceType}", sourceType);
                        break;
                }

                _logger.LogInformation("Fetched {Count} items from GitHub source", articles.Count);
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching from GitHub");
                return articles;
            }
        }

        public async Task<List<Article>> SearchRepositoriesAsync(string query, string language = "", string sort = "stars", int maxResults = 25)
        {
            var articles = new List<Article>();

            try
            {
                _logger.LogDebug("Searching GitHub repositories: {Query}", query);

                var searchQuery = query;
                if (!string.IsNullOrEmpty(language))
                {
                    searchQuery += $" language:{language}";
                }

                var url = $"{GITHUB_API_BASE_URL}/search/repositories" +
                    $"?q={HttpUtility.UrlEncode(searchQuery)}" +
                    $"&sort={sort}" +
                    $"&order=desc" +
                    $"&per_page={Math.Min(maxResults, 100)}";

                var headers = GetApiHeaders();
                var response = await _httpService.GetStringAsync(url, headers);
                
                if (string.IsNullOrEmpty(response))
                {
                    _logger.LogWarning("Empty response from GitHub search API");
                    return articles;
                }

                var searchData = JsonSerializer.Deserialize<GitHubSearchResponse>(response);
                if (searchData?.Items?.Any() != true)
                {
                    _logger.LogWarning("No repositories found for query: {Query}", query);
                    return articles;
                }

                foreach (var repo in searchData.Items)
                {
                    var article = ConvertRepositoryToArticle(repo);
                    if (article != null)
                    {
                        articles.Add(article);
                    }
                }

                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching GitHub repositories: {Query}", query);
                return articles;
            }
        }

        public async Task<GitHubRepositoryInfo?> GetRepositoryInfoAsync(string owner, string repo)
        {
            try
            {
                _logger.LogDebug("Getting GitHub repository info: {Owner}/{Repo}", owner, repo);

                var url = $"{GITHUB_API_BASE_URL}/repos/{owner}/{repo}";
                var headers = GetApiHeaders();
                var response = await _httpService.GetStringAsync(url, headers);
                
                if (string.IsNullOrEmpty(response))
                {
                    return null;
                }

                var repoData = JsonSerializer.Deserialize<GitHubRepository>(response);
                if (repoData == null)
                {
                    return null;
                }

                return new GitHubRepositoryInfo
                {
                    Name = repoData.Name ?? "",
                    FullName = repoData.FullName ?? "",
                    Description = repoData.Description ?? "",
                    HtmlUrl = repoData.HtmlUrl ?? "",
                    Language = repoData.Language ?? "",
                    StargazersCount = repoData.StargazersCount,
                    ForksCount = repoData.ForksCount,
                    OpenIssuesCount = repoData.OpenIssuesCount,
                    CreatedAt = repoData.CreatedAt,
                    UpdatedAt = repoData.UpdatedAt,
                    PushedAt = repoData.PushedAt,
                    OwnerLogin = repoData.Owner?.Login ?? "",
                    OwnerAvatarUrl = repoData.Owner?.AvatarUrl ?? "",
                    Topics = repoData.Topics ?? new List<string>(),
                    License = repoData.License?.Name ?? "",
                    HasIssues = repoData.HasIssues,
                    HasWiki = repoData.HasWiki,
                    HasPages = repoData.HasPages
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting repository info: {Owner}/{Repo}", owner, repo);
                return null;
            }
        }

        public async Task<List<Article>> FetchRepositoryReleasesAsync(string owner, string repo, int maxResults = 10)
        {
            var articles = new List<Article>();

            try
            {
                _logger.LogDebug("Fetching releases for repository: {Owner}/{Repo}", owner, repo);

                var url = $"{GITHUB_API_BASE_URL}/repos/{owner}/{repo}/releases" +
                    $"?per_page={Math.Min(maxResults, 100)}";

                var headers = GetApiHeaders();
                var response = await _httpService.GetStringAsync(url, headers);
                
                if (string.IsNullOrEmpty(response))
                {
                    return articles;
                }

                var releases = JsonSerializer.Deserialize<List<GitHubRelease>>(response);
                if (releases?.Any() != true)
                {
                    return articles;
                }

                foreach (var release in releases)
                {
                    var article = ConvertReleaseToArticle(release, owner, repo);
                    if (article != null)
                    {
                        articles.Add(article);
                    }
                }

                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching repository releases: {Owner}/{Repo}", owner, repo);
                return articles;
            }
        }

        public async Task<List<Article>> FetchTrendingByTopicAsync(string topic, int maxResults = 25)
        {
            try
            {
                _logger.LogDebug("Fetching trending repositories by topic: {Topic}", topic);

                // Use search with topic filter and recent activity
                var query = $"topic:{topic} stars:>10 pushed:>{DateTime.UtcNow.AddDays(-30):yyyy-MM-dd}";
                return await SearchRepositoriesAsync(query, "", "stars", maxResults);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching trending repositories by topic: {Topic}", topic);
                return new List<Article>();
            }
        }

        private async Task<List<Article>> FetchTrendingRepositoriesInternalAsync(string language, int maxResults)
        {
            try
            {
                // GitHub doesn't have a direct trending API, so we'll search for recently popular repos
                var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30).ToString("yyyy-MM-dd");
                var query = $"stars:>10 created:>{thirtyDaysAgo}";
                
                if (!string.IsNullOrEmpty(language))
                {
                    query += $" language:{language}";
                }

                return await SearchRepositoriesAsync(query, "", "stars", maxResults);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching trending repositories");
                return new List<Article>();
            }
        }

        private Article? ConvertRepositoryToArticle(GitHubRepository repo)
        {
            try
            {
                if (repo == null || string.IsNullOrEmpty(repo.FullName))
                {
                    return null;
                }

                var topics = repo.Topics?.Any() == true ? string.Join(", ", repo.Topics) : "";
                var languageInfo = !string.IsNullOrEmpty(repo.Language) ? $"Language: {repo.Language}" : "";
                
                var content = $"{repo.Description}\n\n" +
                             $"⭐ Stars: {repo.StargazersCount:N0}\n" +
                             $"🍴 Forks: {repo.ForksCount:N0}\n" +
                             $"📝 Open Issues: {repo.OpenIssuesCount:N0}\n";
                
                if (!string.IsNullOrEmpty(languageInfo))
                {
                    content += $"💻 {languageInfo}\n";
                }
                
                if (!string.IsNullOrEmpty(topics))
                {
                    content += $"🏷️ Topics: {topics}\n";
                }
                
                content += $"📅 Created: {repo.CreatedAt:yyyy-MM-dd}\n" +
                          $"🔄 Last Updated: {repo.UpdatedAt:yyyy-MM-dd}";

                return new Article
                {
                    Title = $"{repo.FullName} - GitHub Repository",
                    Content = content,
                    Summary = TruncateText(repo.Description ?? "", 200),
                    Url = repo.HtmlUrl ?? "",
                    ImageUrl = repo.Owner?.AvatarUrl,
                    Author = repo.Owner?.Login ?? "",
                    PublishedDate = repo.CreatedAt,
                    CreatedDate = DateTime.UtcNow,
                    ViewCount = repo.StargazersCount // Use star count as a proxy for popularity
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting GitHub repository to article");
                return null;
            }
        }

        private Article? ConvertReleaseToArticle(GitHubRelease release, string owner, string repo)
        {
            try
            {
                if (release == null || string.IsNullOrEmpty(release.TagName))
                {
                    return null;
                }

                var content = $"New release for {owner}/{repo}\n\n";
                content += $"Tag: {release.TagName}\n";
                
                if (!string.IsNullOrEmpty(release.Name) && release.Name != release.TagName)
                {
                    content += $"Name: {release.Name}\n";
                }
                
                content += $"Published: {release.PublishedAt:yyyy-MM-dd HH:mm}\n";
                content += $"Pre-release: {(release.Prerelease ? "Yes" : "No")}\n";
                content += $"Draft: {(release.Draft ? "Yes" : "No")}\n\n";
                
                if (!string.IsNullOrEmpty(release.Body))
                {
                    content += "Release Notes:\n" + release.Body;
                }

                return new Article
                {
                    Title = $"{owner}/{repo} - Release {release.TagName}",
                    Content = content,
                    Summary = TruncateText(release.Body ?? $"New release {release.TagName} for {owner}/{repo}", 200),
                    Url = release.HtmlUrl ?? $"https://github.com/{owner}/{repo}/releases/tag/{release.TagName}",
                    ImageUrl = release.Author?.AvatarUrl,
                    Author = release.Author?.Login ?? owner,
                    PublishedDate = release.PublishedAt ?? DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting GitHub release to article");
                return null;
            }
        }

        private Dictionary<string, string> GetApiHeaders()
        {
            var headers = new Dictionary<string, string>
            {
                ["User-Agent"] = "NewsSite-ContentAggregator/1.0",
                ["Accept"] = "application/vnd.github.v3+json"
            };

            if (!string.IsNullOrEmpty(_apiToken))
            {
                headers["Authorization"] = $"token {_apiToken}";
            }

            return headers;
        }

        private string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            {
                return text;
            }

            return text.Substring(0, maxLength - 3) + "...";
        }
    }

    // GitHub API Response Models
    public class GitHubSearchResponse
    {
        [JsonPropertyName("total_count")]
        public int TotalCount { get; set; }

        [JsonPropertyName("items")]
        public List<GitHubRepository> Items { get; set; } = new();
    }

    public class GitHubRepository
    {
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("full_name")]
        public string? FullName { get; set; }

        [JsonPropertyName("description")]
        public string? Description { get; set; }

        [JsonPropertyName("html_url")]
        public string? HtmlUrl { get; set; }

        [JsonPropertyName("language")]
        public string? Language { get; set; }

        [JsonPropertyName("stargazers_count")]
        public int StargazersCount { get; set; }

        [JsonPropertyName("forks_count")]
        public int ForksCount { get; set; }

        [JsonPropertyName("open_issues_count")]
        public int OpenIssuesCount { get; set; }

        [JsonPropertyName("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonPropertyName("updated_at")]
        public DateTime UpdatedAt { get; set; }

        [JsonPropertyName("pushed_at")]
        public DateTime PushedAt { get; set; }

        [JsonPropertyName("owner")]
        public GitHubUser? Owner { get; set; }

        [JsonPropertyName("topics")]
        public List<string>? Topics { get; set; }

        [JsonPropertyName("license")]
        public GitHubLicense? License { get; set; }

        [JsonPropertyName("has_issues")]
        public bool HasIssues { get; set; }

        [JsonPropertyName("has_wiki")]
        public bool HasWiki { get; set; }

        [JsonPropertyName("has_pages")]
        public bool HasPages { get; set; }
    }

    public class GitHubUser
    {
        [JsonPropertyName("login")]
        public string? Login { get; set; }

        [JsonPropertyName("avatar_url")]
        public string? AvatarUrl { get; set; }
    }

    public class GitHubLicense
    {
        [JsonPropertyName("name")]
        public string? Name { get; set; }
    }

    public class GitHubRelease
    {
        [JsonPropertyName("tag_name")]
        public string? TagName { get; set; }

        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("body")]
        public string? Body { get; set; }

        [JsonPropertyName("html_url")]
        public string? HtmlUrl { get; set; }

        [JsonPropertyName("published_at")]
        public DateTime? PublishedAt { get; set; }

        [JsonPropertyName("prerelease")]
        public bool Prerelease { get; set; }

        [JsonPropertyName("draft")]
        public bool Draft { get; set; }

        [JsonPropertyName("author")]
        public GitHubUser? Author { get; set; }
    }
}