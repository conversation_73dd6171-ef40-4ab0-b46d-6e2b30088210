/* Admin Dashboard Styles */
/* Dark theme optimized for admin interface */

:root {
    --admin-primary: #0d6efd;
    --admin-success: #198754;
    --admin-warning: #ffc107;
    --admin-danger: #dc3545;
    --admin-info: #0dcaf0;
    --admin-dark: #0f1419;
    --admin-light: #e9ecef;
    --admin-sidebar-bg: #1a1d23;
    --admin-sidebar-border: #2d3136;
    --admin-card-bg: #262b36;
    --admin-card-border: #3a4049;
    --admin-text-primary: #e9ecef;
    --admin-text-secondary: #adb5bd;
    --admin-text-muted: #6c757d;
}

/* Global admin styles */
.admin-wrapper {
    background-color: var(--admin-dark);
    color: var(--admin-text-primary);
    min-height: 100vh;
}

/* Sidebar Styles */
.admin-sidebar {
    background: linear-gradient(180deg, var(--admin-sidebar-bg) 0%, #171a1f 100%);
    border-right: 1px solid var(--admin-sidebar-border);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.admin-nav-link {
    color: var(--admin-text-secondary);
    text-decoration: none;
    padding: 0.875rem 1.5rem;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    border-radius: 0;
    position: relative;
    font-weight: 500;
}

.admin-nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: linear-gradient(135deg, var(--admin-primary), #0a58ca);
    transition: width 0.3s ease;
}

.admin-nav-link:hover,
.admin-nav-link.active {
    background: rgba(13, 110, 253, 0.08);
    color: var(--admin-primary);
    border-right: 3px solid var(--admin-primary);
    transform: translateX(2px);
}

.admin-nav-link:hover::before,
.admin-nav-link.active::before {
    width: 4px;
}

.admin-nav-link i {
    width: 20px;
    margin-right: 0.875rem;
    font-size: 1rem;
    opacity: 0.8;
}

.admin-nav-link:hover i,
.admin-nav-link.active i {
    opacity: 1;
}

/* Card Styles */
.admin-card {
    background: var(--admin-card-bg);
    border: 1px solid var(--admin-card-border);
    border-radius: 0.875rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.admin-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
    transform: translateY(-2px);
}

.admin-card .card-header {
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid var(--admin-card-border);
    border-radius: 0.875rem 0.875rem 0 0;
    padding: 1.25rem 1.5rem;
}

.admin-card .card-body {
    padding: 1.5rem;
}

/* Stat Cards */
.admin-stat-card {
    background: linear-gradient(135deg, var(--admin-card-bg) 0%, #1e242d 100%);
    border: 1px solid var(--admin-card-border);
    border-radius: 1rem;
    padding: 1.75rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--admin-primary), var(--admin-info));
}

.admin-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.admin-stat-icon {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    position: relative;
    overflow: hidden;
}

.admin-stat-icon::before {
    content: '';
    position: absolute;
    inset: 0;
    background: inherit;
    opacity: 0.1;
    border-radius: inherit;
}

/* Table Styles */
.table-dark {
    --bs-table-bg: var(--admin-card-bg);
    --bs-table-border-color: var(--admin-card-border);
    --bs-table-striped-bg: rgba(255, 255, 255, 0.02);
    --bs-table-hover-bg: rgba(255, 255, 255, 0.04);
}

.table-dark th {
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 2px solid var(--admin-card-border);
    font-weight: 600;
    color: var(--admin-text-primary);
    padding: 1rem 0.75rem;
}

.table-dark td {
    padding: 0.875rem 0.75rem;
    vertical-align: middle;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

/* Status Indicators */
.status-healthy {
    color: var(--admin-success) !important;
}

.status-warning {
    color: var(--admin-warning) !important;
}

.status-error {
    color: var(--admin-danger) !important;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    box-shadow: 0 0 8px currentColor;
}

.status-indicator.status-healthy {
    background-color: var(--admin-success);
}

.status-indicator.status-warning {
    background-color: var(--admin-warning);
}

.status-indicator.status-error {
    background-color: var(--admin-danger);
}

/* Chart Container */
.chart-container {
    position: relative;
    background: var(--admin-card-bg);
    border: 1px solid var(--admin-card-border);
    border-radius: 0.875rem;
    padding: 1.5rem;
    min-height: 300px;
}

/* Button Styles */
.btn-outline-primary {
    border-color: var(--admin-primary);
    color: var(--admin-primary);
}

.btn-outline-primary:hover {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.btn-outline-secondary {
    border-color: var(--admin-text-secondary);
    color: var(--admin-text-secondary);
}

.btn-outline-secondary:hover {
    background: var(--admin-text-secondary);
    border-color: var(--admin-text-secondary);
    color: var(--admin-dark);
}

/* Badge Styles */
.badge {
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
}

/* Progress Bars */
.progress {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
}

.progress-bar {
    border-radius: 0.5rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background: rgba(25, 135, 84, 0.15);
    color: #75b798;
    border-left: 4px solid var(--admin-success);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.15);
    color: #f5a6a6;
    border-left: 4px solid var(--admin-danger);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.15);
    color: #fdd05a;
    border-left: 4px solid var(--admin-warning);
}

.alert-info {
    background: rgba(13, 202, 240, 0.15);
    color: #9dd9f0;
    border-left: 4px solid var(--admin-info);
}

/* Activity Feed */
.activity-feed {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.activity-feed::-webkit-scrollbar {
    width: 6px;
}

.activity-feed::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.activity-feed::-webkit-scrollbar-thumb {
    background: var(--admin-text-muted);
    border-radius: 3px;
}

.activity-feed::-webkit-scrollbar-thumb:hover {
    background: var(--admin-text-secondary);
}

.activity-item {
    padding: 0.875rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

/* Forms */
.form-control {
    background-color: var(--admin-card-bg);
    border: 1px solid var(--admin-card-border);
    color: var(--admin-text-primary);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    background-color: var(--admin-card-bg);
    border-color: var(--admin-primary);
    color: var(--admin-text-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-select {
    background-color: var(--admin-card-bg);
    border: 1px solid var(--admin-card-border);
    color: var(--admin-text-primary);
}

.form-label {
    color: var(--admin-text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Dropdown Menus */
.dropdown-menu {
    background-color: var(--admin-card-bg);
    border: 1px solid var(--admin-card-border);
    border-radius: 0.75rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
    color: var(--admin-text-secondary);
    padding: 0.75rem 1.25rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--admin-primary);
}

.dropdown-divider {
    border-color: var(--admin-card-border);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 1050;
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-content {
        margin-left: 0 !important;
        padding: 1rem;
    }
    
    .admin-stat-card {
        margin-bottom: 1rem;
    }
    
    .chart-container {
        min-height: 250px;
    }
}

@media (max-width: 576px) {
    .admin-stat-card {
        padding: 1.25rem;
    }
    
    .admin-stat-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
    }
    
    .table-responsive {
        border-radius: 0.75rem;
        overflow: hidden;
    }
}

/* Custom Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.admin-card,
.admin-stat-card {
    animation: fadeInUp 0.6s ease forwards;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-info));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--admin-primary), var(--admin-info)) 1;
}

/* Dark theme overrides for Bootstrap components */
.modal-content {
    background-color: var(--admin-card-bg);
    border: 1px solid var(--admin-card-border);
}

.modal-header {
    border-bottom: 1px solid var(--admin-card-border);
}

.modal-footer {
    border-top: 1px solid var(--admin-card-border);
}

.pagination .page-link {
    background-color: var(--admin-card-bg);
    border-color: var(--admin-card-border);
    color: var(--admin-text-primary);
}

.pagination .page-link:hover {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
}

.pagination .page-item.active .page-link {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}