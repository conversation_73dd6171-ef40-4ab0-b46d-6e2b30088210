-- AI Frontiers Database Deployment Script for Azure SQL
-- This script sets up the production database with proper security and performance settings

USE [aifrontiers-db-prod]
GO

-- Enable Query Store for performance monitoring
IF NOT EXISTS (SELECT * FROM sys.database_query_store_options WHERE actual_state = 2)
BEGIN
    ALTER DATABASE CURRENT SET QUERY_STORE = ON 
    (
        OPERATION_MODE = READ_WRITE,
        CLEANUP_POLICY = (STALE_QUERY_THRESHOLD_DAYS = 30),
        DATA_FLUSH_INTERVAL_SECONDS = 900,
        INTERVAL_LENGTH_MINUTES = 60,
        MAX_STORAGE_SIZE_MB = 1000,
        QUERY_CAPTURE_MODE = AUTO,
        SIZE_BASED_CLEANUP_MODE = AUTO
    )
END
GO

-- Enable Automatic Tuning
ALTER DATABASE CURRENT SET AUTOMATIC_TUNING (FORCE_LAST_GOOD_PLAN = ON)
GO

-- Create database roles for better security
IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'ApplicationRole')
BEGIN
    CREATE ROLE [ApplicationRole]
END
GO

IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'ReadOnlyRole')
BEGIN
    CREATE ROLE [ReadOnlyRole]
END
GO

-- Grant permissions to application role
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::dbo TO [ApplicationRole]
GO

-- Grant only read permissions to readonly role
GRANT SELECT ON SCHEMA::dbo TO [ReadOnlyRole]
GO

-- Create indexes for performance (only if they don't exist)
-- Articles table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Articles') AND name = 'IX_Articles_PublishedDate')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Articles_PublishedDate] ON [Articles] ([PublishedDate] DESC)
    INCLUDE ([Title], [Summary], [Url], [CategoryId], [SourceId])
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Articles') AND name = 'IX_Articles_CategoryId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Articles_CategoryId] ON [Articles] ([CategoryId])
    INCLUDE ([Title], [Summary], [PublishedDate])
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Articles') AND name = 'IX_Articles_SourceId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Articles_SourceId] ON [Articles] ([SourceId])
    INCLUDE ([Title], [PublishedDate])
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Articles') AND name = 'IX_Articles_CreatedDate')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Articles_CreatedDate] ON [Articles] ([CreatedDate] DESC)
END
GO

-- Full-text search index for Articles (if full-text is available)
IF EXISTS (SELECT * FROM sys.fulltext_catalogs WHERE name = 'ArticlesCatalog')
BEGIN
    DROP FULLTEXT INDEX ON [Articles]
    DROP FULLTEXT CATALOG [ArticlesCatalog]
END
GO

IF FULLTEXTSERVICEPROPERTY('IsFullTextInstalled') = 1
BEGIN
    CREATE FULLTEXT CATALOG [ArticlesCatalog] AS DEFAULT
    
    CREATE FULLTEXT INDEX ON [Articles] 
    (
        [Title] LANGUAGE 1033,
        [Summary] LANGUAGE 1033,
        [Content] LANGUAGE 1033
    )
    KEY INDEX [PK_Articles]
    ON [ArticlesCatalog]
    WITH CHANGE_TRACKING AUTO
END
GO

-- Sources table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Sources') AND name = 'IX_Sources_IsActive')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Sources_IsActive] ON [Sources] ([IsActive])
    INCLUDE ([Name], [Url], [SourceType])
END
GO

-- Categories table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Categories') AND name = 'IX_Categories_Slug')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Categories_Slug] ON [Categories] ([Slug])
END
GO

-- Tags table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('Tags') AND name = 'IX_Tags_Name')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Tags_Name] ON [Tags] ([Name])
END
GO

-- ArticleTags junction table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('ArticleTags') AND name = 'IX_ArticleTags_TagId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_ArticleTags_TagId] ON [ArticleTags] ([TagId])
END
GO

-- Create stored procedures for common operations
-- Get recent articles by category
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('GetRecentArticlesByCategory'))
    DROP PROCEDURE [GetRecentArticlesByCategory]
GO

CREATE PROCEDURE [GetRecentArticlesByCategory]
    @CategoryId INT,
    @PageSize INT = 20,
    @PageNumber INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    SELECT 
        a.Id,
        a.Title,
        a.Summary,
        a.Url,
        a.PublishedDate,
        a.ImageUrl,
        s.Name AS SourceName,
        c.Name AS CategoryName,
        c.Slug AS CategorySlug
    FROM Articles a
    INNER JOIN Sources s ON a.SourceId = s.Id
    INNER JOIN Categories c ON a.CategoryId = c.Id
    WHERE a.CategoryId = @CategoryId
        AND a.PublishedDate IS NOT NULL
    ORDER BY a.PublishedDate DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
GO

-- Search articles procedure
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID('SearchArticles'))
    DROP PROCEDURE [SearchArticles]
GO

CREATE PROCEDURE [SearchArticles]
    @SearchTerm NVARCHAR(255),
    @CategoryId INT = NULL,
    @PageSize INT = 20,
    @PageNumber INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    IF FULLTEXTSERVICEPROPERTY('IsFullTextInstalled') = 1
    BEGIN
        -- Use full-text search if available
        SELECT 
            a.Id,
            a.Title,
            a.Summary,
            a.Url,
            a.PublishedDate,
            a.ImageUrl,
            s.Name AS SourceName,
            c.Name AS CategoryName,
            c.Slug AS CategorySlug
        FROM Articles a
        INNER JOIN Sources s ON a.SourceId = s.Id
        INNER JOIN Categories c ON a.CategoryId = c.Id
        WHERE CONTAINS((a.Title, a.Summary, a.Content), @SearchTerm)
            AND (@CategoryId IS NULL OR a.CategoryId = @CategoryId)
        ORDER BY a.PublishedDate DESC
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
    END
    ELSE
    BEGIN
        -- Fallback to LIKE search
        SELECT 
            a.Id,
            a.Title,
            a.Summary,
            a.Url,
            a.PublishedDate,
            a.ImageUrl,
            s.Name AS SourceName,
            c.Name AS CategoryName,
            c.Slug AS CategorySlug
        FROM Articles a
        INNER JOIN Sources s ON a.SourceId = s.Id
        INNER JOIN Categories c ON a.CategoryId = c.Id
        WHERE (a.Title LIKE '%' + @SearchTerm + '%' 
               OR a.Summary LIKE '%' + @SearchTerm + '%'
               OR a.Content LIKE '%' + @SearchTerm + '%')
            AND (@CategoryId IS NULL OR a.CategoryId = @CategoryId)
        ORDER BY a.PublishedDate DESC
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
    END
END
GO

-- Update statistics for better query performance
UPDATE STATISTICS Articles WITH FULLSCAN
GO
UPDATE STATISTICS Sources WITH FULLSCAN
GO
UPDATE STATISTICS Categories WITH FULLSCAN
GO
UPDATE STATISTICS Tags WITH FULLSCAN
GO
UPDATE STATISTICS ArticleTags WITH FULLSCAN
GO

-- Set database configuration for production
ALTER DATABASE CURRENT SET READ_COMMITTED_SNAPSHOT ON
GO

ALTER DATABASE CURRENT SET ALLOW_SNAPSHOT_ISOLATION ON
GO

-- Enable compression for large tables (if supported)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Articles')
BEGIN
    ALTER INDEX ALL ON Articles REBUILD WITH (DATA_COMPRESSION = PAGE)
END
GO

-- Create maintenance plan for index optimization
-- This would typically be done through SQL Server Agent or Azure SQL Database maintenance
PRINT 'Database optimization complete. Consider setting up automated maintenance for:'
PRINT '1. Index defragmentation'
PRINT '2. Statistics updates'
PRINT '3. Database consistency checks'
PRINT '4. Backup verification'

-- Performance monitoring queries for reference
PRINT 'Use these queries to monitor performance:'
PRINT 'SELECT * FROM sys.dm_db_index_usage_stats WHERE database_id = DB_ID()'
PRINT 'SELECT * FROM sys.dm_db_missing_index_details'
PRINT 'SELECT * FROM sys.query_store_runtime_stats_interval'

GO