/* AI Frontiers - Modern Dark Theme */
/* ================================== */

/* CSS Custom Properties for Dark Theme */
:root {
  /* Dark Theme Colors */
  --ai-dark-primary: #0d1117;
  --ai-dark-secondary: #161b22;
  --ai-dark-tertiary: #21262d;
  --ai-dark-surface: #30363d;
  --ai-dark-hover: #373e47;
  
  /* Text Colors */
  --ai-text-primary: #f0f6fc;
  --ai-text-secondary: #8b949e;
  --ai-text-muted: #6e7681;
  --ai-text-inverse: #24292f;
  
  /* Brand Colors */
  --ai-brand-primary: #00d4ff;
  --ai-brand-secondary: #7c3aed;
  --ai-brand-accent: #f59e0b;
  
  /* Category Colors (from database) */
  --ai-category-breaking: #ff4444;
  --ai-category-youtube: #ff0000;
  --ai-category-research: #4CAF50;
  --ai-category-agentic: #2196F3;
  --ai-category-tools: #9C27B0;
  --ai-category-opensource: #FF9800;
  
  /* Gradients */
  --ai-gradient-primary: linear-gradient(135deg, var(--ai-brand-primary) 0%, var(--ai-brand-secondary) 100%);
  --ai-gradient-surface: linear-gradient(145deg, var(--ai-dark-secondary) 0%, var(--ai-dark-tertiary) 100%);
  --ai-gradient-hero: linear-gradient(135deg, #0d1117 0%, #161b22 50%, #21262d 100%);
  
  /* Shadows */
  --ai-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.5);
  --ai-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --ai-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.4);
  --ai-shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.5);
  
  /* Border Radius */
  --ai-radius-sm: 4px;
  --ai-radius-md: 8px;
  --ai-radius-lg: 12px;
  --ai-radius-xl: 16px;
  
  /* Transitions */
  --ai-transition-fast: 0.15s ease-in-out;
  --ai-transition-normal: 0.3s ease-in-out;
  --ai-transition-slow: 0.5s ease-in-out;
}

/* Base Typography */
html {
  font-size: 14px;
  scroll-behavior: smooth;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

/* Global Dark Theme */
body {
  background: var(--ai-dark-primary);
  color: var(--ai-text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  margin: 0;
  min-height: 100vh;
  position: relative;
}

/* Typography Hierarchy */
h1, .h1 {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--ai-text-primary);
  margin-bottom: 1rem;
}

h2, .h2 {
  font-size: 2rem;
  font-weight: 600;
  line-height: 1.3;
  color: var(--ai-text-primary);
  margin-bottom: 0.875rem;
}

h3, .h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  color: var(--ai-text-primary);
  margin-bottom: 0.75rem;
}

h4, .h4 {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.4;
  color: var(--ai-text-secondary);
  margin-bottom: 0.625rem;
}

p {
  color: var(--ai-text-secondary);
  margin-bottom: 1rem;
}

.text-muted {
  color: var(--ai-text-muted) !important;
}

.text-brand {
  background: var(--ai-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Focus States */
.btn:focus, 
.btn:active:focus, 
.btn-link.nav-link:focus, 
.form-control:focus, 
.form-check-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
  border-color: var(--ai-brand-primary);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--ai-dark-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--ai-dark-surface);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--ai-dark-hover);
}

/* Selection */
::selection {
  background: var(--ai-brand-primary);
  color: var(--ai-dark-primary);
}

::-moz-selection {
  background: var(--ai-brand-primary);
  color: var(--ai-dark-primary);
}

/* ================================== */
/* LAYOUT COMPONENTS */
/* ================================== */

/* Navigation Bar */
.navbar-dark {
  background: rgba(22, 27, 34, 0.95) !important;
  border-bottom: 1px solid var(--ai-dark-tertiary);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: var(--ai-shadow-sm);
  transition: var(--ai-transition-normal);
}

.navbar-scrolled {
  background: var(--ai-dark-secondary) !important;
  box-shadow: var(--ai-shadow-md);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--ai-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none !important;
  transition: var(--ai-transition-normal);
}

.navbar-brand:hover {
  transform: scale(1.05);
}

.navbar-nav .nav-link {
  color: var(--ai-text-secondary) !important;
  font-weight: 500;
  padding: 0.75rem 1rem !important;
  border-radius: var(--ai-radius-md);
  transition: var(--ai-transition-fast);
  position: relative;
}

.navbar-nav .nav-link:hover {
  color: var(--ai-text-primary) !important;
  background: var(--ai-dark-hover);
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  color: var(--ai-brand-primary) !important;
  background: rgba(0, 212, 255, 0.1);
}

.navbar-toggler {
  border: none;
  padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
  box-shadow: none;
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28240, 246, 252, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Footer */
.footer {
  background: var(--ai-dark-secondary);
  border-top: 1px solid var(--ai-dark-tertiary);
  color: var(--ai-text-muted);
  padding: 2rem 0;
  margin-top: auto;
}

.footer a {
  color: var(--ai-brand-primary);
  text-decoration: none;
  transition: var(--ai-transition-fast);
}

.footer a:hover {
  color: var(--ai-text-primary);
  text-decoration: underline;
}

.footer .hover-primary {
  transition: var(--ai-transition-fast);
}

.footer .hover-primary:hover {
  transform: translateY(-2px);
  color: var(--ai-brand-primary) !important;
}

/* Container Adjustments */
.container-fluid {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

@media (min-width: 1200px) {
  .container-fluid {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Main Content Area */
main {
  min-height: calc(100vh - 200px);
  padding-bottom: 3rem;
}

/* ================================== */
/* HERO SECTION */
/* ================================== */

.hero-section {
  background: var(--ai-gradient-hero);
  padding: 4rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: var(--ai-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--ai-text-secondary);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 768px) {
  .hero-section {
    padding: 2.5rem 0;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
}

/* ================================== */
/* CARD COMPONENTS */
/* ================================== */

.card {
  background: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-lg);
  transition: var(--ai-transition-normal);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--ai-shadow-lg);
  border-color: var(--ai-dark-hover);
}

.card-header {
  background: var(--ai-dark-tertiary);
  border-bottom: 1px solid var(--ai-dark-surface);
  color: var(--ai-text-primary);
  font-weight: 600;
}

.card-body {
  color: var(--ai-text-secondary);
}

.card-footer {
  background: var(--ai-dark-tertiary);
  border-top: 1px solid var(--ai-dark-surface);
  color: var(--ai-text-muted);
}

/* Article Cards */
.article-card {
  background: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-lg);
  overflow: hidden;
  transition: var(--ai-transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.article-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--ai-shadow-xl);
  border-color: var(--ai-brand-primary);
}

.article-card .card-img-top {
  height: 200px;
  object-fit: cover;
  transition: var(--ai-transition-normal);
}

.article-card:hover .card-img-top {
  transform: scale(1.05);
}

.article-card .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
}

.article-card .card-title {
  color: var(--ai-text-primary);
  font-weight: 600;
  font-size: 1.1rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-card .card-text {
  color: var(--ai-text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid var(--ai-dark-tertiary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.article-date {
  color: var(--ai-text-muted);
  font-size: 0.8rem;
}

.article-category {
  padding: 0.25rem 0.75rem;
  border-radius: var(--ai-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Category-specific colors */
.article-category.breaking { background: rgba(255, 68, 68, 0.2); color: var(--ai-category-breaking); }
.article-category.youtube { background: rgba(255, 0, 0, 0.2); color: var(--ai-category-youtube); }
.article-category.research { background: rgba(76, 175, 80, 0.2); color: var(--ai-category-research); }
.article-category.agentic { background: rgba(33, 150, 243, 0.2); color: var(--ai-category-agentic); }
.article-category.tools { background: rgba(156, 39, 176, 0.2); color: var(--ai-category-tools); }
.article-category.opensource { background: rgba(255, 152, 0, 0.2); color: var(--ai-category-opensource); }

/* ================================== */
/* BUTTONS */
/* ================================== */

.btn {
  border-radius: var(--ai-radius-md);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: var(--ai-transition-fast);
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--ai-gradient-primary);
  color: white;
  box-shadow: var(--ai-shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--ai-shadow-md);
  color: white;
}

.btn-secondary {
  background: var(--ai-dark-surface);
  color: var(--ai-text-primary);
  border: 1px solid var(--ai-dark-hover);
}

.btn-secondary:hover {
  background: var(--ai-dark-hover);
  color: var(--ai-text-primary);
  transform: translateY(-1px);
}

.btn-outline-primary {
  background: transparent;
  color: var(--ai-brand-primary);
  border: 1px solid var(--ai-brand-primary);
}

.btn-outline-primary:hover {
  background: var(--ai-brand-primary);
  color: var(--ai-dark-primary);
  transform: translateY(-1px);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* ================================== */
/* CATEGORY NAVIGATION */
/* ================================== */

.category-nav {
  background: var(--ai-dark-secondary);
  border-radius: var(--ai-radius-lg);
  padding: 1rem;
  margin-bottom: 2rem;
  border: 1px solid var(--ai-dark-tertiary);
}

.category-nav .nav {
  gap: 0.5rem;
  flex-wrap: wrap;
}

.category-nav .nav-link {
  color: var(--ai-text-secondary);
  background: var(--ai-dark-tertiary);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-md);
  padding: 0.75rem 1rem;
  transition: var(--ai-transition-fast);
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-nav .nav-link:hover {
  color: var(--ai-text-primary);
  background: var(--ai-dark-hover);
  transform: translateY(-1px);
}

.category-nav .nav-link.active {
  background: var(--ai-brand-primary);
  color: var(--ai-dark-primary);
  border-color: var(--ai-brand-primary);
}

.category-nav .nav-link i {
  font-size: 1rem;
}

/* ================================== */
/* FORMS */
/* ================================== */

.form-control {
  background: var(--ai-dark-tertiary);
  border: 1px solid var(--ai-dark-surface);
  color: var(--ai-text-primary);
  border-radius: var(--ai-radius-md);
  padding: 0.75rem 1rem;
  transition: var(--ai-transition-fast);
}

.form-control:focus {
  background: var(--ai-dark-surface);
  border-color: var(--ai-brand-primary);
  color: var(--ai-text-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
}

.form-control::placeholder {
  color: var(--ai-text-muted);
}

.form-label {
  color: var(--ai-text-secondary);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-select {
  background: var(--ai-dark-tertiary);
  border: 1px solid var(--ai-dark-surface);
  color: var(--ai-text-primary);
  border-radius: var(--ai-radius-md);
}

.form-select:focus {
  background: var(--ai-dark-surface);
  border-color: var(--ai-brand-primary);
  color: var(--ai-text-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
}

/* ================================== */
/* UTILITIES */
/* ================================== */

.bg-dark-primary { background: var(--ai-dark-primary) !important; }
.bg-dark-secondary { background: var(--ai-dark-secondary) !important; }
.bg-dark-tertiary { background: var(--ai-dark-tertiary) !important; }
.bg-dark-surface { background: var(--ai-dark-surface) !important; }

.text-primary-brand { color: var(--ai-brand-primary) !important; }
.text-secondary-brand { color: var(--ai-brand-secondary) !important; }
.text-accent-brand { color: var(--ai-brand-accent) !important; }

.border-dark { border-color: var(--ai-dark-tertiary) !important; }
.border-brand { border-color: var(--ai-brand-primary) !important; }

.rounded-ai { border-radius: var(--ai-radius-lg) !important; }
.rounded-ai-sm { border-radius: var(--ai-radius-sm) !important; }
.rounded-ai-md { border-radius: var(--ai-radius-md) !important; }
.rounded-ai-xl { border-radius: var(--ai-radius-xl) !important; }

.shadow-ai { box-shadow: var(--ai-shadow-md) !important; }
.shadow-ai-lg { box-shadow: var(--ai-shadow-lg) !important; }

.transition-ai { transition: var(--ai-transition-normal) !important; }
.transition-ai-fast { transition: var(--ai-transition-fast) !important; }

/* Gradient text utility */
.gradient-text {
  background: var(--ai-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glowing effect */
.glow {
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.glow:hover {
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

/* ================================== */
/* RESPONSIVE ADJUSTMENTS */
/* ================================== */

@media (max-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .article-card .card-body {
    padding: 1rem;
  }
  
  .category-nav {
    padding: 0.75rem;
  }
  
  .category-nav .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .btn {
    padding: 0.625rem 1.25rem;
  }
  
  h1, .h1 { font-size: 2rem; }
  h2, .h2 { font-size: 1.75rem; }
  h3, .h3 { font-size: 1.375rem; }
}

@media (max-width: 576px) {
  .hero-section {
    padding: 2rem 0;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .article-card .card-img-top {
    height: 150px;
  }
  
  .category-nav .nav {
    gap: 0.25rem;
  }
  
  .category-nav .nav-link {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
}

/* ================================== */
/* LOADING STATES */
/* ================================== */

.loading {
  opacity: 0.7;
  pointer-events: none;
}

.spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--ai-dark-surface);
  border-radius: 50%;
  border-top-color: var(--ai-brand-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ================================== */
/* ACCESSIBILITY IMPROVEMENTS */
/* ================================== */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators */
a:focus,
button:focus,
.btn:focus {
  outline: 2px solid var(--ai-brand-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --ai-text-secondary: #ffffff;
    --ai-text-muted: #cccccc;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ================================== */
/* BOOTSTRAP OVERRIDES */
/* ================================== */

/* Override Bootstrap's default styles for dark theme */
.navbar-light .navbar-brand {
  color: var(--ai-text-primary) !important;
}

.navbar-light .navbar-nav .nav-link {
  color: var(--ai-text-secondary) !important;
}

.navbar-light .navbar-nav .nav-link:hover {
  color: var(--ai-text-primary) !important;
}

.navbar-light .navbar-toggler {
  border-color: var(--ai-dark-surface);
}

.text-dark {
  color: var(--ai-text-primary) !important;
}

.bg-white {
  background-color: var(--ai-dark-secondary) !important;
}

.border-bottom {
  border-bottom-color: var(--ai-dark-tertiary) !important;
}

.border-top {
  border-top-color: var(--ai-dark-tertiary) !important;
}

.box-shadow {
  box-shadow: var(--ai-shadow-sm) !important;
}

/* Alert overrides */
.alert {
  border-radius: var(--ai-radius-md);
  border: 1px solid;
}

.alert-primary {
  background-color: rgba(0, 212, 255, 0.1);
  border-color: var(--ai-brand-primary);
  color: var(--ai-brand-primary);
}

.alert-secondary {
  background-color: var(--ai-dark-tertiary);
  border-color: var(--ai-dark-surface);
  color: var(--ai-text-secondary);
}

/* ================================== */
/* SEARCH FUNCTIONALITY STYLES */
/* ================================== */

/* Global Search Bar in Navbar */
.navbar-search {
  min-width: 300px;
  max-width: 400px;
}

.search-form-navbar {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-navbar {
  background-color: var(--ai-dark-tertiary);
  border: 1px solid var(--ai-dark-surface);
  color: var(--ai-text-primary);
  border-radius: var(--ai-radius-lg);
  padding: 8px 40px 8px 12px;
  font-size: 14px;
  transition: var(--ai-transition-normal);
}

.search-input-navbar:focus {
  background-color: var(--ai-dark-secondary);
  border-color: var(--ai-brand-primary);
  color: var(--ai-text-primary);
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.search-input-navbar::placeholder {
  color: var(--ai-text-muted);
}

.btn-search-navbar {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--ai-text-muted);
  padding: 4px 8px;
  border-radius: var(--ai-radius-sm);
  transition: var(--ai-transition-fast);
}

.btn-search-navbar:hover {
  color: var(--ai-brand-primary);
  background-color: var(--ai-dark-surface);
}

.search-suggestions-navbar {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-md);
  box-shadow: var(--ai-shadow-lg);
  z-index: 1050;
  margin-top: 4px;
  max-height: 400px;
  overflow-y: auto;
}

/* Search Page Hero */
.search-page {
  min-height: 100vh;
  background: var(--ai-gradient-hero);
}

.search-hero {
  padding: 80px 0 60px;
  text-align: center;
}

.search-title {
  font-size: 3rem;
  font-weight: 800;
  background: var(--ai-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.search-subtitle {
  font-size: 1.25rem;
  color: var(--ai-text-secondary);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Main Search Form */
.search-form {
  margin-bottom: 2rem;
}

.search-input-group {
  display: flex;
  gap: 12px;
  margin-bottom: 1rem;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--ai-text-muted);
  z-index: 10;
}

.search-input {
  width: 100%;
  padding: 16px 20px 16px 48px;
  font-size: 18px;
  background-color: var(--ai-dark-secondary);
  border: 2px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-xl);
  color: var(--ai-text-primary);
  transition: var(--ai-transition-normal);
}

.search-input:focus {
  background-color: var(--ai-dark-tertiary);
  border-color: var(--ai-brand-primary);
  box-shadow: 0 0 0 4px rgba(0, 212, 255, 0.1);
  outline: none;
}

.search-input::placeholder {
  color: var(--ai-text-muted);
  font-size: 16px;
}

.search-btn {
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  background: var(--ai-gradient-primary);
  border: none;
  border-radius: var(--ai-radius-xl);
  color: white;
  transition: var(--ai-transition-normal);
  min-width: 120px;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--ai-shadow-lg);
  color: white;
}

/* Quick Filters */
.quick-filters {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-label {
  color: var(--ai-text-secondary);
  font-size: 14px;
  margin-right: 8px;
}

.filter-chip {
  padding: 6px 16px;
  background-color: var(--ai-dark-tertiary);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-lg);
  color: var(--ai-text-primary);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: var(--ai-transition-fast);
}

.filter-chip:hover,
.filter-chip.active {
  background-color: var(--ai-brand-primary);
  border-color: var(--ai-brand-primary);
  color: white;
  text-decoration: none;
}

.advanced-link {
  background-color: var(--ai-dark-surface);
  border-color: var(--ai-brand-secondary);
  color: var(--ai-brand-secondary);
}

.advanced-link:hover {
  background-color: var(--ai-brand-secondary);
  color: white;
}

/* Search Suggestions */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-lg);
  box-shadow: var(--ai-shadow-xl);
  z-index: 1000;
  margin-top: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.search-suggestion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--ai-dark-surface);
  cursor: pointer;
  transition: var(--ai-transition-fast);
}

.search-suggestion-item:last-child {
  border-bottom: none;
}

.search-suggestion-item:hover,
.search-suggestion-item.active {
  background-color: var(--ai-dark-tertiary);
}

.suggestion-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.suggestion-content i {
  color: var(--ai-text-muted);
  width: 16px;
  text-align: center;
}

.suggestion-text {
  color: var(--ai-text-primary);
  font-weight: 500;
}

.suggestion-text mark {
  background-color: var(--ai-brand-primary);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
}

.suggestion-count {
  font-size: 12px;
  color: var(--ai-text-muted);
  background-color: var(--ai-dark-surface);
  padding: 2px 8px;
  border-radius: var(--ai-radius-sm);
}

.suggestion-type {
  font-size: 11px;
  color: var(--ai-text-muted);
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.search-suggestion-loading,
.search-suggestion-empty {
  padding: 16px;
  text-align: center;
  color: var(--ai-text-muted);
}

.search-suggestion-loading i {
  margin-right: 8px;
}

/* Search Suggestions Section */
.search-suggestions-section {
  padding: 60px 0;
  background-color: var(--ai-dark-secondary);
}

.suggestion-card {
  background: var(--ai-gradient-surface);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-lg);
  padding: 24px;
  height: 100%;
  transition: var(--ai-transition-normal);
}

.suggestion-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--ai-shadow-lg);
  border-color: var(--ai-dark-hover);
}

.suggestion-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--ai-text-primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-md);
  color: var(--ai-text-primary);
  text-decoration: none;
  font-size: 14px;
  transition: var(--ai-transition-fast);
}

.suggestion-item:hover {
  background-color: var(--ai-dark-surface);
  color: var(--ai-text-primary);
  text-decoration: none;
}

.suggestion-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: var(--ai-radius-sm);
  font-weight: 600;
  text-transform: uppercase;
}

.suggestion-badge.trending {
  background-color: var(--ai-category-breaking);
  color: white;
}

/* Category Grid */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  background-color: var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-md);
  color: var(--ai-text-primary);
  text-decoration: none;
  text-align: center;
  transition: var(--ai-transition-fast);
}

.category-item:hover {
  background-color: var(--ai-dark-surface);
  color: var(--ai-text-primary);
  text-decoration: none;
  transform: translateY(-2px);
}

.category-item i {
  font-size: 24px;
  margin-bottom: 8px;
}

.category-item span {
  font-weight: 600;
  margin-bottom: 4px;
}

.category-item small {
  color: var(--ai-text-muted);
  font-size: 12px;
}

/* Recent Searches */
.recent-searches-section {
  padding: 40px 0;
  background-color: var(--ai-dark-tertiary);
}

.recent-searches {
  text-align: center;
}

.recent-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--ai-text-primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.recent-items {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-md);
  color: var(--ai-text-secondary);
  text-decoration: none;
  font-size: 14px;
  transition: var(--ai-transition-fast);
}

.recent-item:hover {
  background-color: var(--ai-dark-surface);
  color: var(--ai-text-primary);
  text-decoration: none;
}

.clear-recent {
  color: var(--ai-text-muted);
  font-size: 14px;
  padding: 6px 12px;
}

.clear-recent:hover {
  color: var(--ai-category-breaking);
}

/* Search Tips */
.search-tips-section {
  padding: 60px 0;
  background-color: var(--ai-dark-primary);
}

.search-tips {
  text-align: center;
}

.tips-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--ai-text-primary);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 20px;
  background-color: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-lg);
  text-align: left;
  transition: var(--ai-transition-normal);
}

.tip-item:hover {
  border-color: var(--ai-brand-primary);
  box-shadow: var(--ai-shadow-md);
}

.tip-item i {
  color: var(--ai-brand-primary);
  font-size: 18px;
  margin-top: 2px;
  flex-shrink: 0;
}

.tip-item strong {
  color: var(--ai-text-primary);
}

/* Search Results Page */
.search-results-page {
  min-height: 100vh;
  background-color: var(--ai-dark-primary);
}

.search-header {
  background-color: var(--ai-dark-secondary);
  padding: 20px 0;
  border-bottom: 1px solid var(--ai-dark-surface);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-form-compact .search-input-group {
  max-width: 600px;
  margin: 0 auto;
}

.search-form-compact .search-input {
  padding: 12px 16px 12px 40px;
  font-size: 16px;
}

.search-form-compact .search-btn {
  padding: 12px 20px;
  font-size: 14px;
  min-width: 80px;
}

.search-context {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.search-stats {
  color: var(--ai-text-secondary);
  font-size: 14px;
}

.results-count {
  color: var(--ai-text-primary);
}

.search-context-text {
  color: var(--ai-brand-primary);
  margin-left: 8px;
}

.search-time {
  color: var(--ai-text-muted);
  margin-left: 8px;
}

/* Active Filters */
.active-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  background-color: var(--ai-brand-primary);
  color: white;
  border-radius: var(--ai-radius-lg);
  font-size: 12px;
  font-weight: 500;
}

.filter-remove {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  font-weight: bold;
  margin-left: 4px;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--ai-transition-fast);
}

.filter-remove:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Search Sidebar */
.search-sidebar {
  background-color: var(--ai-dark-secondary);
  border-radius: var(--ai-radius-lg);
  padding: 24px;
  height: fit-content;
  position: sticky;
  top: 120px;
}

.filter-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--ai-dark-surface);
}

.filter-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.filter-title {
  font-size: 1rem;
  font-weight: 700;
  color: var(--ai-text-primary);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Sort Options */
.sort-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sort-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--ai-radius-md);
  transition: var(--ai-transition-fast);
}

.sort-option:hover {
  background-color: var(--ai-dark-tertiary);
}

.sort-option input[type="radio"] {
  margin-right: 8px;
  accent-color: var(--ai-brand-primary);
}

.sort-label {
  color: var(--ai-text-primary);
  font-size: 14px;
}

/* Filter Lists */
.filter-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  border-radius: var(--ai-radius-sm);
  cursor: pointer;
  transition: var(--ai-transition-fast);
  text-decoration: none;
  color: var(--ai-text-primary);
}

.filter-item:hover {
  background-color: var(--ai-dark-tertiary);
  color: var(--ai-text-primary);
  text-decoration: none;
}

.filter-item input[type="checkbox"] {
  margin-right: 8px;
  accent-color: var(--ai-brand-primary);
}

.filter-label {
  flex: 1;
  font-size: 14px;
}

.filter-count {
  font-size: 12px;
  color: var(--ai-text-muted);
  background-color: var(--ai-dark-surface);
  padding: 2px 6px;
  border-radius: var(--ai-radius-sm);
}

.show-more-filters {
  font-size: 12px;
  color: var(--ai-brand-primary);
  padding: 4px 0;
  margin-top: 4px;
}

.show-more-filters:hover {
  color: var(--ai-brand-secondary);
}

/* Tag Cloud */
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  padding: 4px 10px;
  background-color: var(--ai-dark-tertiary);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-md);
  color: var(--ai-text-primary);
  text-decoration: none;
  font-size: 12px;
  font-weight: 500;
  transition: var(--ai-transition-fast);
}

.tag-item:hover {
  background-color: var(--ai-brand-primary);
  border-color: var(--ai-brand-primary);
  color: white;
  text-decoration: none;
}

.tag-item[data-count]:after {
  content: attr(data-count);
  margin-left: 4px;
  font-size: 10px;
  opacity: 0.7;
}

/* Author Items */
.author-item {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
}

/* Results Header */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid var(--ai-dark-surface);
}

.view-toggle {
  display: flex;
  gap: 4px;
}

.view-btn {
  padding: 8px 12px;
  font-size: 14px;
  background-color: var(--ai-dark-tertiary);
  border: 1px solid var(--ai-dark-surface);
  color: var(--ai-text-secondary);
  border-radius: var(--ai-radius-md);
  transition: var(--ai-transition-fast);
}

.view-btn:hover,
.view-btn.active {
  background-color: var(--ai-brand-primary);
  border-color: var(--ai-brand-primary);
  color: white;
}

/* Search Result Cards */
.results-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-result-card {
  background: var(--ai-gradient-surface);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-lg);
  padding: 24px;
  transition: var(--ai-transition-normal);
}

.search-result-card:hover {
  border-color: var(--ai-dark-hover);
  box-shadow: var(--ai-shadow-md);
}

.result-header {
  margin-bottom: 16px;
}

.result-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.3;
}

.result-title a {
  color: var(--ai-text-primary);
  text-decoration: none;
  transition: var(--ai-transition-fast);
}

.result-title a:hover {
  color: var(--ai-brand-primary);
}

.result-title mark {
  background-color: var(--ai-brand-primary);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
}

.result-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  font-size: 14px;
  color: var(--ai-text-muted);
}

.result-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.result-meta a {
  color: var(--ai-text-muted);
  text-decoration: none;
}

.result-meta a:hover {
  color: var(--ai-brand-primary);
}

.result-body {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.result-image {
  flex-shrink: 0;
  width: 120px;
  height: 80px;
  border-radius: var(--ai-radius-md);
  overflow: hidden;
}

.result-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.result-text {
  flex: 1;
}

.result-summary {
  color: var(--ai-text-secondary);
  line-height: 1.5;
  margin-bottom: 12px;
}

.result-summary mark {
  background-color: var(--ai-brand-primary);
  color: white;
  padding: 1px 3px;
  border-radius: 2px;
}

.result-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag-badge {
  padding: 2px 8px;
  background-color: var(--ai-dark-tertiary);
  border: 1px solid var(--ai-dark-surface);
  border-radius: var(--ai-radius-sm);
  color: var(--ai-text-muted);
  text-decoration: none;
  font-size: 11px;
  font-weight: 500;
  transition: var(--ai-transition-fast);
}

.tag-badge:hover {
  background-color: var(--ai-brand-primary);
  border-color: var(--ai-brand-primary);
  color: white;
  text-decoration: none;
}

.result-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid var(--ai-dark-surface);
}

.result-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--ai-text-muted);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.relevance-score {
  color: var(--ai-brand-primary);
  font-weight: 600;
}

.result-badges {
  display: flex;
  gap: 6px;
}

.badge {
  padding: 3px 8px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  border-radius: var(--ai-radius-sm);
  letter-spacing: 0.5px;
}

.badge-featured {
  background-color: var(--ai-brand-accent);
  color: white;
}

.badge-breaking {
  background-color: var(--ai-category-breaking);
  color: white;
}

.badge-trending {
  background-color: var(--ai-category-research);
  color: white;
}

/* Grid View */
.results-container[data-view="grid"] {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.results-container[data-view="grid"] .search-result-card {
  padding: 20px;
}

.results-container[data-view="grid"] .result-title {
  font-size: 1.25rem;
}

.results-container[data-view="grid"] .result-body {
  flex-direction: column;
  gap: 12px;
}

.results-container[data-view="grid"] .result-image {
  width: 100%;
  height: 120px;
}

/* Compact View */
.results-container[data-view="compact"] .search-result-card {
  padding: 16px;
}

.results-container[data-view="compact"] .result-title {
  font-size: 1.1rem;
  margin-bottom: 4px;
}

.results-container[data-view="compact"] .result-body {
  gap: 12px;
}

.results-container[data-view="compact"] .result-image {
  width: 80px;
  height: 60px;
}

.results-container[data-view="compact"] .result-summary {
  font-size: 14px;
  margin-bottom: 8px;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 60px 20px;
  color: var(--ai-text-secondary);
}

.no-results-icon {
  font-size: 4rem;
  color: var(--ai-text-muted);
  margin-bottom: 24px;
}

.no-results h3 {
  color: var(--ai-text-primary);
  margin-bottom: 16px;
}

.no-results-suggestions {
  margin: 24px 0;
  text-align: left;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.no-results-suggestions h5 {
  color: var(--ai-text-primary);
  margin-bottom: 12px;
}

.no-results-suggestions ul {
  color: var(--ai-text-secondary);
}

.no-results-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Pagination */
.pagination-wrapper {
  text-align: center;
}

.pagination {
  justify-content: center;
  margin-bottom: 16px;
}

.page-link {
  background-color: var(--ai-dark-tertiary);
  border-color: var(--ai-dark-surface);
  color: var(--ai-text-primary);
}

.page-link:hover {
  background-color: var(--ai-dark-surface);
  border-color: var(--ai-dark-hover);
  color: var(--ai-text-primary);
}

.page-item.active .page-link {
  background-color: var(--ai-brand-primary);
  border-color: var(--ai-brand-primary);
  color: white;
}

.pagination-info {
  font-size: 14px;
  color: var(--ai-text-muted);
}

/* Advanced Search Page */
.advanced-search-page {
  min-height: 100vh;
  background: var(--ai-gradient-hero);
  padding: 40px 0;
}

.advanced-search-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header {
  max-width: 600px;
  margin: 0 auto;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: var(--ai-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.page-subtitle {
  font-size: 1.1rem;
  color: var(--ai-text-secondary);
}

.advanced-search-content {
  max-width: 1200px;
  margin: 0 auto;
}

.search-form-sections {
  background-color: var(--ai-dark-secondary);
  border-radius: var(--ai-radius-xl);
  border: 1px solid var(--ai-dark-surface);
  overflow: hidden;
}

.search-section {
  padding: 32px;
  border-bottom: 1px solid var(--ai-dark-surface);
}

.search-section:last-child {
  border-bottom: none;
}

.section-header {
  margin-bottom: 24px;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--ai-text-primary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-description {
  color: var(--ai-text-secondary);
  font-size: 14px;
}

/* Form Groups */
.filter-group {
  margin-bottom: 20px;
}

.form-label {
  font-weight: 600;
  color: var(--ai-text-primary);
  margin-bottom: 8px;
  display: block;
}

.form-control {
  background-color: var(--ai-dark-tertiary);
  border: 1px solid var(--ai-dark-surface);
  color: var(--ai-text-primary);
  border-radius: var(--ai-radius-md);
  padding: 10px 14px;
  transition: var(--ai-transition-normal);
}

.form-control:focus {
  background-color: var(--ai-dark-secondary);
  border-color: var(--ai-brand-primary);
  color: var(--ai-text-primary);
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.form-control::placeholder {
  color: var(--ai-text-muted);
}

.form-text {
  color: var(--ai-text-muted);
  font-size: 12px;
  margin-top: 4px;
}

/* Custom Checkboxes */
.custom-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--ai-radius-md);
  transition: var(--ai-transition-fast);
  margin-bottom: 4px;
}

.custom-checkbox:hover {
  background-color: var(--ai-dark-tertiary);
}

.custom-checkbox input[type="checkbox"] {
  margin-right: 10px;
  accent-color: var(--ai-brand-primary);
  cursor: pointer;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ai-text-primary);
  font-size: 14px;
  cursor: pointer;
  flex: 1;
}

.item-count {
  font-size: 12px;
  color: var(--ai-text-muted);
  background-color: var(--ai-dark-surface);
  padding: 2px 6px;
  border-radius: var(--ai-radius-sm);
  margin-left: auto;
}

/* Scrollable Areas */
.max-height-scroll {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 8px;
}

.max-height-scroll::-webkit-scrollbar {
  width: 4px;
}

.max-height-scroll::-webkit-scrollbar-track {
  background: var(--ai-dark-surface);
  border-radius: 2px;
}

.max-height-scroll::-webkit-scrollbar-thumb {
  background: var(--ai-text-muted);
  border-radius: 2px;
}

.max-height-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--ai-brand-primary);
}

/* Tag Cloud Select */
.tag-cloud-select {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background-color: var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-md);
}

.tag-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.tag-checkbox input[type="checkbox"] {
  display: none;
}

.tag-label {
  padding: 4px 10px;
  background-color: var(--ai-dark-surface);
  border: 1px solid var(--ai-dark-hover);
  border-radius: var(--ai-radius-md);
  color: var(--ai-text-primary);
  font-size: 12px;
  font-weight: 500;
  transition: var(--ai-transition-fast);
  cursor: pointer;
}

.tag-checkbox input[type="checkbox"]:checked + .tag-label {
  background-color: var(--ai-brand-primary);
  border-color: var(--ai-brand-primary);
  color: white;
}

.tag-label:hover {
  background-color: var(--ai-brand-primary);
  color: white;
}

/* Date Range Selector */
.date-range-selector select {
  margin-bottom: 8px;
}

.custom-date-range {
  padding: 12px;
  background-color: var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-md);
  border: 1px solid var(--ai-dark-surface);
}

/* Query Examples */
.query-examples {
  background-color: var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-md);
  padding: 16px;
  border: 1px solid var(--ai-dark-surface);
}

.query-examples h6 {
  color: var(--ai-text-primary);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
}

.example-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.example-btn {
  text-align: left;
  font-size: 13px;
  color: var(--ai-brand-primary);
  padding: 4px 8px;
  border-radius: var(--ai-radius-sm);
  font-family: 'Courier New', monospace;
  background-color: var(--ai-dark-surface);
  border: 1px solid var(--ai-dark-hover);
}

.example-btn:hover {
  background-color: var(--ai-brand-primary);
  color: white;
}

/* Form Actions */
.search-actions {
  background-color: var(--ai-dark-primary);
  padding: 32px;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.action-buttons .btn {
  min-width: 160px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar-search {
    display: none !important;
  }

  .search-title {
    font-size: 2rem;
  }

  .search-input-group {
    flex-direction: column;
    gap: 12px;
  }

  .search-btn {
    width: 100%;
  }

  .quick-filters {
    justify-content: flex-start;
  }

  .search-context {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .results-container[data-view="grid"] {
    grid-template-columns: 1fr;
  }

  .result-body {
    flex-direction: column;
    gap: 12px;
  }

  .result-image {
    width: 100% !important;
    height: 150px !important;
  }

  .result-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .search-section {
    padding: 24px 20px;
  }

  .page-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .btn {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .search-hero {
    padding: 40px 0;
  }

  .search-title {
    font-size: 1.75rem;
  }

  .search-subtitle {
    font-size: 1rem;
  }

  .search-input {
    font-size: 16px;
    padding: 14px 16px 14px 40px;
  }

  .suggestion-card {
    padding: 16px;
  }

  .tips-grid {
    grid-template-columns: 1fr;
  }

  .search-sidebar {
    position: static;
    margin-bottom: 20px;
  }

  .search-result-card {
    padding: 16px;
  }

  .result-title {
    font-size: 1.25rem;
  }

  .result-meta {
    flex-direction: column;
    gap: 8px;
  }
}

.alert-success {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: var(--ai-category-research);
  color: var(--ai-category-research);
}

.alert-danger {
  background-color: rgba(255, 68, 68, 0.1);
  border-color: var(--ai-category-breaking);
  color: var(--ai-category-breaking);
}

.alert-warning {
  background-color: rgba(255, 152, 0, 0.1);
  border-color: var(--ai-category-opensource);
  color: var(--ai-category-opensource);
}

.alert-info {
  background-color: rgba(33, 150, 243, 0.1);
  border-color: var(--ai-category-agentic);
  color: var(--ai-category-agentic);
}

/* Modal overrides */
.modal-content {
  background-color: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-lg);
}

.modal-header {
  border-bottom-color: var(--ai-dark-tertiary);
  color: var(--ai-text-primary);
}

.modal-body {
  color: var(--ai-text-secondary);
}

.modal-footer {
  border-top-color: var(--ai-dark-tertiary);
}

.btn-close {
  filter: invert(1);
}

/* Dropdown overrides */
.dropdown-menu {
  background-color: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-md);
  box-shadow: var(--ai-shadow-lg);
}

.dropdown-item {
  color: var(--ai-text-secondary);
  transition: var(--ai-transition-fast);
}

.dropdown-item:hover {
  background-color: var(--ai-dark-hover);
  color: var(--ai-text-primary);
}

.dropdown-item:active {
  background-color: var(--ai-brand-primary);
  color: var(--ai-dark-primary);
}

.dropdown-divider {
  border-top-color: var(--ai-dark-tertiary);
}

/* Badge overrides */
.badge {
  border-radius: var(--ai-radius-sm);
}

.badge.bg-primary {
  background-color: var(--ai-brand-primary) !important;
  color: var(--ai-dark-primary) !important;
}

.badge.bg-secondary {
  background-color: var(--ai-dark-surface) !important;
  color: var(--ai-text-primary) !important;
}

/* Pagination overrides */
.pagination .page-link {
  background-color: var(--ai-dark-secondary);
  border-color: var(--ai-dark-tertiary);
  color: var(--ai-text-secondary);
}

.pagination .page-link:hover {
  background-color: var(--ai-dark-hover);
  border-color: var(--ai-dark-surface);
  color: var(--ai-text-primary);
}

.pagination .page-item.active .page-link {
  background-color: var(--ai-brand-primary);
  border-color: var(--ai-brand-primary);
  color: var(--ai-dark-primary);
}

/* Table overrides */
.table {
  color: var(--ai-text-secondary);
}

.table-dark {
  --bs-table-bg: var(--ai-dark-secondary);
  --bs-table-striped-bg: var(--ai-dark-tertiary);
  --bs-table-hover-bg: var(--ai-dark-hover);
  --bs-table-border-color: var(--ai-dark-surface);
  color: var(--ai-text-primary);
}

.table thead th {
  border-bottom-color: var(--ai-dark-surface);
  color: var(--ai-text-primary);
  font-weight: 600;
}

.table td, .table th {
  border-top-color: var(--ai-dark-tertiary);
}

/* Progress bar overrides */
.progress {
  background-color: var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-sm);
}

.progress-bar {
  background: var(--ai-gradient-primary);
}

/* List group overrides */
.list-group-item {
  background-color: var(--ai-dark-secondary);
  border-color: var(--ai-dark-tertiary);
  color: var(--ai-text-secondary);
}

.list-group-item:hover {
  background-color: var(--ai-dark-hover);
}

.list-group-item.active {
  background-color: var(--ai-brand-primary);
  border-color: var(--ai-brand-primary);
  color: var(--ai-dark-primary);
}

/* ================================== */
/* CATEGORY-SPECIFIC STYLES */
/* ================================== */

/* Category Hero Section */
.category-hero-section {
  background: linear-gradient(135deg, var(--ai-dark-primary) 0%, var(--ai-dark-secondary) 100%);
  padding: 3rem 0 2rem;
  border-bottom: 1px solid var(--ai-dark-tertiary);
  position: relative;
  overflow: hidden;
}

.category-hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 50%);
  opacity: 0.5;
}

.category-hero-content {
  position: relative;
  z-index: 1;
}

.category-icon-large {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--ai-radius-lg);
  backdrop-filter: blur(10px);
}

.category-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
}

.category-description {
  font-size: 1.1rem;
  line-height: 1.5;
}

.category-stats {
  margin-top: 1.5rem;
}

.stat-item {
  text-align: center;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--ai-radius-md);
  backdrop-filter: blur(5px);
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--ai-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-actions {
  margin-top: 1rem;
}

/* Category Controls Section */
.category-controls-section {
  padding: 1.5rem 0;
  background: var(--ai-dark-secondary);
  border-bottom: 1px solid var(--ai-dark-tertiary);
}

.category-controls-card {
  background: var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--ai-dark-surface);
}

.search-input-group .input-group {
  border-radius: var(--ai-radius-md);
  overflow: hidden;
}

.search-input-group .input-group-text {
  background: var(--ai-dark-surface);
  border-color: var(--ai-dark-hover);
  color: var(--ai-text-muted);
}

.advanced-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Articles Section */
.category-articles-section {
  padding: 2rem 0;
}

.articles-grid {
  display: grid;
  gap: 1.5rem;
}

.articles-grid[data-view-mode="grid"] {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.articles-grid[data-view-mode="list"] {
  grid-template-columns: 1fr;
}

.articles-grid[data-view-mode="compact"] {
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

/* Article Card Variants */
.article-card-list {
  display: flex;
  background: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-lg);
  overflow: hidden;
  transition: var(--ai-transition-normal);
}

.article-card-list:hover {
  transform: translateY(-2px);
  box-shadow: var(--ai-shadow-lg);
}

.article-card-list .card-img {
  width: 200px;
  min-height: 150px;
  object-fit: cover;
  transition: var(--ai-transition-normal);
}

.article-card-compact {
  background: var(--ai-dark-secondary);
  border: 1px solid var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-md);
  padding: 1rem;
  transition: var(--ai-transition-fast);
}

.article-card-compact:hover {
  background: var(--ai-dark-hover);
  transform: translateX(4px);
}

.article-image-thumbnail img {
  object-fit: cover;
  border-radius: var(--ai-radius-sm);
}

/* Category Features Section */
.category-features-section {
  padding: 1.5rem 0;
  background: var(--ai-dark-secondary);
  border-top: 1px solid var(--ai-dark-tertiary);
}

.features-card {
  background: var(--ai-dark-tertiary);
  border-radius: var(--ai-radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--ai-dark-surface);
}

.platform-categories .badge,
.feature-stats .badge,
.tools-filters .btn,
.github-stats {
  margin: 0.25rem;
}

/* Breaking News Alerts */
.breaking-news-alert {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1050;
  max-width: 400px;
  box-shadow: var(--ai-shadow-xl);
  border-left: 4px solid var(--ai-category-breaking);
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Empty State */
.empty-state {
  padding: 4rem 2rem;
}

.empty-state-icon {
  font-size: 4rem;
  opacity: 0.5;
}

.empty-state-title {
  color: var(--ai-text-primary);
  margin-bottom: 1rem;
}

.empty-state-description {
  font-size: 1.1rem;
  max-width: 500px;
  margin: 0 auto 2rem;
}

/* Badge Enhancements */
.badge.featured-badge,
.badge.breaking-badge,
.badge.trending-badge {
  font-size: 0.65rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--ai-radius-sm);
  backdrop-filter: blur(5px);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Loading States */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--ai-text-muted);
}

.loading-indicator .spinner-border {
  width: 3rem;
  height: 3rem;
  border-width: 0.3rem;
}

/* View Mode Specific Styles */
.view-mode-toggle .btn {
  border-color: var(--ai-dark-surface);
  color: var(--ai-text-muted);
}

.view-mode-toggle .btn.active {
  background-color: var(--ai-brand-primary);
  border-color: var(--ai-brand-primary);
  color: var(--ai-dark-primary);
}

.view-mode-toggle .btn:hover:not(.active) {
  background-color: var(--ai-dark-hover);
  border-color: var(--ai-dark-hover);
  color: var(--ai-text-primary);
}

/* Results Header */
.results-header {
  border-bottom: 1px solid var(--ai-dark-tertiary);
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.results-info {
  color: var(--ai-text-muted);
  font-size: 0.9rem;
}

/* Breadcrumb Styling */
.breadcrumb {
  background: transparent;
  padding: 0;
  margin: 0;
}

.breadcrumb-item {
  color: var(--ai-text-muted);
}

.breadcrumb-item.active {
  color: var(--ai-text-primary);
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: var(--ai-text-muted);
  margin: 0 0.5rem;
}

/* ================================== */
/* RESPONSIVE CATEGORY STYLES */
/* ================================== */

@media (max-width: 768px) {
  .category-hero-section {
    padding: 2rem 0 1.5rem;
  }

  .category-title {
    font-size: 2rem;
  }

  .category-icon-large {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .category-controls-card {
    padding: 1rem;
  }

  .view-controls {
    justify-content: center;
    margin-top: 1rem;
  }

  .articles-grid[data-view-mode="grid"] {
    grid-template-columns: 1fr;
  }

  .article-card-list {
    flex-direction: column;
  }

  .article-card-list .card-img {
    width: 100%;
    height: 150px;
  }

  .breaking-news-alert {
    position: relative;
    top: auto;
    right: auto;
    margin: 1rem;
    max-width: none;
  }
}

@media (max-width: 576px) {
  .category-hero-section {
    padding: 1.5rem 0;
  }

  .category-title {
    font-size: 1.75rem;
  }

  .category-description {
    font-size: 1rem;
  }

  .stat-item {
    padding: 0.375rem 0.75rem;
  }

  .stat-number {
    font-size: 1.25rem;
  }

  .advanced-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .view-controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-state-icon {
    font-size: 3rem;
  }
}

/* ================================== */
/* PRINT STYLES */
/* ================================== */

@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  .navbar,
  .footer,
  .btn {
    display: none !important;
  }
  
  .article-card {
    border: 1px solid #ccc !important;
    box-shadow: none !important;
    break-inside: avoid;
  }
}