using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using NewsSite.Data;
using NewsSite.Models;
using System.Text.RegularExpressions;

namespace NewsSite.Services
{
    public class CategoryService : ICategoryService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CategoryService> _logger;
        private readonly IMemoryCache _cache;
        private const int DefaultCacheExpirationMinutes = 30;

        public CategoryService(ApplicationDbContext context, ILogger<CategoryService> logger, IMemoryCache cache)
        {
            _context = context;
            _logger = logger;
            _cache = cache;
        }

        #region Basic CRUD Operations

        public async Task<Category?> GetCategoryByIdAsync(int id)
        {
            return await GetByIdAsync(id);
        }

        public async Task<Category?> GetCategoryBySlugAsync(string slug)
        {
            return await GetBySlugAsync(slug);
        }

        public async Task<Category?> GetByIdAsync(int id)
        {
            try
            {
                var cacheKey = $"category_{id}";
                if (_cache.TryGetValue(cacheKey, out Category? cachedCategory))
                {
                    return cachedCategory;
                }

                var category = await _context.Categories.FirstOrDefaultAsync(c => c.Id == id);

                if (category != null)
                {
                    _cache.Set(cacheKey, category, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return category;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category with ID {CategoryId}", id);
                throw;
            }
        }

        public async Task<Category?> GetBySlugAsync(string slug)
        {
            try
            {
                var cacheKey = $"category_slug_{slug}";
                if (_cache.TryGetValue(cacheKey, out Category? cachedCategory))
                {
                    return cachedCategory;
                }

                var category = await _context.Categories.FirstOrDefaultAsync(c => c.Slug == slug);

                if (category != null)
                {
                    _cache.Set(cacheKey, category, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                }

                return category;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category with slug {Slug}", slug);
                throw;
            }
        }

        public async Task<IEnumerable<Category>> GetAllCategoriesAsync()
        {
            try
            {
                const string cacheKey = "all_categories";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Category>? cachedCategories))
                {
                    return cachedCategories!;
                }

                var categories = await _context.Categories
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Name)
                    .ToListAsync();

                _cache.Set(cacheKey, categories, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return categories;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all categories");
                throw;
            }
        }

        public async Task<IEnumerable<Category>> GetActiveCategoriesAsync()
        {
            try
            {
                const string cacheKey = "active_categories";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Category>? cachedCategories))
                {
                    return cachedCategories!;
                }

                var categories = await _context.Categories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Name)
                    .ToListAsync();

                _cache.Set(cacheKey, categories, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return categories;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active categories");
                throw;
            }
        }

        public async Task<Category> CreateCategoryAsync(Category category)
        {
            try
            {
                // Generate slug if not provided
                if (string.IsNullOrEmpty(category.Slug))
                {
                    category.Slug = await GenerateUniqueSlugAsync(category.Name);
                }

                // Set display order if not specified
                if (category.DisplayOrder == 0)
                {
                    var maxOrder = await _context.Categories.MaxAsync(c => (int?)c.DisplayOrder) ?? 0;
                    category.DisplayOrder = maxOrder + 1;
                }

                category.CreatedDate = DateTime.UtcNow;

                _context.Categories.Add(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created new category with ID {CategoryId}", category.Id);
                InvalidateCategoryCache();

                return category;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating category");
                throw;
            }
        }

        public async Task<Category> UpdateCategoryAsync(Category category)
        {
            try
            {
                var existingCategory = await _context.Categories.FindAsync(category.Id);
                if (existingCategory == null)
                {
                    throw new ArgumentException("Category not found", nameof(category));
                }

                // Update properties
                existingCategory.Name = category.Name;
                existingCategory.Description = category.Description;
                existingCategory.Slug = category.Slug;
                existingCategory.Color = category.Color;
                existingCategory.Icon = category.Icon;
                existingCategory.DisplayOrder = category.DisplayOrder;
                existingCategory.IsActive = category.IsActive;
                existingCategory.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated category with ID {CategoryId}", category.Id);
                InvalidateCategoryCache();

                return existingCategory;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating category with ID {CategoryId}", category.Id);
                throw;
            }
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            try
            {
                var category = await _context.Categories.FindAsync(id);
                if (category == null)
                {
                    return false;
                }

                // Check if category has articles
                var hasArticles = await _context.Articles.AnyAsync(a => a.CategoryId == id);
                if (hasArticles)
                {
                    throw new InvalidOperationException("Cannot delete category that contains articles. Move articles to another category first.");
                }

                _context.Categories.Remove(category);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted category with ID {CategoryId}", id);
                InvalidateCategoryCache();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting category with ID {CategoryId}", id);
                throw;
            }
        }

        #endregion

        #region Category with Statistics

        public async Task<CategoryWithStats?> GetCategoryWithStatsAsync(int id)
        {
            try
            {
                var cacheKey = $"category_stats_{id}";
                if (_cache.TryGetValue(cacheKey, out CategoryWithStats? cached))
                {
                    return cached;
                }

                var category = await _context.Categories.FindAsync(id);
                if (category == null) return null;

                var stats = await CalculateCategoryStatsAsync(id);
                var result = new CategoryWithStats
                {
                    Category = category,
                    ArticleCount = stats.ArticleCount,
                    PublishedArticleCount = stats.PublishedArticleCount,
                    LastArticleDate = stats.LastArticleDate
                };

                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(10)); // Shorter cache for stats
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category with stats for ID {CategoryId}", id);
                throw;
            }
        }

        public async Task<CategoryWithStats?> GetCategoryWithStatsBySlugAsync(string slug)
        {
            try
            {
                var category = await GetBySlugAsync(slug);
                if (category == null) return null;

                return await GetCategoryWithStatsAsync(category.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category with stats for slug {Slug}", slug);
                throw;
            }
        }

        public async Task<IEnumerable<CategoryWithStats>> GetCategoriesWithStatsAsync()
        {
            try
            {
                const string cacheKey = "categories_with_stats";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<CategoryWithStats>? cached))
                {
                    return cached!;
                }

                var categories = await GetAllCategoriesAsync();
                var result = new List<CategoryWithStats>();

                foreach (var category in categories)
                {
                    var stats = await CalculateCategoryStatsAsync(category.Id);
                    result.Add(new CategoryWithStats
                    {
                        Category = category,
                        ArticleCount = stats.ArticleCount,
                        PublishedArticleCount = stats.PublishedArticleCount,
                        LastArticleDate = stats.LastArticleDate
                    });
                }

                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(10));
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving categories with stats");
                throw;
            }
        }

        public async Task<IEnumerable<CategoryWithStats>> GetActiveCategoriesWithStatsAsync()
        {
            try
            {
                const string cacheKey = "active_categories_with_stats";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<CategoryWithStats>? cached))
                {
                    return cached!;
                }

                var categories = await GetActiveCategoriesAsync();
                var result = new List<CategoryWithStats>();

                foreach (var category in categories)
                {
                    var stats = await CalculateCategoryStatsAsync(category.Id);
                    result.Add(new CategoryWithStats
                    {
                        Category = category,
                        ArticleCount = stats.ArticleCount,
                        PublishedArticleCount = stats.PublishedArticleCount,
                        LastArticleDate = stats.LastArticleDate
                    });
                }

                _cache.Set(cacheKey, result, TimeSpan.FromMinutes(10));
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active categories with stats");
                throw;
            }
        }

        #endregion

        #region Category-specific Article Operations

        public async Task<PaginatedResult<Article>> GetCategoryArticlesWithFiltersAsync(
            int categoryId, 
            int page, 
            int pageSize, 
            string sortBy, 
            bool sortDesc, 
            Dictionary<string, object> filters, 
            ArticleStatus? status = null)
        {
            try
            {
                var query = _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Include(a => a.Metadata)
                    .Where(a => a.CategoryId == categoryId);

                if (status.HasValue)
                {
                    query = query.Where(a => a.Status == status.Value);
                }

                // Apply filters
                if (filters != null)
                {
                    foreach (var filter in filters)
                    {
                        switch (filter.Key.ToLower())
                        {
                            case "searchterm":
                                if (filter.Value is string searchTerm && !string.IsNullOrWhiteSpace(searchTerm))
                                {
                                    query = query.Where(a => a.Title.Contains(searchTerm) || 
                                                           (a.Summary != null && a.Summary.Contains(searchTerm)) ||
                                                           (a.Author != null && a.Author.Contains(searchTerm)));
                                }
                                break;

                            case "fromdate":
                                if (filter.Value is DateTime fromDate)
                                {
                                    query = query.Where(a => a.PublishedDate >= fromDate);
                                }
                                break;

                            case "todate":
                                if (filter.Value is DateTime toDate)
                                {
                                    query = query.Where(a => a.PublishedDate <= toDate);
                                }
                                break;

                            case "isfeatured":
                                if (filter.Value is bool isFeatured)
                                {
                                    query = query.Where(a => a.IsFeatured == isFeatured);
                                }
                                break;

                            case "isbreaking":
                                if (filter.Value is bool isBreaking)
                                {
                                    query = query.Where(a => a.IsBreaking == isBreaking);
                                }
                                break;

                            case "istrending":
                                if (filter.Value is bool isTrending)
                                {
                                    query = query.Where(a => a.IsTrending == isTrending);
                                }
                                break;

                            case "author":
                                if (filter.Value is string author && !string.IsNullOrWhiteSpace(author))
                                {
                                    query = query.Where(a => a.Author != null && a.Author.Contains(author));
                                }
                                break;

                            case "sourceid":
                                if (filter.Value is int sourceId)
                                {
                                    query = query.Where(a => a.SourceId == sourceId);
                                }
                                break;
                        }
                    }
                }

                // Apply sorting
                query = sortBy.ToLower() switch
                {
                    "title" => sortDesc ? query.OrderByDescending(a => a.Title) : query.OrderBy(a => a.Title),
                    "author" => sortDesc ? query.OrderByDescending(a => a.Author) : query.OrderBy(a => a.Author),
                    "viewcount" => sortDesc ? query.OrderByDescending(a => a.ViewCount) : query.OrderBy(a => a.ViewCount),
                    "priority" => sortDesc ? query.OrderByDescending(a => a.Priority) : query.OrderBy(a => a.Priority),
                    _ => sortDesc ? query.OrderByDescending(a => a.PublishedDate) : query.OrderBy(a => a.PublishedDate)
                };

                var totalCount = await query.CountAsync();
                var skip = (page - 1) * pageSize;

                var articles = await query
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                return new PaginatedResult<Article>
                {
                    Items = articles,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving filtered articles for category {CategoryId} with filters {@Filters}", categoryId, filters);
                throw;
            }
        }

        public async Task<PaginatedResult<Article>> GetCategoryArticlesAsync(int categoryId, int page = 1, int pageSize = 20, ArticleStatus? status = null)
        {
            try
            {
                var query = _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => a.CategoryId == categoryId);

                if (status.HasValue)
                {
                    query = query.Where(a => a.Status == status.Value);
                }

                var totalCount = await query.CountAsync();
                var skip = (page - 1) * pageSize;

                var articles = await query
                    .OrderByDescending(a => a.PublishedDate)
                    .Skip(skip)
                    .Take(pageSize)
                    .ToListAsync();

                return new PaginatedResult<Article>
                {
                    Items = articles,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<PaginatedResult<Article>> GetCategoryArticlesBySlugAsync(string slug, int page = 1, int pageSize = 20, ArticleStatus? status = null)
        {
            try
            {
                var category = await GetBySlugAsync(slug);
                if (category == null)
                {
                    return new PaginatedResult<Article>
                    {
                        Items = new List<Article>(),
                        TotalCount = 0,
                        Page = page,
                        PageSize = pageSize
                    };
                }

                return await GetCategoryArticlesAsync(category.Id, page, pageSize, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for category slug {Slug}", slug);
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetLatestCategoryArticlesAsync(int categoryId, int count = 10)
        {
            try
            {
                return await _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => a.CategoryId == categoryId && a.Status == ArticleStatus.Published)
                    .OrderByDescending(a => a.PublishedDate)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving latest articles for category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<IEnumerable<Article>> GetFeaturedCategoryArticlesAsync(int categoryId, int count = 5)
        {
            try
            {
                return await _context.Articles
                    .Include(a => a.Category)
                    .Include(a => a.Source)
                    .Where(a => a.CategoryId == categoryId && 
                               a.Status == ArticleStatus.Published && 
                               a.IsFeatured)
                    .OrderByDescending(a => a.Priority)
                    .ThenByDescending(a => a.PublishedDate)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured articles for category {CategoryId}", categoryId);
                throw;
            }
        }

        #endregion

        #region Category Management

        public async Task<bool> SetCategoryActiveStatusAsync(int categoryId, bool isActive)
        {
            try
            {
                var category = await _context.Categories.FindAsync(categoryId);
                if (category == null) return false;

                category.IsActive = isActive;
                category.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateCategoryCache();

                _logger.LogInformation("Updated active status for category {CategoryId} to {IsActive}", categoryId, isActive);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating active status for category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<bool> UpdateCategoryDisplayOrderAsync(int categoryId, int displayOrder)
        {
            try
            {
                var category = await _context.Categories.FindAsync(categoryId);
                if (category == null) return false;

                category.DisplayOrder = displayOrder;
                category.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                InvalidateCategoryCache();

                _logger.LogInformation("Updated display order for category {CategoryId} to {DisplayOrder}", categoryId, displayOrder);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating display order for category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<bool> ReorderCategoriesAsync(Dictionary<int, int> categoryDisplayOrders)
        {
            try
            {
                var categoryIds = categoryDisplayOrders.Keys.ToList();
                var categories = await _context.Categories
                    .Where(c => categoryIds.Contains(c.Id))
                    .ToListAsync();

                foreach (var category in categories)
                {
                    if (categoryDisplayOrders.TryGetValue(category.Id, out var newOrder))
                    {
                        category.DisplayOrder = newOrder;
                        category.ModifiedDate = DateTime.UtcNow;
                    }
                }

                await _context.SaveChangesAsync();
                InvalidateCategoryCache();

                _logger.LogInformation("Reordered {Count} categories", categories.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reordering categories");
                throw;
            }
        }

        #endregion

        #region Statistics and Analytics

        public async Task<int> GetArticleCountByCategoryAsync(int categoryId, ArticleStatus? status = null)
        {
            try
            {
                var query = _context.Articles.Where(a => a.CategoryId == categoryId);

                if (status.HasValue)
                {
                    query = query.Where(a => a.Status == status.Value);
                }

                return await query.CountAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting article count for category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<Dictionary<int, int>> GetArticleCountsForAllCategoriesAsync(ArticleStatus? status = null)
        {
            try
            {
                var query = _context.Articles.AsQueryable();

                if (status.HasValue)
                {
                    query = query.Where(a => a.Status == status.Value);
                }

                var counts = await query
                    .GroupBy(a => a.CategoryId)
                    .Select(g => new { CategoryId = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.CategoryId, x => x.Count);

                return counts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting article counts for all categories");
                throw;
            }
        }

        public async Task<IEnumerable<Category>> GetTopCategoriesByArticleCountAsync(int count = 10, int days = 30)
        {
            try
            {
                var fromDate = DateTime.UtcNow.AddDays(-days);

                var topCategories = await _context.Articles
                    .Where(a => a.PublishedDate >= fromDate && a.Status == ArticleStatus.Published)
                    .GroupBy(a => a.CategoryId)
                    .Select(g => new { CategoryId = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .Take(count)
                    .Join(_context.Categories,
                          ac => ac.CategoryId,
                          c => c.Id,
                          (ac, c) => c)
                    .ToListAsync();

                return topCategories;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top categories by article count");
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetCategoryArticleCountByDateRangeAsync(int categoryId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var articles = await _context.Articles
                    .Where(a => a.CategoryId == categoryId && 
                               a.PublishedDate >= fromDate && 
                               a.PublishedDate <= toDate)
                    .GroupBy(a => a.PublishedDate.Date)
                    .Select(g => new { Date = g.Key, Count = g.Count() })
                    .ToListAsync();

                return articles.ToDictionary(x => x.Date.ToString("yyyy-MM-dd"), x => x.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting category article count by date range for category {CategoryId}", categoryId);
                throw;
            }
        }

        #endregion

        #region Category Navigation and Hierarchy

        public async Task<IEnumerable<Category>> GetCategoriesForNavigationAsync()
        {
            try
            {
                const string cacheKey = "navigation_categories";
                if (_cache.TryGetValue(cacheKey, out IEnumerable<Category>? cached))
                {
                    return cached!;
                }

                var categories = await _context.Categories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Name)
                    .Select(c => new Category
                    {
                        Id = c.Id,
                        Name = c.Name,
                        Slug = c.Slug,
                        Color = c.Color,
                        Icon = c.Icon,
                        DisplayOrder = c.DisplayOrder
                    })
                    .ToListAsync();

                _cache.Set(cacheKey, categories, TimeSpan.FromMinutes(DefaultCacheExpirationMinutes));
                return categories;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving categories for navigation");
                throw;
            }
        }

        public async Task<Category?> GetCategoryForBreadcrumbAsync(string slug)
        {
            try
            {
                return await _context.Categories
                    .Where(c => c.Slug == slug && c.IsActive)
                    .Select(c => new Category
                    {
                        Id = c.Id,
                        Name = c.Name,
                        Slug = c.Slug
                    })
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category for breadcrumb with slug {Slug}", slug);
                throw;
            }
        }

        #endregion

        #region Validation

        public async Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null)
        {
            try
            {
                var query = _context.Categories.Where(c => c.Slug == slug);

                if (excludeId.HasValue)
                {
                    query = query.Where(c => c.Id != excludeId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking slug uniqueness for {Slug}", slug);
                throw;
            }
        }

        public async Task<bool> ValidateCategoryAsync(Category category)
        {
            try
            {
                // Check required fields
                if (string.IsNullOrWhiteSpace(category.Name) || string.IsNullOrWhiteSpace(category.Slug))
                {
                    return false;
                }

                // Check slug uniqueness
                if (!await IsSlugUniqueAsync(category.Slug, category.Id))
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating category");
                throw;
            }
        }

        public async Task<bool> CanDeleteCategoryAsync(int categoryId)
        {
            try
            {
                var hasArticles = await _context.Articles.AnyAsync(a => a.CategoryId == categoryId);
                return !hasArticles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if category {CategoryId} can be deleted", categoryId);
                throw;
            }
        }

        #endregion

        #region Bulk Operations

        public async Task<int> BulkUpdateActiveStatusAsync(IEnumerable<int> categoryIds, bool isActive)
        {
            try
            {
                var categories = await _context.Categories
                    .Where(c => categoryIds.Contains(c.Id))
                    .ToListAsync();

                foreach (var category in categories)
                {
                    category.IsActive = isActive;
                    category.ModifiedDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                InvalidateCategoryCache();

                _logger.LogInformation("Bulk updated active status to {IsActive} for {Count} categories", isActive, categories.Count);
                return categories.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating category active status");
                throw;
            }
        }

        public async Task<int> BulkReorderCategoriesAsync(Dictionary<int, int> reorderMap)
        {
            try
            {
                return await ReorderCategoriesAsync(reorderMap) ? reorderMap.Count : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk reordering categories");
                throw;
            }
        }

        #endregion

        #region Search and Filtering

        public async Task<IEnumerable<Category>> SearchCategoriesAsync(string query)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(query))
                {
                    return await GetAllCategoriesAsync();
                }

                return await _context.Categories
                    .Where(c => c.Name.Contains(query) || 
                               (c.Description != null && c.Description.Contains(query)))
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching categories with query: {Query}", query);
                throw;
            }
        }

        public async Task<IEnumerable<Category>> GetCategoriesByColorAsync(string color)
        {
            try
            {
                return await _context.Categories
                    .Where(c => c.Color == color)
                    .OrderBy(c => c.DisplayOrder)
                    .ThenBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving categories by color {Color}", color);
                throw;
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task<(int ArticleCount, int PublishedArticleCount, DateTime? LastArticleDate)> CalculateCategoryStatsAsync(int categoryId)
        {
            var articles = await _context.Articles
                .Where(a => a.CategoryId == categoryId)
                .GroupBy(a => a.CategoryId)
                .Select(g => new
                {
                    ArticleCount = g.Count(),
                    PublishedArticleCount = g.Count(a => a.Status == ArticleStatus.Published),
                    LastArticleDate = g.Max(a => (DateTime?)a.PublishedDate)
                })
                .FirstOrDefaultAsync();

            return (
                articles?.ArticleCount ?? 0,
                articles?.PublishedArticleCount ?? 0,
                articles?.LastArticleDate
            );
        }

        private async Task<string> GenerateUniqueSlugAsync(string name)
        {
            var baseSlug = GenerateSlug(name);
            var slug = baseSlug;
            var counter = 1;

            while (!await IsSlugUniqueAsync(slug))
            {
                slug = $"{baseSlug}-{counter}";
                counter++;
            }

            return slug;
        }

        private static string GenerateSlug(string name)
        {
            if (string.IsNullOrEmpty(name))
                return string.Empty;

            var slug = name
                .ToLowerInvariant()
                .Replace(" ", "-")
                .Replace("&", "and");
            
            slug = Regex.Replace(slug, @"[^a-z0-9\-]", "");
            slug = Regex.Replace(slug, @"-+", "-");
            
            return slug.Trim('-');
        }

        private void InvalidateCategoryCache()
        {
            // Remove category-related cache entries
            var cacheKeys = new[]
            {
                "all_categories",
                "active_categories",
                "categories_with_stats",
                "active_categories_with_stats",
                "navigation_categories"
            };

            foreach (var key in cacheKeys)
            {
                _cache.Remove(key);
            }
        }

        #endregion
    }
}