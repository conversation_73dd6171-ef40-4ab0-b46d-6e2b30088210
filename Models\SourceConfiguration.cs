using System.ComponentModel.DataAnnotations;

namespace NewsSite.Models
{
    public class SourceConfiguration
    {
        public int Id { get; set; }

        [Required]
        public int SourceId { get; set; }

        // Update intervals in minutes
        public int UpdateIntervalMinutes { get; set; } = 60; // Default 1 hour

        public int MaxArticlesPerSync { get; set; } = 50;

        public int ArticleRetentionDays { get; set; } = 30; // How long to keep articles

        // Content processing settings
        public bool AutoCategorize { get; set; } = true;
        public bool AutoGenerateTags { get; set; } = true;
        public bool EnableDuplicateDetection { get; set; } = true;

        // Summary generation settings
        public int MaxSummaryLength { get; set; } = 500; // Max characters for summary
        public bool UseAiSummarization { get; set; } = false;

        // Priority settings
        public int Priority { get; set; } = 5; // 1-10, higher means more frequent updates

        // Filtering settings
        public DateTime? ContentFromDate { get; set; } // Only sync content after this date

        [MaxLength(1000)]
        public string? CustomHeaders { get; set; } // JSON for custom HTTP headers

        [MaxLength(2000)]
        public string? XPathSelectors { get; set; } // JSON for custom content extraction
        
        // API-specific settings (stored as JSON)
        [MaxLength(2000)]
        public string? ApiSettingsJson { get; set; }
        
        // Parsed API settings for easy access
        public Dictionary<string, string>? ApiSettings
        {
            get
            {
                if (string.IsNullOrEmpty(ApiSettingsJson))
                    return null;
                    
                try
                {
                    return System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(ApiSettingsJson);
                }
                catch
                {
                    return null;
                }
            }
            set
            {
                ApiSettingsJson = value != null ? System.Text.Json.JsonSerializer.Serialize(value) : null;
            }
        }
        
        // Max items to fetch per sync
        public int? MaxItems => MaxArticlesPerSync;

        // Quality control
        public int MinContentLength { get; set; } = 100; // Minimum characters required
        public bool RequireImages { get; set; } = false;

        // Notification settings
        public bool NotifyOnErrors { get; set; } = true;
        public bool NotifyOnSuccess { get; set; } = false;

        [MaxLength(500)]
        public string? NotificationEmails { get; set; } // JSON array of email addresses
        
        // Analytics tracking
        public int RecentArticleCount { get; set; } = 0;
        public int AverageViews { get; set; } = 0;

        // Audit fields
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime? ModifiedDate { get; set; }

        // Navigation properties
        public virtual Source Source { get; set; } = null!;
    }
}