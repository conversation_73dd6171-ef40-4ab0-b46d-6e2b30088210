using System.Text;
using System.Text.RegularExpressions;
using System.Globalization;
using NewsSite.Data;
using Microsoft.EntityFrameworkCore;

namespace NewsSite.Services.Utilities
{
    public class SlugService : ISlugService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SlugService> _logger;

        // Common words to remove from slugs for better SEO
        private static readonly string[] StopWords = 
        {
            "a", "an", "and", "are", "as", "at", "be", "by", "for", "from", "has", "he", 
            "in", "is", "it", "its", "of", "on", "that", "the", "to", "was", "were", "will", "with"
        };

        public SlugService(ApplicationDbContext context, ILogger<SlugService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<string> GenerateSlugAsync(string text)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(text))
                    return "untitled";

                // Clean and normalize the text
                var cleanText = CleanTextForSlug(text);
                
                // Convert to slug format
                var slug = ConvertToSlug(cleanText);
                
                // Ensure minimum and maximum length
                slug = EnsureSlugLength(slug);
                
                return slug;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating slug from text: {Text}", text);
                return "untitled";
            }
        }

        public async Task<string> GenerateUniqueSlugAsync(string text)
        {
            try
            {
                var baseSlug = await GenerateSlugAsync(text);
                var uniqueSlug = baseSlug;
                var counter = 1;

                // Keep checking and incrementing until we find a unique slug
                while (await SlugExistsAsync(uniqueSlug))
                {
                    uniqueSlug = $"{baseSlug}-{counter}";
                    counter++;
                    
                    // Prevent infinite loops
                    if (counter > 1000)
                    {
                        uniqueSlug = $"{baseSlug}-{Guid.NewGuid().ToString("N")[..8]}";
                        break;
                    }
                }

                return uniqueSlug;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating unique slug from text: {Text}", text);
                return $"untitled-{Guid.NewGuid().ToString("N")[..8]}";
            }
        }

        public bool IsValidSlug(string slug)
        {
            if (string.IsNullOrWhiteSpace(slug))
                return false;

            // Valid slug pattern: lowercase letters, numbers, and hyphens only
            // Must start and end with alphanumeric character
            var pattern = @"^[a-z0-9]+(?:-[a-z0-9]+)*$";
            return Regex.IsMatch(slug, pattern) && 
                   slug.Length >= 3 && 
                   slug.Length <= 100;
        }

        public string CleanTextForSlug(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return "";

            try
            {
                // Convert to lowercase and normalize
                text = text.ToLowerInvariant().Trim();
                
                // Remove HTML tags
                text = Regex.Replace(text, @"<[^>]*>", "");
                
                // Remove diacritics (accents) - convert é to e, ñ to n, etc.
                text = RemoveDiacritics(text);
                
                // Replace common symbols and punctuation with spaces
                text = Regex.Replace(text, @"[^\w\s-]", " ");
                
                // Replace multiple spaces with single space
                text = Regex.Replace(text, @"\s+", " ");
                
                // Remove stop words (but keep the text meaningful)
                text = RemoveStopWords(text);
                
                return text.Trim();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning text for slug: {Text}", text);
                return "text";
            }
        }

        public async Task<bool> SlugExistsAsync(string slug)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(slug))
                    return false;

                // Check if slug exists in articles, categories, tags, or sources
                var existsInArticles = await _context.Articles.AnyAsync(a => a.Slug == slug);
                if (existsInArticles) return true;

                var existsInCategories = await _context.Categories.AnyAsync(c => c.Slug == slug);
                if (existsInCategories) return true;

                var existsInTags = await _context.Tags.AnyAsync(t => t.Slug == slug);
                if (existsInTags) return true;

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if slug exists: {Slug}", slug);
                return true; // Assume it exists to be safe
            }
        }

        private string ConvertToSlug(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return "";

            // Replace spaces and underscores with hyphens
            text = Regex.Replace(text, @"[\s_]+", "-");
            
            // Remove any characters that aren't letters, numbers, or hyphens
            text = Regex.Replace(text, @"[^a-z0-9\-]", "");
            
            // Remove multiple consecutive hyphens
            text = Regex.Replace(text, @"-{2,}", "-");
            
            // Remove leading and trailing hyphens
            text = text.Trim('-');
            
            return text;
        }

        private string RemoveDiacritics(string text)
        {
            try
            {
                // Normalize to decomposed form
                var normalizedString = text.Normalize(NormalizationForm.FormD);
                var stringBuilder = new StringBuilder();

                foreach (var c in normalizedString)
                {
                    var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                    if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                    {
                        stringBuilder.Append(c);
                    }
                }

                return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
            }
            catch
            {
                return text; // Return original if normalization fails
            }
        }

        private string RemoveStopWords(string text)
        {
            try
            {
                var words = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                
                // Only remove stop words if we have enough words remaining
                var nonStopWords = words.Where(word => 
                    !StopWords.Contains(word) && 
                    word.Length > 1).ToArray();

                // If removing stop words would leave us with too few words, keep them
                if (nonStopWords.Length < 2 && words.Length > 2)
                {
                    return string.Join(" ", words);
                }

                return nonStopWords.Any() ? string.Join(" ", nonStopWords) : string.Join(" ", words);
            }
            catch
            {
                return text; // Return original if processing fails
            }
        }

        private string EnsureSlugLength(string slug)
        {
            if (string.IsNullOrWhiteSpace(slug))
                return "untitled";

            // Ensure minimum length
            if (slug.Length < 3)
            {
                return slug.PadRight(3, '0');
            }

            // Ensure maximum length
            if (slug.Length > 100)
            {
                // Try to cut at a hyphen near the end
                var cutPosition = slug.LastIndexOf('-', 97);
                if (cutPosition > 50) // Make sure we don't cut too early
                {
                    slug = slug.Substring(0, cutPosition);
                }
                else
                {
                    slug = slug.Substring(0, 100);
                }
            }

            return slug;
        }
    }
}