using System.ComponentModel.DataAnnotations;

namespace NewsSite.Models
{
    public class Tag
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Slug { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(20)]
        public string? Color { get; set; } // For UI theming

        public int UsageCount { get; set; } = 0; // Track how many articles use this tag

        public bool IsActive { get; set; } = true;

        public bool IsSystemGenerated { get; set; } = false; // Auto-generated vs manually created

        // Audit fields
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime? ModifiedDate { get; set; }

        // Navigation properties
        public virtual ICollection<ArticleTag> ArticleTags { get; set; } = new List<ArticleTag>();
    }
}