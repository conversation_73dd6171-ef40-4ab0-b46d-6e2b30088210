using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NewsSite.Areas.Admin.Models;
using NewsSite.Data;
using NewsSite.Models;
using NewsSite.Services;

namespace NewsSite.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "Admin")]
    public class SourcesController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ISourceService _sourceService;
        private readonly ILogger<SourcesController> _logger;

        public SourcesController(
            ApplicationDbContext context,
            ISourceService sourceService,
            ILogger<SourcesController> logger)
        {
            _context = context;
            _sourceService = sourceService;
            _logger = logger;
        }

        public async Task<IActionResult> Index(SourceFilterOptions? filters = null)
        {
            try
            {
                filters ??= new SourceFilterOptions();
                
                var query = _context.Sources
                    .Include(s => s.Configuration)
                    .AsQueryable();

                // Apply filters
                if (filters.Type.HasValue)
                {
                    query = query.Where(s => s.Type == filters.Type.Value);
                }

                if (filters.IsActive.HasValue)
                {
                    query = query.Where(s => s.IsActive == filters.IsActive.Value);
                }

                if (!string.IsNullOrEmpty(filters.SearchTerm))
                {
                    query = query.Where(s => s.Name.Contains(filters.SearchTerm) || 
                                           s.Description!.Contains(filters.SearchTerm));
                }

                if (!string.IsNullOrEmpty(filters.Status))
                {
                    switch (filters.Status.ToLower())
                    {
                        case "healthy":
                            query = query.Where(s => s.ConsecutiveFailures == 0);
                            break;
                        case "warning":
                            query = query.Where(s => s.ConsecutiveFailures > 0 && s.ConsecutiveFailures < 3);
                            break;
                        case "error":
                            query = query.Where(s => s.ConsecutiveFailures >= 3);
                            break;
                    }
                }

                // Apply sorting
                query = filters.SortBy.ToLower() switch
                {
                    "name" => filters.SortDirection == "desc" ? query.OrderByDescending(s => s.Name) : query.OrderBy(s => s.Name),
                    "type" => filters.SortDirection == "desc" ? query.OrderByDescending(s => s.Type) : query.OrderBy(s => s.Type),
                    "lastsync" => filters.SortDirection == "desc" ? query.OrderByDescending(s => s.LastSyncDate) : query.OrderBy(s => s.LastSyncDate),
                    "createdate" => filters.SortDirection == "desc" ? query.OrderByDescending(s => s.CreatedDate) : query.OrderBy(s => s.CreatedDate),
                    _ => query.OrderBy(s => s.Name)
                };

                var totalItems = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalItems / filters.PageSize);

                var sources = await query
                    .Skip((filters.Page - 1) * filters.PageSize)
                    .Take(filters.PageSize)
                    .Select(s => new SourceListItem
                    {
                        Id = s.Id,
                        Name = s.Name,
                        Type = s.Type,
                        Url = s.Url,
                        IsActive = s.IsActive,
                        LastSync = s.LastSyncDate,
                        LastSuccessfulSync = s.LastSuccessfulSyncDate,
                        ConsecutiveFailures = s.ConsecutiveFailures,
                        Status = s.ConsecutiveFailures == 0 ? "Healthy" :
                                 s.ConsecutiveFailures < 3 ? "Warning" : "Error",
                        UpdateIntervalMinutes = s.Configuration != null ? s.Configuration.UpdateIntervalMinutes : 60,
                        ArticleCount = s.Articles.Count(),
                        RecentArticleCount = s.Articles.Count(a => a.CreatedDate >= DateTime.UtcNow.AddDays(-7)),
                        LastError = s.LastErrorMessage,
                        HasApiKey = !string.IsNullOrEmpty(s.ApiKey),
                        CreatedDate = s.CreatedDate
                    })
                    .ToListAsync();

                var viewModel = new SourceManagementViewModel
                {
                    Sources = sources,
                    Filters = filters,
                    Pagination = new PaginationInfo
                    {
                        CurrentPage = filters.Page,
                        TotalPages = totalPages,
                        TotalItems = totalItems,
                        PageSize = filters.PageSize,
                        HasPrevious = filters.Page > 1,
                        HasNext = filters.Page < totalPages
                    }
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading sources list");
                TempData["Error"] = "Failed to load sources. Please try again.";
                return View(new SourceManagementViewModel());
            }
        }

        public IActionResult Create()
        {
            var viewModel = new CreateSourceViewModel();
            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateSourceViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var source = new Source
                {
                    Name = model.Name,
                    Type = model.Type,
                    Url = model.Url,
                    Description = model.Description,
                    ApiKey = model.ApiKey,
                    ApiSecret = model.ApiSecret,
                    IsActive = model.IsActive,
                    RequiresAuthentication = model.RequiresAuthentication,
                    MaxRequestsPerHour = model.MaxRequestsPerHour,
                    EnableContentFiltering = model.EnableContentFiltering,
                    ContentFilterKeywords = model.ContentFilterKeywords,
                    CreatedBy = User.Identity!.Name ?? "Admin",
                    CreatedDate = DateTime.UtcNow
                };

                _context.Sources.Add(source);
                await _context.SaveChangesAsync();

                // Create configuration
                var configuration = new SourceConfiguration
                {
                    SourceId = source.Id,
                    UpdateIntervalMinutes = model.UpdateIntervalMinutes,
                    MaxArticlesPerSync = model.MaxArticlesPerSync,
                    ArticleRetentionDays = model.ArticleRetentionDays,
                    AutoCategorize = model.AutoCategorize,
                    AutoGenerateTags = model.AutoGenerateTags,
                    EnableDuplicateDetection = model.EnableDuplicateDetection,
                    Priority = model.Priority,
                    MaxSummaryLength = model.MaxSummaryLength,
                    UseAiSummarization = model.UseAiSummarization,
                    MinContentLength = model.MinContentLength,
                    RequireImages = model.RequireImages,
                    NotifyOnErrors = model.NotifyOnErrors,
                    NotifyOnSuccess = model.NotifyOnSuccess,
                    ContentFromDate = model.ContentFromDate,
                    NotificationEmails = model.NotificationEmails,
                    CustomHeaders = model.CustomHeaders,
                    ApiSettingsJson = model.ApiSettingsJson,
                    CreatedDate = DateTime.UtcNow
                };

                _context.SourceConfigurations.Add(configuration);
                await _context.SaveChangesAsync();

                TempData["Success"] = $"Source '{source.Name}' created successfully.";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating source");
                ModelState.AddModelError("", "Failed to create source. Please try again.");
                return View(model);
            }
        }

        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var source = await _context.Sources
                    .Include(s => s.Configuration)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (source == null)
                {
                    TempData["Error"] = "Source not found.";
                    return RedirectToAction(nameof(Index));
                }

                var recentArticleCount = await _context.Articles
                    .CountAsync(a => a.SourceId == id && a.CreatedDate >= DateTime.UtcNow.AddDays(-7));

                var totalArticleCount = await _context.Articles
                    .CountAsync(a => a.SourceId == id);

                var viewModel = new EditSourceViewModel
                {
                    Id = source.Id,
                    Name = source.Name,
                    Type = source.Type,
                    Url = source.Url,
                    Description = source.Description,
                    ApiKey = source.ApiKey,
                    ApiSecret = source.ApiSecret,
                    IsActive = source.IsActive,
                    RequiresAuthentication = source.RequiresAuthentication,
                    MaxRequestsPerHour = source.MaxRequestsPerHour,
                    EnableContentFiltering = source.EnableContentFiltering,
                    ContentFilterKeywords = source.ContentFilterKeywords,
                    CreatedDate = source.CreatedDate,
                    CreatedBy = source.CreatedBy,
                    ModifiedDate = source.ModifiedDate,
                    ModifiedBy = source.ModifiedBy,
                    LastSync = source.LastSyncDate,
                    LastSuccessfulSync = source.LastSuccessfulSyncDate,
                    ConsecutiveFailures = source.ConsecutiveFailures,
                    LastErrorMessage = source.LastErrorMessage,
                    ArticleCount = totalArticleCount,
                    RecentArticleCount = recentArticleCount
                };

                if (source.Configuration != null)
                {
                    viewModel.UpdateIntervalMinutes = source.Configuration.UpdateIntervalMinutes;
                    viewModel.MaxArticlesPerSync = source.Configuration.MaxArticlesPerSync;
                    viewModel.ArticleRetentionDays = source.Configuration.ArticleRetentionDays;
                    viewModel.AutoCategorize = source.Configuration.AutoCategorize;
                    viewModel.AutoGenerateTags = source.Configuration.AutoGenerateTags;
                    viewModel.EnableDuplicateDetection = source.Configuration.EnableDuplicateDetection;
                    viewModel.Priority = source.Configuration.Priority;
                    viewModel.MaxSummaryLength = source.Configuration.MaxSummaryLength;
                    viewModel.UseAiSummarization = source.Configuration.UseAiSummarization;
                    viewModel.MinContentLength = source.Configuration.MinContentLength;
                    viewModel.RequireImages = source.Configuration.RequireImages;
                    viewModel.NotifyOnErrors = source.Configuration.NotifyOnErrors;
                    viewModel.NotifyOnSuccess = source.Configuration.NotifyOnSuccess;
                    viewModel.ContentFromDate = source.Configuration.ContentFromDate;
                    viewModel.NotificationEmails = source.Configuration.NotificationEmails;
                    viewModel.CustomHeaders = source.Configuration.CustomHeaders;
                    viewModel.ApiSettingsJson = source.Configuration.ApiSettingsJson;
                }

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading source for editing: {SourceId}", id);
                TempData["Error"] = "Failed to load source. Please try again.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EditSourceViewModel model)
        {
            if (id != model.Id)
            {
                TempData["Error"] = "Invalid source ID.";
                return RedirectToAction(nameof(Index));
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var source = await _context.Sources
                    .Include(s => s.Configuration)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (source == null)
                {
                    TempData["Error"] = "Source not found.";
                    return RedirectToAction(nameof(Index));
                }

                // Update source properties
                source.Name = model.Name;
                source.Type = model.Type;
                source.Url = model.Url;
                source.Description = model.Description;
                source.ApiKey = model.ApiKey;
                source.ApiSecret = model.ApiSecret;
                source.IsActive = model.IsActive;
                source.RequiresAuthentication = model.RequiresAuthentication;
                source.MaxRequestsPerHour = model.MaxRequestsPerHour;
                source.EnableContentFiltering = model.EnableContentFiltering;
                source.ContentFilterKeywords = model.ContentFilterKeywords;
                source.ModifiedBy = User.Identity!.Name ?? "Admin";
                source.ModifiedDate = DateTime.UtcNow;

                // Update or create configuration
                if (source.Configuration == null)
                {
                    source.Configuration = new SourceConfiguration
                    {
                        SourceId = source.Id,
                        CreatedDate = DateTime.UtcNow
                    };
                    _context.SourceConfigurations.Add(source.Configuration);
                }

                source.Configuration.UpdateIntervalMinutes = model.UpdateIntervalMinutes;
                source.Configuration.MaxArticlesPerSync = model.MaxArticlesPerSync;
                source.Configuration.ArticleRetentionDays = model.ArticleRetentionDays;
                source.Configuration.AutoCategorize = model.AutoCategorize;
                source.Configuration.AutoGenerateTags = model.AutoGenerateTags;
                source.Configuration.EnableDuplicateDetection = model.EnableDuplicateDetection;
                source.Configuration.Priority = model.Priority;
                source.Configuration.MaxSummaryLength = model.MaxSummaryLength;
                source.Configuration.UseAiSummarization = model.UseAiSummarization;
                source.Configuration.MinContentLength = model.MinContentLength;
                source.Configuration.RequireImages = model.RequireImages;
                source.Configuration.NotifyOnErrors = model.NotifyOnErrors;
                source.Configuration.NotifyOnSuccess = model.NotifyOnSuccess;
                source.Configuration.ContentFromDate = model.ContentFromDate;
                source.Configuration.NotificationEmails = model.NotificationEmails;
                source.Configuration.CustomHeaders = model.CustomHeaders;
                source.Configuration.ApiSettingsJson = model.ApiSettingsJson;
                source.Configuration.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                TempData["Success"] = $"Source '{source.Name}' updated successfully.";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating source: {SourceId}", id);
                ModelState.AddModelError("", "Failed to update source. Please try again.");
                return View(model);
            }
        }

        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var source = await _context.Sources
                    .Include(s => s.Configuration)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (source == null)
                {
                    TempData["Error"] = "Source not found.";
                    return RedirectToAction(nameof(Index));
                }

                var articleCount = await _context.Articles.CountAsync(a => a.SourceId == id);
                ViewBag.ArticleCount = articleCount;

                return View(source);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading source for deletion: {SourceId}", id);
                TempData["Error"] = "Failed to load source. Please try again.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var source = await _context.Sources
                    .Include(s => s.Configuration)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (source == null)
                {
                    TempData["Error"] = "Source not found.";
                    return RedirectToAction(nameof(Index));
                }

                var sourceName = source.Name;

                // Delete configuration first
                if (source.Configuration != null)
                {
                    _context.SourceConfigurations.Remove(source.Configuration);
                }

                // Note: Articles are not deleted due to foreign key constraints
                // In a real implementation, you might want to mark them as orphaned
                _context.Sources.Remove(source);
                await _context.SaveChangesAsync();

                TempData["Success"] = $"Source '{sourceName}' deleted successfully.";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting source: {SourceId}", id);
                TempData["Error"] = "Failed to delete source. It may have associated articles.";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        public async Task<IActionResult> BulkAction([FromBody] BulkSourceAction action)
        {
            try
            {
                if (action.SourceIds?.Any() != true)
                {
                    return Json(new { success = false, message = "No sources selected" });
                }

                var sources = await _context.Sources
                    .Where(s => action.SourceIds.Contains(s.Id))
                    .ToListAsync();

                if (!sources.Any())
                {
                    return Json(new { success = false, message = "No valid sources found" });
                }

                var count = 0;
                var currentUser = User.Identity!.Name ?? "Admin";

                switch (action.Action.ToLower())
                {
                    case "activate":
                        foreach (var source in sources.Where(s => !s.IsActive))
                        {
                            source.IsActive = true;
                            source.ModifiedBy = currentUser;
                            source.ModifiedDate = DateTime.UtcNow;
                            count++;
                        }
                        break;

                    case "deactivate":
                        foreach (var source in sources.Where(s => s.IsActive))
                        {
                            source.IsActive = false;
                            source.ModifiedBy = currentUser;
                            source.ModifiedDate = DateTime.UtcNow;
                            count++;
                        }
                        break;

                    case "delete":
                        // For safety, only allow deletion if no articles exist
                        var sourcesWithoutArticles = sources.Where(s => !_context.Articles.Any(a => a.SourceId == s.Id)).ToList();
                        foreach (var source in sourcesWithoutArticles)
                        {
                            _context.Sources.Remove(source);
                            count++;
                        }
                        break;

                    case "sync":
                        // In a real implementation, this would trigger background sync jobs
                        count = sources.Count;
                        break;

                    default:
                        return Json(new { success = false, message = "Invalid action" });
                }

                await _context.SaveChangesAsync();

                return Json(new { 
                    success = true, 
                    message = $"{action.Action} completed for {count} sources" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing bulk action: {Action}", action.Action);
                return Json(new { success = false, message = "Failed to perform bulk action" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> TestSource([FromBody] int sourceId)
        {
            try
            {
                var source = await _context.Sources.FindAsync(sourceId);
                if (source == null)
                {
                    return Json(new { success = false, message = "Source not found" });
                }

                // In a real implementation, this would test the actual connection
                var result = new SourceTestResult
                {
                    IsSuccessful = true,
                    Message = "Connection test successful",
                    TestArticleCount = new Random().Next(1, 10),
                    ResponseTime = TimeSpan.FromMilliseconds(new Random().Next(100, 2000)),
                    Warnings = new List<string>(),
                    Errors = new List<string>()
                };

                // Simulate some test conditions
                if (string.IsNullOrEmpty(source.ApiKey) && source.RequiresAuthentication)
                {
                    result.Warnings.Add("API key is required but not configured");
                }

                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing source: {SourceId}", sourceId);
                return Json(new SourceTestResult
                {
                    IsSuccessful = false,
                    Message = "Test failed due to an error",
                    Errors = new List<string> { "Internal error occurred" }
                });
            }
        }
    }
}