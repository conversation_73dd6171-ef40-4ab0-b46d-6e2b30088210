using Microsoft.AspNetCore.Mvc;
using NewsSite.Models;
using NewsSite.Services;

namespace NewsSite.Controllers
{
    public class CategoryController : Controller
    {
        private readonly ICategoryService _categoryService;
        private readonly IContentService _contentService;
        private readonly ILogger<CategoryController> _logger;

        public CategoryController(
            ICategoryService categoryService,
            IContentService contentService,
            ILogger<CategoryController> logger)
        {
            _categoryService = categoryService;
            _contentService = contentService;
            _logger = logger;
        }

        [Route("{slug}")]
        public async Task<IActionResult> Index(
            string slug,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string sortBy = "PublishedDate",
            [FromQuery] bool sortDesc = true,
            [FromQuery] string viewMode = "grid",
            [FromQuery] string searchTerm = "",
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] bool featuredOnly = false,
            [FromQuery] bool breakingOnly = false)
        {
            try
            {
                // Validate parameters
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 20;
                if (!IsValidViewMode(viewMode)) viewMode = "grid";

                // Get category by slug
                var category = await _categoryService.GetBySlugAsync(slug);
                if (category == null)
                {
                    _logger.LogWarning("Category not found for slug: {Slug}", slug);
                    return NotFound();
                }

                // Get category statistics
                var categoryWithStats = await _categoryService.GetCategoryWithStatsAsync(category.Id);
                if (categoryWithStats == null)
                {
                    _logger.LogWarning("Category statistics not found for ID: {CategoryId}", category.Id);
                    return NotFound();
                }

                // Get articles for this category with filtering
                var articlesResult = await GetFilteredCategoryArticles(
                    category.Id, page, pageSize, sortBy, sortDesc, 
                    searchTerm, fromDate, toDate, featuredOnly, breakingOnly);

                // Create view model
                var viewModel = new CategoryPageViewModel
                {
                    Category = categoryWithStats.Category,
                    ArticleCount = categoryWithStats.ArticleCount,
                    PublishedArticleCount = categoryWithStats.PublishedArticleCount,
                    Articles = articlesResult.Items.ToList(),
                    TotalCount = articlesResult.TotalCount,
                    CurrentPage = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)articlesResult.TotalCount / pageSize),
                    ViewMode = viewMode,
                    SortBy = sortBy,
                    SortDesc = sortDesc,
                    SearchTerm = searchTerm,
                    FromDate = fromDate,
                    ToDate = toDate,
                    FeaturedOnly = featuredOnly,
                    BreakingOnly = breakingOnly,
                    HasNextPage = page * pageSize < articlesResult.TotalCount,
                    HasPreviousPage = page > 1
                };

                // Set page metadata for SEO
                ViewData["Title"] = $"{category.Name} - AI Frontiers";
                ViewData["Description"] = category.Description ?? $"Latest {category.Name.ToLower()} articles and updates from AI Frontiers";
                ViewData["Keywords"] = $"AI news, {category.Name.ToLower()}, artificial intelligence, {slug}";
                ViewData["CategoryColor"] = category.Color;
                ViewData["CategoryIcon"] = category.Icon;

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading category page for slug: {Slug}", slug);
                return StatusCode(500, "An error occurred while loading the category page.");
            }
        }

        [HttpGet]
        [Route("api/category/{slug}/articles")]
        public async Task<IActionResult> GetArticlesJson(
            string slug,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string sortBy = "PublishedDate",
            [FromQuery] bool sortDesc = true,
            [FromQuery] string searchTerm = "",
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] bool featuredOnly = false,
            [FromQuery] bool breakingOnly = false)
        {
            try
            {
                // Validate parameters
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 20;

                // Get category by slug
                var category = await _categoryService.GetBySlugAsync(slug);
                if (category == null)
                {
                    return NotFound(new { error = "Category not found" });
                }

                // Get filtered articles
                var articlesResult = await GetFilteredCategoryArticles(
                    category.Id, page, pageSize, sortBy, sortDesc,
                    searchTerm, fromDate, toDate, featuredOnly, breakingOnly);

                var response = new
                {
                    articles = articlesResult.Items.Select(MapToArticleDto),
                    totalCount = articlesResult.TotalCount,
                    page = page,
                    pageSize = pageSize,
                    totalPages = (int)Math.Ceiling((double)articlesResult.TotalCount / pageSize),
                    hasNextPage = page * pageSize < articlesResult.TotalCount,
                    hasPreviousPage = page > 1
                };

                return Json(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles for category: {Slug}", slug);
                return StatusCode(500, new { error = "Unable to retrieve articles" });
            }
        }

        private async Task<PaginatedResult<Article>> GetFilteredCategoryArticles(
            int categoryId, 
            int page, 
            int pageSize, 
            string sortBy, 
            bool sortDesc,
            string searchTerm,
            DateTime? fromDate,
            DateTime? toDate,
            bool featuredOnly,
            bool breakingOnly)
        {
            // Build filter criteria
            var filters = new Dictionary<string, object>();
            
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                filters["searchTerm"] = searchTerm.Trim();
            }
            
            if (fromDate.HasValue)
            {
                filters["fromDate"] = fromDate.Value;
            }
            
            if (toDate.HasValue)
            {
                filters["toDate"] = toDate.Value.Date.AddDays(1).AddSeconds(-1); // End of day
            }
            
            if (featuredOnly)
            {
                filters["isFeatured"] = true;
            }
            
            if (breakingOnly)
            {
                filters["isBreaking"] = true;
            }

            // Get articles with filters applied
            return await _categoryService.GetCategoryArticlesWithFiltersAsync(
                categoryId, page, pageSize, sortBy, sortDesc, filters, ArticleStatus.Published);
        }

        private static bool IsValidViewMode(string viewMode)
        {
            return viewMode switch
            {
                "grid" => true,
                "list" => true,
                "compact" => true,
                _ => false
            };
        }

        private static object MapToArticleDto(Article article)
        {
            return new
            {
                id = article.Id,
                title = article.Title,
                slug = article.Slug,
                summary = article.Summary,
                imageUrl = article.ImageUrl,
                imageAlt = article.ImageAlt,
                author = article.Author,
                publishedDate = article.PublishedDate,
                isFeatured = article.IsFeatured,
                isBreaking = article.IsBreaking,
                isTrending = article.IsTrending,
                categoryName = article.Category?.Name ?? "Uncategorized",
                categorySlug = article.Category?.Slug ?? "uncategorized",
                categoryColor = article.Category?.Color,
                categoryIcon = article.Category?.Icon,
                sourceName = article.Source?.Name ?? "Unknown Source",
                viewCount = article.ViewCount,
                readingTimeMinutes = article.Metadata?.ReadingTimeMinutes ?? CalculateReadingTime(article.Summary),
                timeAgo = GetTimeAgo(article.PublishedDate),
                publishedDateFormatted = article.PublishedDate.ToString("MMM dd, yyyy"),
                publishedTimeFormatted = article.PublishedDate.ToString("h:mm tt")
            };
        }

        private static int CalculateReadingTime(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return 1;

            var wordCount = text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
            const int wordsPerMinute = 200; // Average reading speed
            return Math.Max(1, (int)Math.Ceiling((double)wordCount / wordsPerMinute));
        }

        private static string GetTimeAgo(DateTime publishedDate)
        {
            var timeSpan = DateTime.UtcNow - publishedDate;

            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} minutes ago";
            
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} hours ago";
            
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} days ago";
            
            if (timeSpan.TotalDays < 30)
                return $"{(int)(timeSpan.TotalDays / 7)} weeks ago";
            
            if (timeSpan.TotalDays < 365)
                return $"{(int)(timeSpan.TotalDays / 30)} months ago";
            
            return $"{(int)(timeSpan.TotalDays / 365)} years ago";
        }
    }

    // DTO Classes
    public class CategoryPageViewModel
    {
        public Category Category { get; set; } = new();
        public int ArticleCount { get; set; }
        public int PublishedArticleCount { get; set; }
        public List<Article> Articles { get; set; } = new();
        public int TotalCount { get; set; }
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public string ViewMode { get; set; } = "grid";
        public string SortBy { get; set; } = "PublishedDate";
        public bool SortDesc { get; set; } = true;
        public string SearchTerm { get; set; } = "";
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool FeaturedOnly { get; set; }
        public bool BreakingOnly { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

}