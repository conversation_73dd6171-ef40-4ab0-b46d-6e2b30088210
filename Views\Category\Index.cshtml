@model CategoryPageViewModel
@{
    ViewData["Title"] = $"{Model.Category.Name} - AI Frontiers";
    var categoryColor = Model.Category.Color ?? "#00d4ff";
    var categoryIcon = Model.Category.Icon ?? "fas fa-layer-group";
}

<!-- Category Hero Section -->
<section class="category-hero-section" style="--category-color: @categoryColor;">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="category-hero-content">
                    <!-- Breadcrumb Navigation -->
                    <nav aria-label="breadcrumb" class="mb-3">
                        <ol class="breadcrumb bg-transparent p-0 m-0">
                            <li class="breadcrumb-item">
                                <a href="@Url.Action("Index", "Home")" class="text-muted">
                                    <i class="fas fa-home me-1"></i>Home
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="text-muted">Categories</span>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <span style="color: @categoryColor;">@Model.Category.Name</span>
                            </li>
                        </ol>
                    </nav>

                    <!-- Category Header -->
                    <div class="d-flex align-items-center mb-3">
                        <div class="category-icon-large me-3" style="color: @categoryColor;">
                            <i class="@categoryIcon"></i>
                        </div>
                        <div>
                            <h1 class="category-title mb-2" style="color: @categoryColor;">@Model.Category.Name</h1>
                            <p class="category-description text-muted mb-0">
                                @(Model.Category.Description ?? $"Latest {Model.Category.Name.ToLower()} articles and updates")
                            </p>
                        </div>
                    </div>

                    <!-- Category Statistics -->
                    <div class="category-stats">
                        <div class="row g-3">
                            <div class="col-auto">
                                <div class="stat-item">
                                    <span class="stat-number" style="color: @categoryColor;">@Model.PublishedArticleCount</span>
                                    <span class="stat-label">Articles</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="stat-item">
                                    <span class="stat-number" style="color: @categoryColor;">@Model.Articles.Count(a => a.IsBreaking)</span>
                                    <span class="stat-label">Breaking</span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="stat-item">
                                    <span class="stat-number" style="color: @categoryColor;">@Model.Articles.Count(a => a.IsFeatured)</span>
                                    <span class="stat-label">Featured</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="category-actions">
                    <!-- Subscribe/Follow button -->
                    <button class="btn btn-outline-primary me-2" onclick="toggleCategoryFollow('@Model.Category.Slug')">
                        <i class="fas fa-bell me-2"></i>Follow Category
                    </button>
                    <!-- RSS Feed -->
                    <a href="/rss/category/@Model.Category.Slug" class="btn btn-secondary" target="_blank" title="RSS Feed">
                        <i class="fas fa-rss me-2"></i>RSS
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters and Controls Section -->
<section class="category-controls-section">
    <div class="container-fluid">
        <div class="category-controls-card">
            <div class="row align-items-center">
                <!-- Search and Filters -->
                <div class="col-lg-6">
                    <div class="search-filters">
                        <!-- Search Input -->
                        <div class="search-input-group">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="Search within @Model.Category.Name..." 
                                       value="@Model.SearchTerm">
                                <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Advanced Filters -->
                        <div class="advanced-filters mt-2">
                            <div class="row g-2">
                                <div class="col-auto">
                                    <select class="form-select form-select-sm" id="dateFilter">
                                        <option value="">All Time</option>
                                        <option value="today">Today</option>
                                        <option value="week">This Week</option>
                                        <option value="month">This Month</option>
                                        <option value="year">This Year</option>
                                        <option value="custom">Custom Range</option>
                                    </select>
                                </div>
                                <div class="col-auto">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="featuredOnly" 
                                               @(Model.FeaturedOnly ? "checked" : "")>
                                        <label class="form-check-label" for="featuredOnly">
                                            <i class="fas fa-star me-1" style="color: @categoryColor;"></i>Featured Only
                                        </label>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="breakingOnly" 
                                               @(Model.BreakingOnly ? "checked" : "")>
                                        <label class="form-check-label" for="breakingOnly">
                                            <i class="fas fa-bolt me-1" style="color: @categoryColor;"></i>Breaking Only
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- View Controls -->
                <div class="col-lg-6">
                    <div class="view-controls">
                        <div class="d-flex justify-content-lg-end align-items-center gap-3">
                            <!-- Sort Options -->
                            <div class="sort-options">
                                <select class="form-select form-select-sm" id="sortBy" style="min-width: 150px;">
                                    <option value="PublishedDate" @(Model.SortBy == "PublishedDate" ? "selected" : "")>Latest</option>
                                    <option value="ViewCount" @(Model.SortBy == "ViewCount" ? "selected" : "")>Most Viewed</option>
                                    <option value="Title" @(Model.SortBy == "Title" ? "selected" : "")>Title A-Z</option>
                                    <option value="Author" @(Model.SortBy == "Author" ? "selected" : "")>Author</option>
                                </select>
                            </div>

                            <!-- Sort Direction -->
                            <button class="btn btn-outline-secondary btn-sm" id="sortDirection" 
                                    onclick="toggleSortDirection()" title="@(Model.SortDesc ? "Descending" : "Ascending")">
                                <i class="fas @(Model.SortDesc ? "fa-sort-amount-down" : "fa-sort-amount-up")"></i>
                            </button>

                            <!-- View Mode Toggle -->
                            <div class="view-mode-toggle">
                                <div class="btn-group" role="group" aria-label="View mode">
                                    <button type="button" class="btn btn-outline-secondary btn-sm @(Model.ViewMode == "grid" ? "active" : "")" 
                                            onclick="setViewMode('grid')" title="Grid View">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm @(Model.ViewMode == "list" ? "active" : "")" 
                                            onclick="setViewMode('list')" title="List View">
                                        <i class="fas fa-list"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm @(Model.ViewMode == "compact" ? "active" : "")" 
                                            onclick="setViewMode('compact')" title="Compact View">
                                        <i class="fas fa-bars"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Articles Section -->
<section class="category-articles-section">
    <div class="container-fluid">
        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="text-center py-5" style="display: none;">
            <div class="spinner-border" style="color: @categoryColor;" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2 text-muted">Loading articles...</div>
        </div>

        <!-- Articles Container -->
        <div id="articlesContainer">
            @if (Model.Articles.Any())
            {
                <!-- Results Header -->
                <div class="results-header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="results-info">
                            <span class="text-muted">
                                Showing @((Model.CurrentPage - 1) * Model.PageSize + 1)-@Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalCount) 
                                of @Model.TotalCount articles
                            </span>
                        </div>
                        <div class="results-actions">
                            <button class="btn btn-outline-secondary btn-sm" onclick="exportResults()" title="Export Results">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Articles Grid/List -->
                <div class="articles-grid" data-view-mode="@Model.ViewMode">
                    @foreach (var article in Model.Articles)
                    {
                        @await Html.PartialAsync("_ArticleCard", article, new ViewDataDictionary(ViewData)
                        {
                            ["ViewMode"] = Model.ViewMode,
                            ["CategoryColor"] = categoryColor,
                            ["ShowCategoryInfo"] = false
                        })
                    }
                </div>

                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <nav aria-label="Category articles pagination" class="mt-5">
                        <ul class="pagination justify-content-center">
                            @if (Model.HasPreviousPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="#" onclick="changePage(@(Model.CurrentPage - 1))" title="Previous Page">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            }

                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                            {
                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                    <a class="page-link" href="#" onclick="changePage(@i)">@i</a>
                                </li>
                            }

                            @if (Model.HasNextPage)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="#" onclick="changePage(@(Model.CurrentPage + 1))" title="Next Page">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            }
                        </ul>
                    </nav>
                }

                <!-- Load More Button (for infinite scroll option) -->
                @if (Model.HasNextPage)
                {
                    <div class="text-center mt-4">
                        <button class="btn btn-outline-primary" id="loadMoreBtn" onclick="loadMoreArticles()">
                            <i class="fas fa-plus me-2"></i>Load More Articles
                        </button>
                    </div>
                }
            }
            else
            {
                <!-- Empty State -->
                <div class="empty-state text-center py-5">
                    <div class="empty-state-icon mb-3" style="color: @categoryColor;">
                        <i class="@categoryIcon fa-3x"></i>
                    </div>
                    <h3 class="empty-state-title">No Articles Found</h3>
                    <p class="empty-state-description text-muted">
                        @if (!string.IsNullOrEmpty(Model.SearchTerm) || Model.FeaturedOnly || Model.BreakingOnly)
                        {
                            <text>No articles match your current filters. Try adjusting your search criteria.</text>
                        }
                        else
                        {
                            <text>There are no articles in this category yet. Check back soon for updates!</text>
                        }
                    </p>
                    <div class="empty-state-actions mt-4">
                        @if (!string.IsNullOrEmpty(Model.SearchTerm) || Model.FeaturedOnly || Model.BreakingOnly)
                        {
                            <button class="btn btn-outline-primary" onclick="clearAllFilters()">
                                <i class="fas fa-filter-circle-xmark me-2"></i>Clear All Filters
                            </button>
                        }
                        <a href="@Url.Action("Index", "Home")" class="btn btn-secondary ms-2">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<!-- Category-Specific Features Section -->
@switch (Model.Category.Slug)
{
    case "breaking-ai-news":
        <section class="category-features-section breaking-features">
            <div class="container-fluid">
                <div class="features-card">
                    <h4><i class="fas fa-bolt me-2" style="color: @categoryColor;"></i>Breaking News Alerts</h4>
                    <p class="text-muted">Get instant notifications when breaking AI news is published.</p>
                    <button class="btn btn-primary btn-sm" onclick="enableBreakingAlerts()">
                        <i class="fas fa-bell me-2"></i>Enable Alerts
                    </button>
                </div>
            </div>
        </section>
        break;

    case "youtube-ai-discoveries":
        <section class="category-features-section youtube-features">
            <div class="container-fluid">
                <div class="features-card">
                    <h4><i class="fab fa-youtube me-2" style="color: @categoryColor;"></i>Video Discoveries</h4>
                    <p class="text-muted">AI-powered video content discovery from top creators and channels.</p>
                    <div class="feature-stats">
                        <span class="badge bg-secondary me-2">@Model.Articles.Count Videos</span>
                        <span class="badge bg-secondary">Multiple Channels</span>
                    </div>
                </div>
            </div>
        </section>
        break;

    case "research-papers":
        <section class="category-features-section research-features">
            <div class="container-fluid">
                <div class="features-card">
                    <h4><i class="fas fa-flask me-2" style="color: @categoryColor;"></i>Research Papers</h4>
                    <p class="text-muted">Latest peer-reviewed research and academic publications in AI.</p>
                    <div class="feature-actions">
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="filterByJournal()">
                            <i class="fas fa-journal-whills me-2"></i>Filter by Journal
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="showCitationHelp()">
                            <i class="fas fa-quote-right me-2"></i>Citation Help
                        </button>
                    </div>
                </div>
            </div>
        </section>
        break;

    case "agentic-platforms":
        <section class="category-features-section agentic-features">
            <div class="container-fluid">
                <div class="features-card">
                    <h4><i class="fas fa-robot me-2" style="color: @categoryColor;"></i>Agentic Platforms</h4>
                    <p class="text-muted">Autonomous AI systems and intelligent agent platforms.</p>
                    <div class="platform-categories">
                        <span class="badge bg-info me-2">Conversational</span>
                        <span class="badge bg-success me-2">Automation</span>
                        <span class="badge bg-warning me-2">Multi-Agent</span>
                        <span class="badge bg-danger">Enterprise</span>
                    </div>
                </div>
            </div>
        </section>
        break;

    case "ai-development-tools":
        <section class="category-features-section tools-features">
            <div class="container-fluid">
                <div class="features-card">
                    <h4><i class="fas fa-tools me-2" style="color: @categoryColor;"></i>Development Tools</h4>
                    <p class="text-muted">Essential tools and frameworks for AI developers.</p>
                    <div class="tools-filters">
                        <button class="btn btn-outline-secondary btn-sm me-2" onclick="filterByToolType('frameworks')">Frameworks</button>
                        <button class="btn btn-outline-secondary btn-sm me-2" onclick="filterByToolType('libraries')">Libraries</button>
                        <button class="btn btn-outline-secondary btn-sm me-2" onclick="filterByToolType('platforms')">Platforms</button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="filterByToolType('apis')">APIs</button>
                    </div>
                </div>
            </div>
        </section>
        break;

    case "trending-open-source":
        <section class="category-features-section opensource-features">
            <div class="container-fluid">
                <div class="features-card">
                    <h4><i class="fab fa-github me-2" style="color: @categoryColor;"></i>Open Source Trending</h4>
                    <p class="text-muted">Discover trending open-source AI projects and repositories.</p>
                    <div class="github-stats">
                        <div class="row g-3">
                            <div class="col-auto">
                                <small class="text-muted">Total Stars:</small>
                                <strong class="ms-1">@(Model.Articles.Sum(a => a.Metadata?.ReadingTimeMinutes ?? 0) * 100)+</strong>
                            </div>
                            <div class="col-auto">
                                <small class="text-muted">Languages:</small>
                                <strong class="ms-1">Python, JavaScript, Go+</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        break;
}

@section Scripts {
    <script>
        // Category page JavaScript functionality
        const categorySlug = '@Model.Category.Slug';
        const categoryColor = '@categoryColor';
        let currentPage = @Model.CurrentPage;
        let isLoading = false;

        // Initialize page
        $(document).ready(function() {
            initializeCategoryPage();
            setupEventHandlers();
            
            // Auto-refresh for breaking news category
            if (categorySlug === 'breaking-ai-news') {
                setInterval(checkForNewArticles, 30000); // Check every 30 seconds
            }
        });

        function initializeCategoryPage() {
            // Set category color theme
            document.documentElement.style.setProperty('--current-category-color', categoryColor);
            
            // Initialize filters from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            
            // Set form values from URL parameters
            $('#searchInput').val(urlParams.get('searchTerm') || '');
            $('#sortBy').val(urlParams.get('sortBy') || 'PublishedDate');
            $('#featuredOnly').prop('checked', urlParams.get('featuredOnly') === 'true');
            $('#breakingOnly').prop('checked', urlParams.get('breakingOnly') === 'true');
            
            // Set view mode
            const viewMode = urlParams.get('viewMode') || 'grid';
            setViewMode(viewMode);
        }

        function setupEventHandlers() {
            // Search input with debounce
            let searchTimeout;
            $('#searchInput').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch();
                }, 500);
            });

            // Filter change handlers
            $('#sortBy, #dateFilter').on('change', performSearch);
            $('#featuredOnly, #breakingOnly').on('change', performSearch);

            // Infinite scroll
            $(window).scroll(function() {
                if ($(window).scrollTop() + $(window).height() >= $(document).height() - 1000) {
                    if (!isLoading && $('#loadMoreBtn').is(':visible')) {
                        loadMoreArticles();
                    }
                }
            });
        }

        function performSearch() {
            if (isLoading) return;
            
            isLoading = true;
            showLoading();
            
            const params = {
                searchTerm: $('#searchInput').val(),
                sortBy: $('#sortBy').val(),
                sortDesc: @Model.SortDesc.ToString().ToLower(),
                featuredOnly: $('#featuredOnly').is(':checked'),
                breakingOnly: $('#breakingOnly').is(':checked'),
                page: 1,
                pageSize: @Model.PageSize,
                viewMode: $('.view-mode-toggle .btn.active').data('mode') || 'grid'
            };

            // Add date filter
            const dateFilter = $('#dateFilter').val();
            if (dateFilter) {
                const dateRange = getDateRange(dateFilter);
                params.fromDate = dateRange.from;
                params.toDate = dateRange.to;
            }

            // Update URL
            const url = new URL(window.location);
            Object.keys(params).forEach(key => {
                if (params[key]) {
                    url.searchParams.set(key, params[key]);
                } else {
                    url.searchParams.delete(key);
                }
            });
            window.history.pushState({}, '', url);

            // Fetch articles
            fetchArticles(params)
                .then(data => {
                    updateArticlesDisplay(data, true);
                    currentPage = 1;
                })
                .finally(() => {
                    isLoading = false;
                    hideLoading();
                });
        }

        function loadMoreArticles() {
            if (isLoading) return;
            
            isLoading = true;
            $('#loadMoreBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Loading...');
            
            const params = getCurrentParams();
            params.page = currentPage + 1;
            
            fetchArticles(params)
                .then(data => {
                    updateArticlesDisplay(data, false);
                    currentPage++;
                    
                    if (!data.hasNextPage) {
                        $('#loadMoreBtn').hide();
                    }
                })
                .finally(() => {
                    isLoading = false;
                    $('#loadMoreBtn').prop('disabled', false).html('<i class="fas fa-plus me-2"></i>Load More Articles');
                });
        }

        function fetchArticles(params) {
            return $.ajax({
                url: `/api/category/${categorySlug}/articles`,
                data: params,
                type: 'GET'
            });
        }

        function updateArticlesDisplay(data, replace = true) {
            if (replace) {
                $('#articlesContainer').html(renderArticles(data));
            } else {
                $('.articles-grid').append(renderArticlesList(data.articles));
            }
            
            // Update results info
            updateResultsInfo(data);
            
            // Reinitialize any interactive elements
            initializeArticleCards();
        }

        function renderArticles(data) {
            if (data.articles.length === 0) {
                return renderEmptyState();
            }
            
            let html = `
                <div class="results-header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="results-info">
                            <span class="text-muted">Showing ${data.articles.length} of ${data.totalCount} articles</span>
                        </div>
                        <div class="results-actions">
                            <button class="btn btn-outline-secondary btn-sm" onclick="exportResults()">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                        </div>
                    </div>
                </div>
                <div class="articles-grid" data-view-mode="${getCurrentViewMode()}">
                    ${renderArticlesList(data.articles)}
                </div>
            `;
            
            if (data.hasNextPage) {
                html += `
                    <div class="text-center mt-4">
                        <button class="btn btn-outline-primary" id="loadMoreBtn" onclick="loadMoreArticles()">
                            <i class="fas fa-plus me-2"></i>Load More Articles
                        </button>
                    </div>
                `;
            }
            
            return html;
        }

        function renderArticlesList(articles) {
            return articles.map(article => renderArticleCard(article)).join('');
        }

        function renderArticleCard(article) {
            const viewMode = getCurrentViewMode();
            const cardClass = viewMode === 'list' ? 'article-card-list' : 
                             viewMode === 'compact' ? 'article-card-compact' : 'article-card';
            
            return `
                <div class="col-12 ${viewMode === 'grid' ? 'col-md-6 col-lg-4' : ''} mb-4">
                    <article class="${cardClass}">
                        ${article.imageUrl ? `<img src="${article.imageUrl}" alt="${article.imageAlt || article.title}" class="card-img-top">` : ''}
                        <div class="card-body">
                            <h5 class="card-title">
                                <a href="/article/${article.slug}" class="text-decoration-none">${article.title}</a>
                            </h5>
                            <p class="card-text">${article.summary}</p>
                            <div class="article-meta">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>${article.timeAgo}
                                    </small>
                                    <div class="article-badges">
                                        ${article.isBreaking ? `<span class="badge" style="background-color: ${categoryColor}20; color: ${categoryColor};">Breaking</span>` : ''}
                                        ${article.isFeatured ? `<span class="badge" style="background-color: ${categoryColor}20; color: ${categoryColor};">Featured</span>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>
            `;
        }

        function renderEmptyState() {
            return `
                <div class="empty-state text-center py-5">
                    <div class="empty-state-icon mb-3" style="color: ${categoryColor};">
                        <i class="@categoryIcon fa-3x"></i>
                    </div>
                    <h3 class="empty-state-title">No Articles Found</h3>
                    <p class="empty-state-description text-muted">
                        No articles match your current filters. Try adjusting your search criteria.
                    </p>
                    <div class="empty-state-actions mt-4">
                        <button class="btn btn-outline-primary" onclick="clearAllFilters()">
                            <i class="fas fa-filter-circle-xmark me-2"></i>Clear All Filters
                        </button>
                    </div>
                </div>
            `;
        }

        function setViewMode(mode) {
            $('.view-mode-toggle .btn').removeClass('active');
            $(`.view-mode-toggle .btn[onclick="setViewMode('${mode}')"]`).addClass('active');
            $('.articles-grid').attr('data-view-mode', mode);
            
            // Update URL parameter
            const url = new URL(window.location);
            url.searchParams.set('viewMode', mode);
            window.history.pushState({}, '', url);
        }

        function getCurrentViewMode() {
            return $('.view-mode-toggle .btn.active').data('mode') || 'grid';
        }

        function toggleSortDirection() {
            const currentDesc = @Model.SortDesc.ToString().ToLower();
            const newDesc = !currentDesc;
            
            $('#sortDirection i').toggleClass('fa-sort-amount-down fa-sort-amount-up');
            $('#sortDirection').attr('title', newDesc ? 'Descending' : 'Ascending');
            
            performSearch();
        }

        function changePage(page) {
            if (isLoading || page < 1) return;
            
            const params = getCurrentParams();
            params.page = page;
            
            isLoading = true;
            showLoading();
            
            fetchArticles(params)
                .then(data => {
                    updateArticlesDisplay(data, true);
                    currentPage = page;
                    
                    // Scroll to top of articles
                    $('html, body').animate({
                        scrollTop: $('.category-articles-section').offset().top - 100
                    }, 500);
                })
                .finally(() => {
                    isLoading = false;
                    hideLoading();
                });
        }

        function getCurrentParams() {
            return {
                searchTerm: $('#searchInput').val(),
                sortBy: $('#sortBy').val(),
                sortDesc: $('#sortDirection i').hasClass('fa-sort-amount-down'),
                featuredOnly: $('#featuredOnly').is(':checked'),
                breakingOnly: $('#breakingOnly').is(':checked'),
                pageSize: @Model.PageSize,
                viewMode: getCurrentViewMode()
            };
        }

        function clearSearch() {
            $('#searchInput').val('');
            performSearch();
        }

        function clearAllFilters() {
            $('#searchInput').val('');
            $('#dateFilter').val('');
            $('#featuredOnly, #breakingOnly').prop('checked', false);
            performSearch();
        }

        function showLoading() {
            $('#loadingIndicator').show();
            $('#articlesContainer').addClass('loading');
        }

        function hideLoading() {
            $('#loadingIndicator').hide();
            $('#articlesContainer').removeClass('loading');
        }

        function checkForNewArticles() {
            // Check for new breaking news articles
            if (categorySlug === 'breaking-ai-news') {
                fetchArticles({ page: 1, pageSize: 1, breakingOnly: true })
                    .then(data => {
                        if (data.articles.length > 0) {
                            const latestArticle = data.articles[0];
                            const lastCheck = localStorage.getItem('lastBreakingCheck');
                            
                            if (!lastCheck || new Date(latestArticle.publishedDate) > new Date(lastCheck)) {
                                showBreakingNewsNotification(latestArticle);
                                localStorage.setItem('lastBreakingCheck', new Date().toISOString());
                            }
                        }
                    });
            }
        }

        function showBreakingNewsNotification(article) {
            // Create and show breaking news notification
            const notification = $(`
                <div class="alert alert-danger alert-dismissible fade show breaking-news-alert" role="alert">
                    <i class="fas fa-bolt me-2"></i>
                    <strong>Breaking:</strong> <a href="/article/${article.slug}" class="alert-link">${article.title}</a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `);
            
            $('.category-articles-section .container-fluid').prepend(notification);
            
            // Auto-hide after 10 seconds
            setTimeout(() => {
                notification.fadeOut();
            }, 10000);
        }

        function getDateRange(filter) {
            const now = new Date();
            const ranges = {
                today: { from: new Date(now.setHours(0,0,0,0)), to: new Date(now.setHours(23,59,59,999)) },
                week: { from: new Date(now.setDate(now.getDate() - 7)), to: new Date() },
                month: { from: new Date(now.setDate(now.getDate() - 30)), to: new Date() },
                year: { from: new Date(now.setFullYear(now.getFullYear() - 1)), to: new Date() }
            };
            
            return ranges[filter] || { from: null, to: null };
        }

        function exportResults() {
            // Implement export functionality
            const params = getCurrentParams();
            params.export = 'csv';
            
            const url = `/api/category/${categorySlug}/articles/export?` + $.param(params);
            window.open(url, '_blank');
        }

        function toggleCategoryFollow(slug) {
            // Implement follow/unfollow functionality
            console.log('Toggle follow for category:', slug);
        }

        function initializeArticleCards() {
            // Initialize any interactive elements on article cards
            $('.article-card').hover(
                function() { $(this).find('.card-img-top').addClass('hover-scale'); },
                function() { $(this).find('.card-img-top').removeClass('hover-scale'); }
            );
        }

        // Category-specific functions
        function enableBreakingAlerts() {
            if ('Notification' in window) {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        localStorage.setItem('breakingAlertsEnabled', 'true');
                        $(event.target).html('<i class="fas fa-check me-2"></i>Alerts Enabled').addClass('btn-success').removeClass('btn-primary');
                    }
                });
            }
        }

        function filterByToolType(type) {
            $('#searchInput').val(type);
            performSearch();
        }

        function filterByJournal() {
            // Implement journal filtering for research papers
            console.log('Filter by journal');
        }

        function showCitationHelp() {
            // Show citation format help modal
            console.log('Show citation help');
        }
    </script>

    <style>
        /* Category-specific styles */
        .category-hero-section {
            background: linear-gradient(135deg, var(--ai-dark-primary) 0%, var(--ai-dark-secondary) 100%);
            padding: 3rem 0 2rem;
            border-bottom: 1px solid var(--ai-dark-tertiary);
            position: relative;
            overflow: hidden;
        }

        .category-hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 50%);
            opacity: 0.5;
        }

        .category-hero-content {
            position: relative;
            z-index: 1;
        }

        .category-icon-large {
            font-size: 3rem;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--ai-radius-lg);
            backdrop-filter: blur(10px);
        }

        .category-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }

        .category-description {
            font-size: 1.1rem;
            line-height: 1.5;
        }

        .category-stats {
            margin-top: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--ai-radius-md);
            backdrop-filter: blur(5px);
        }

        .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .stat-label {
            display: block;
            font-size: 0.8rem;
            color: var(--ai-text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .category-actions {
            margin-top: 1rem;
        }

        .category-controls-section {
            padding: 1.5rem 0;
            background: var(--ai-dark-secondary);
            border-bottom: 1px solid var(--ai-dark-tertiary);
        }

        .category-controls-card {
            background: var(--ai-dark-tertiary);
            border-radius: var(--ai-radius-lg);
            padding: 1.5rem;
            border: 1px solid var(--ai-dark-surface);
        }

        .search-input-group .input-group {
            border-radius: var(--ai-radius-md);
            overflow: hidden;
        }

        .search-input-group .input-group-text {
            background: var(--ai-dark-surface);
            border-color: var(--ai-dark-hover);
            color: var(--ai-text-muted);
        }

        .advanced-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            align-items: center;
        }

        .view-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .category-articles-section {
            padding: 2rem 0;
        }

        .articles-grid {
            display: grid;
            gap: 1.5rem;
        }

        .articles-grid[data-view-mode="grid"] {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }

        .articles-grid[data-view-mode="list"] {
            grid-template-columns: 1fr;
        }

        .articles-grid[data-view-mode="compact"] {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .article-card-list {
            display: flex;
            background: var(--ai-dark-secondary);
            border: 1px solid var(--ai-dark-tertiary);
            border-radius: var(--ai-radius-lg);
            overflow: hidden;
            transition: var(--ai-transition-normal);
        }

        .article-card-list:hover {
            transform: translateY(-2px);
            box-shadow: var(--ai-shadow-lg);
        }

        .article-card-list .card-img-top {
            width: 200px;
            height: 120px;
            object-fit: cover;
        }

        .article-card-compact {
            background: var(--ai-dark-secondary);
            border: 1px solid var(--ai-dark-tertiary);
            border-radius: var(--ai-radius-md);
            padding: 1rem;
            transition: var(--ai-transition-fast);
        }

        .article-card-compact:hover {
            background: var(--ai-dark-hover);
        }

        .category-features-section {
            padding: 1.5rem 0;
            background: var(--ai-dark-secondary);
            border-top: 1px solid var(--ai-dark-tertiary);
        }

        .features-card {
            background: var(--ai-dark-tertiary);
            border-radius: var(--ai-radius-lg);
            padding: 1.5rem;
            border: 1px solid var(--ai-dark-surface);
        }

        .breaking-news-alert {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1050;
            max-width: 400px;
            box-shadow: var(--ai-shadow-xl);
            border-left: 4px solid var(--ai-category-breaking);
        }

        .hover-scale {
            transform: scale(1.05);
            transition: transform 0.3s ease;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .category-hero-section {
                padding: 2rem 0 1.5rem;
            }

            .category-title {
                font-size: 2rem;
            }

            .category-icon-large {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }

            .category-controls-card {
                padding: 1rem;
            }

            .view-controls {
                justify-content: center;
                margin-top: 1rem;
            }

            .articles-grid[data-view-mode="grid"] {
                grid-template-columns: 1fr;
            }

            .article-card-list {
                flex-direction: column;
            }

            .article-card-list .card-img-top {
                width: 100%;
                height: 150px;
            }
        }
    </style>
}