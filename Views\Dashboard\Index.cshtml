@model NewsSite.Areas.Admin.Models.AdminDashboardViewModel
@{
    ViewData["Title"] = "Admin Dashboard";
    ViewData["Subtitle"] = "System overview and key metrics";
    Layout = "_AdminLayout";
}

<div class="row g-4 mb-4">
    <!-- Key Stats Cards -->
    <div class="col-xl-3 col-md-6">
        <div class="admin-stat-card">
            <div class="d-flex align-items-center">
                <div class="admin-stat-icon bg-primary bg-opacity-10 text-primary me-3">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div>
                    <h3 class="mb-0" id="total-articles">@Model.Stats.TotalArticles.ToString("N0")</h3>
                    <p class="text-muted mb-0">Total Articles</p>
                </div>
            </div>
            <div class="mt-3">
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    @Model.Stats.ArticlesToday today
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="admin-stat-card">
            <div class="d-flex align-items-center">
                <div class="admin-stat-icon bg-info bg-opacity-10 text-info me-3">
                    <i class="fas fa-rss"></i>
                </div>
                <div>
                    <h3 class="mb-0" id="active-sources">@Model.Stats.ActiveSources</h3>
                    <p class="text-muted mb-0">Active Sources</p>
                </div>
            </div>
            <div class="mt-3">
                <small class="text-muted">
                    @Model.Stats.TotalSources total sources
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="admin-stat-card">
            <div class="d-flex align-items-center">
                <div class="admin-stat-icon bg-warning bg-opacity-10 text-warning me-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div>
                    <h3 class="mb-0" id="failed-syncs">@Model.Stats.FailedSyncs</h3>
                    <p class="text-muted mb-0">Failed Syncs</p>
                </div>
            </div>
            <div class="mt-3">
                <small class="@(Model.Stats.FailedSyncs > 0 ? "text-warning" : "text-success")">
                    <i class="fas fa-@(Model.Stats.FailedSyncs > 0 ? "exclamation" : "check") me-1"></i>
                    @(Model.Stats.FailedSyncs > 0 ? "Attention needed" : "All healthy")
                </small>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="admin-stat-card">
            <div class="d-flex align-items-center">
                <div class="admin-stat-icon bg-success bg-opacity-10 text-success me-3">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <h3 class="mb-0" id="users-count">@Model.Stats.UsersCount.ToString("N0")</h3>
                    <p class="text-muted mb-0">Registered Users</p>
                </div>
            </div>
            <div class="mt-3">
                <small class="text-muted">
                    @Model.Stats.TotalCategories categories
                </small>
            </div>
        </div>
    </div>
</div>

<div class="row g-4 mb-4">
    <!-- Content Performance Chart -->
    <div class="col-xl-8">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Content Performance
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="chartPeriod" id="chart7days" checked>
                        <label class="btn btn-outline-primary" for="chart7days">7 Days</label>
                        
                        <input type="radio" class="btn-check" name="chartPeriod" id="chart30days">
                        <label class="btn btn-outline-primary" for="chart30days">30 Days</label>
                        
                        <input type="radio" class="btn-check" name="chartPeriod" id="chart90days">
                        <label class="btn btn-outline-primary" for="chart90days">90 Days</label>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="contentChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="col-xl-4">
        <div class="admin-card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-heartbeat me-2"></i>
                    System Health
                </h5>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-muted">Overall Status</span>
                        <span class="badge bg-@(Model.SystemHealth.IsHealthy ? "success" : "danger")">
                            @(Model.SystemHealth.IsHealthy ? "Healthy" : "Issues Detected")
                        </span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">CPU Usage</small>
                        <small>@Model.SystemHealth.CpuUsage.ToString("F1")%</small>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-@(Model.SystemHealth.CpuUsage > 80 ? "danger" : Model.SystemHealth.CpuUsage > 60 ? "warning" : "success")" 
                             style="width: @Model.SystemHealth.CpuUsage%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">Memory Usage</small>
                        <small>@Model.SystemHealth.MemoryUsage.ToString("F1")%</small>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-@(Model.SystemHealth.MemoryUsage > 80 ? "danger" : Model.SystemHealth.MemoryUsage > 60 ? "warning" : "success")" 
                             style="width: @Model.SystemHealth.MemoryUsage%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">Database Size</small>
                        <small>@((Model.SystemHealth.DatabaseSize / 1024.0 / 1024.0).ToString("F1")) MB</small>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">Background Jobs</small>
                        <small>@Model.SystemHealth.ActiveJobs active, @Model.SystemHealth.FailedJobs failed</small>
                    </div>
                </div>

                @if (Model.SystemHealth.Issues.Any())
                {
                    <div class="mt-3">
                        <h6 class="text-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Issues Detected
                        </h6>
                        @foreach (var issue in Model.SystemHealth.Issues)
                        {
                            <small class="d-block text-warning">• @issue</small>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<div class="row g-4 mb-4">
    <!-- Source Health Status -->
    <div class="col-xl-7">
        <div class="admin-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-rss me-2"></i>
                        Source Health Status
                    </h5>
                    <a href="/admin/sources" class="btn btn-sm btn-outline-primary">
                        View All Sources
                    </a>
                </div>

                <div class="table-responsive">
                    <table class="table table-dark table-hover">
                        <thead>
                            <tr>
                                <th>Source</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Last Sync</th>
                                <th>Articles (7d)</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var source in Model.SourceHealth)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="status-indicator <EMAIL>() me-2"></div>
                                            <div>
                                                <strong>@source.Name</strong>
                                                @if (!source.IsActive)
                                                {
                                                    <span class="badge bg-secondary ms-1">Inactive</span>
                                                }
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">@source.Type</span>
                                    </td>
                                    <td>
                                        <span class="<EMAIL>()">
                                            <i class="fas fa-@(source.Status == "Healthy" ? "check-circle" : source.Status == "Warning" ? "exclamation-triangle" : "times-circle") me-1"></i>
                                            @source.Status
                                        </span>
                                        @if (source.ConsecutiveFailures > 0)
                                        {
                                            <small class="d-block text-muted">@source.ConsecutiveFailures consecutive failures</small>
                                        }
                                    </td>
                                    <td>
                                        @if (source.LastSync.HasValue)
                                        {
                                            <span title="@source.LastSync.Value.ToString("yyyy-MM-dd HH:mm:ss UTC")">
                                                @source.LastSync.Value.ToString("MMM dd, HH:mm")
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Never</span>
                                        }
                                    </td>
                                    <td>
                                        <span class="badge bg-@(source.ArticlesThisWeek > 0 ? "success" : "secondary")">
                                            @source.ArticlesThisWeek
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="syncSource(@source.Id)" title="Sync Now">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                            <a href="/admin/sources/edit/@source.Id" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="col-xl-5">
        <div class="admin-card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-history me-2"></i>
                    Recent Activity
                </h5>

                <div class="activity-feed">
                    @foreach (var activity in Model.RecentActivities)
                    {
                        <div class="activity-item d-flex align-items-start mb-3">
                            <div class="activity-icon me-3">
                                <i class="fas fa-@(activity.Type == "Success" ? "check-circle text-success" : 
                                                   activity.Type == "Error" ? "times-circle text-danger" : 
                                                   activity.Type == "Warning" ? "exclamation-triangle text-warning" : 
                                                   "info-circle text-info")"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong class="d-block">@activity.Activity</strong>
                                        <small class="text-muted">@activity.Details</small>
                                    </div>
                                    <small class="text-muted">
                                        @activity.Timestamp.ToString("MMM dd, HH:mm")
                                    </small>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <div class="text-center mt-3">
                    <a href="#" class="btn btn-sm btn-outline-primary">View All Activity</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Quick Actions -->
    <div class="col-xl-4">
        <div class="admin-card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="triggerFullSync()">
                        <i class="fas fa-sync me-2"></i>
                        Sync All Sources
                    </button>
                    
                    <a href="/admin/sources/create" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        Add New Source
                    </a>
                    
                    <button class="btn btn-outline-warning" onclick="clearAllCaches()">
                        <i class="fas fa-broom me-2"></i>
                        Clear All Caches
                    </button>
                    
                    <a href="/admin/settings" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>
                        System Settings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Distribution -->
    <div class="col-xl-8">
        <div class="admin-card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-chart-pie me-2"></i>
                    Content Distribution by Category
                </h5>
                
                <div class="chart-container">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            initializeContentChart();
            initializeCategoryChart();
            
            // Set up auto-refresh
            setInterval(refreshDashboardData, 30000);
        });

        function initializeContentChart() {
            const ctx = document.getElementById('contentChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Articles Published',
                        data: [12, 19, 3, 5, 2, 3, 15],
                        borderColor: '#0d6efd',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#3a4049'
                            },
                            ticks: {
                                color: '#adb5bd'
                            }
                        },
                        x: {
                            grid: {
                                color: '#3a4049'
                            },
                            ticks: {
                                color: '#adb5bd'
                            }
                        }
                    }
                }
            });
        }

        function initializeCategoryChart() {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: @Html.Raw(Json.Serialize(Model.ContentMetrics.Select(m => m.CategoryName))),
                    datasets: [{
                        data: @Html.Raw(Json.Serialize(Model.ContentMetrics.Select(m => m.ArticleCount))),
                        backgroundColor: [
                            '#ff4444', '#ff0000', '#4CAF50', '#2196F3', '#9C27B0', '#FF9800'
                        ],
                        borderWidth: 2,
                        borderColor: '#262b36'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#adb5bd',
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    }
                }
            });
        }

        function refreshDashboardData() {
            fetch('/admin/dashboard/GetStats')
                .then(response => response.json())
                .then(data => {
                    if (!data.error) {
                        document.getElementById('total-articles').textContent = data.totalArticles.toLocaleString();
                        document.getElementById('active-sources').textContent = data.activeSources;
                        document.getElementById('failed-syncs').textContent = data.failedSyncs;
                        document.getElementById('users-count').textContent = data.usersCount.toLocaleString();
                    }
                })
                .catch(error => console.error('Error refreshing dashboard data:', error));
        }

        function syncSource(sourceId) {
            if (confirm('Are you sure you want to sync this source now?')) {
                fetch('/admin/dashboard/TriggerSync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                    },
                    body: JSON.stringify([sourceId])
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Sync triggered successfully');
                        setTimeout(refreshDashboardData, 2000);
                    } else {
                        alert('Failed to trigger sync: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error triggering sync: ' + error.message);
                });
            }
        }

        function triggerFullSync() {
            if (confirm('Are you sure you want to sync all active sources? This may take some time.')) {
                // Implementation for full sync
                alert('Full sync triggered for all active sources');
            }
        }

        function clearAllCaches() {
            if (confirm('Are you sure you want to clear all caches? This may temporarily impact performance.')) {
                fetch('/admin/settings/ClearCache', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('All caches cleared successfully');
                    } else {
                        alert('Failed to clear caches: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error clearing caches: ' + error.message);
                });
            }
        }
    </script>

    <style>
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-indicator.status-healthy {
            background-color: var(--bs-success);
        }
        
        .status-indicator.status-warning {
            background-color: var(--bs-warning);
        }
        
        .status-indicator.status-error {
            background-color: var(--bs-danger);
        }

        .activity-feed {
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-icon {
            width: 20px;
            text-align: center;
        }
    </style>
}