# AI Frontiers - Frontend API Specification
**Generated:** 2025-08-05  
**Page:** Category Pages (All 6 AI Categories)  
**Frontend Architect:** <PERSON> Code Assistant

## Overview

This specification defines the complete API requirements for AI Frontiers category pages. Each category page provides a tailored browsing experience while maintaining design consistency across all 6 AI categories.

## Categories Implemented

1. **Breaking AI News** (slug: `breaking-ai-news`, color: `#ff4444`, icon: `fas fa-bolt`)
2. **YouTube AI Discoveries** (slug: `youtube-ai-discoveries`, color: `#ff0000`, icon: `fab fa-youtube`)  
3. **Research & Papers** (slug: `research-papers`, color: `#4CAF50`, icon: `fas fa-flask`)
4. **Agentic Platforms** (slug: `agentic-platforms`, color: `#2196F3`, icon: `fas fa-robot`)
5. **AI Development Tools** (slug: `ai-development-tools`, color: `#9C27B0`, icon: `fas fa-tools`)
6. **Trending Open Source** (slug: `trending-open-source`, color: `#FF9800`, icon: `fab fa-github`)

## Required API Endpoints

### 1. GET /{slug} - Category Page Display
**Purpose:** Display category page with articles  
**Route:** `/{slug}` (e.g., `/breaking-ai-news`)  
**Controller:** `CategoryController.Index`

**Parameters:**
```csharp
string slug,                    // Category slug (required)
int page = 1,                  // Page number (default: 1)
int pageSize = 20,             // Items per page (default: 20, max: 100)
string sortBy = "PublishedDate", // Sort field (default: PublishedDate)
bool sortDesc = true,          // Sort direction (default: descending)
string viewMode = "grid",      // View mode: grid|list|compact
string searchTerm = "",        // Search within category
DateTime? fromDate = null,     // Filter from date
DateTime? toDate = null,       // Filter to date
bool featuredOnly = false,     // Show only featured articles
bool breakingOnly = false      // Show only breaking news
```

**Returns:** View with `CategoryPageViewModel`

**Validation Rules:**
- `slug`: Must match valid category slugs
- `page`: Minimum 1
- `pageSize`: Range 1-100
- `viewMode`: Must be "grid", "list", or "compact"
- `sortBy`: Must be valid sort field
- Date filters: `fromDate` must be <= `toDate`

### 2. GET /api/category/{slug}/articles - AJAX Article Loading
**Purpose:** Load articles via AJAX for filtering/pagination  
**Route:** `/api/category/{slug}/articles`  
**Controller:** `CategoryController.GetArticlesJson`

**Parameters:** Same as above

**Success Response:** 200 OK
```json
{
  "articles": [
    {
      "id": 1,
      "title": "Article Title",
      "slug": "article-slug",
      "summary": "Article summary text...",
      "imageUrl": "https://example.com/image.jpg",
      "imageAlt": "Image description",
      "author": "Author Name",
      "publishedDate": "2025-08-05T10:30:00Z",
      "publishedDateFormatted": "Aug 05, 2025",
      "publishedTimeFormatted": "10:30 AM",
      "isFeatured": true,
      "isBreaking": false,
      "isTrending": true,
      "categoryName": "Breaking AI News",
      "categorySlug": "breaking-ai-news",
      "categoryColor": "#ff4444",
      "categoryIcon": "fas fa-bolt",
      "sourceName": "AI Research Lab",
      "viewCount": 1250,
      "readingTimeMinutes": 5,
      "timeAgo": "2h ago"
    }
  ],
  "totalCount": 150,
  "page": 1,
  "pageSize": 20,
  "totalPages": 8,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

**Error Response:** 404 Not Found
```json
{
  "error": "Category not found"
}
```

### 3. GET /api/categories/{slug} - Category Details
**Purpose:** Get category information with statistics  
**Route:** `/api/categories/{slug}`  
**Controller:** `Api/CategoriesController.GetBySlug`

**Parameters:**
```csharp
string slug,                   // Category slug
bool includeStats = true       // Include article counts
```

**Success Response:** 200 OK
```json
{
  "id": 1,
  "name": "Breaking AI News",
  "slug": "breaking-ai-news",
  "description": "Latest breaking news in AI...",
  "color": "#ff4444",
  "icon": "fas fa-bolt",
  "articleCount": 150,
  "publishedArticleCount": 142,
  "isActive": true,
  "displayOrder": 1
}
```

### 4. GET /api/categories/navigation - Navigation Menu
**Purpose:** Get categories for navigation dropdown  
**Route:** `/api/categories/navigation`  
**Controller:** `Api/CategoriesController.GetNavigationCategories`

**Success Response:** 200 OK
```json
[
  {
    "id": 1,
    "name": "Breaking AI News",
    "slug": "breaking-ai-news",
    "color": "#ff4444",
    "icon": "fas fa-bolt",
    "displayOrder": 1
  }
]
```

## Database Requirements

### Categories Table Structure
```sql
CREATE TABLE Categories (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Slug NVARCHAR(50) NOT NULL UNIQUE,
    Description NVARCHAR(500) NULL,
    Color NVARCHAR(20) NULL,
    Icon NVARCHAR(50) NULL,
    DisplayOrder INT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ModifiedDate DATETIME2 NULL
);
```

### Required Seed Data
All 6 categories must exist with exact slugs, colors, and icons as specified above.

## Frontend Implementation Details

### View Model Structure
```csharp
public class CategoryPageViewModel
{
    public Category Category { get; set; }
    public int ArticleCount { get; set; }
    public int PublishedArticleCount { get; set; }
    public List<Article> Articles { get; set; }
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public string ViewMode { get; set; } = "grid";
    public string SortBy { get; set; } = "PublishedDate";
    public bool SortDesc { get; set; } = true;
    public string SearchTerm { get; set; } = "";
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public bool FeaturedOnly { get; set; }
    public bool BreakingOnly { get; set; }
    public bool HasNextPage { get; set; }
    public bool HasPreviousPage { get; set; }
}
```

### Routing Configuration
```csharp
// Custom route for categories (must come before default route)
app.MapControllerRoute(
    name: "category",
    pattern: "{slug}",
    defaults: new { controller = "Category", action = "Index" },
    constraints: new { slug = @"^(breaking-ai-news|youtube-ai-discoveries|research-papers|agentic-platforms|ai-development-tools|trending-open-source)$" });
```

### Category-Specific Features

#### Breaking AI News
- Real-time updates every 30 seconds
- Breaking news notification system
- Auto-refresh for new articles
- Alert badges for urgent news

#### YouTube AI Discoveries  
- Video thumbnail display
- Duration and channel information
- Play count metrics
- Creator verification badges

#### Research & Papers
- Author and journal information
- Abstract summaries
- Citation helpers
- PDF download links
- Impact factor display

#### Agentic Platforms
- Platform descriptions
- Feature highlight badges
- Integration capabilities
- Pricing tier indicators

#### AI Development Tools
- Tool category filtering
- GitHub statistics (stars, forks)
- Installation instructions
- Language/framework tags
- License information

#### Trending Open Source
- Repository statistics
- Recent activity indicators
- Contributor count
- Issue/PR counts
- Language breakdown

## Client-Side JavaScript Requirements

### Core Functionality
```javascript
// Category page initialization
function initializeCategoryPage()
// Search and filtering
function performSearch()
// Pagination
function changePage(page)
// View mode switching
function setViewMode(mode) 
// Sort direction toggle
function toggleSortDirection()
// Load more articles (infinite scroll)
function loadMoreArticles()
// Real-time updates (breaking news)
function checkForNewArticles()
```

### AJAX Integration
- Debounced search (500ms delay)
- Loading states and error handling
- URL state management
- Progressive enhancement
- Mobile-responsive behavior

## SEO and Meta Tags

### Per Category Page
```html
<title>{Category Name} - AI Frontiers</title>
<meta name="description" content="{Category Description}" />
<meta name="keywords" content="AI news, {category keywords}, artificial intelligence" />
<meta property="og:title" content="{Category Name} - AI Frontiers" />
<meta property="og:description" content="{Category Description}" />
<meta property="og:type" content="website" />
```

### Structured Data
```json
{
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  "name": "{Category Name}",
  "description": "{Category Description}",
  "url": "https://ai-frontiers.com/{slug}",
  "mainEntity": {
    "@type": "ItemList",
    "numberOfItems": "{article count}"
  }
}
```

## Performance Requirements

### Response Times
- Initial page load: < 2 seconds
- AJAX requests: < 500ms
- Search results: < 300ms
- Pagination: < 400ms

### Caching Strategy
- Category data: 30 minutes
- Article lists: 10 minutes
- Navigation data: 1 hour
- Static assets: 1 year

### Database Optimization
- Indexed on: CategoryId, PublishedDate, Status
- Pagination with OFFSET/FETCH
- Include necessary related data
- Avoid N+1 queries

## Error Handling

### Client-Side
- Network error fallbacks
- Graceful degradation
- User-friendly error messages
- Retry mechanisms

### Server-Side
- Detailed logging
- Structured error responses
- HTTP status codes
- Exception handling

## Security Considerations

### Input Validation
- XSS prevention
- SQL injection protection
- Parameter validation
- Rate limiting

### Content Security
- Image URL validation
- Content sanitization
- CORS configuration
- HTTPS enforcement

## Accessibility Requirements

### WCAG 2.1 AA Compliance
- Keyboard navigation
- Screen reader support
- Color contrast ratios
- Focus indicators
- ARIA labels

### Implementation
```html
<nav aria-label="Category articles pagination">
<button aria-label="Switch to grid view">
<section aria-labelledby="category-heading">
```

## Browser Support

### Required Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers

### Progressive Enhancement
- Works without JavaScript
- Enhanced with JavaScript
- Responsive design
- Touch-friendly interface

## Testing Requirements

### Unit Tests
- Controller actions
- Service methods
- Data validation
- Error handling

### Integration Tests
- API endpoints
- Database queries
- Search functionality
- Pagination logic

### UI Tests
- Responsive design
- Cross-browser compatibility
- Accessibility compliance
- Performance benchmarks

## Deployment Considerations

### Environment Variables
```
ASPNETCORE_ENVIRONMENT=Production
ConnectionStrings__DefaultConnection={connection_string}
```

### Health Checks
- Database connectivity
- External API availability
- Cache performance
- Memory usage

## Success Criteria

### Functional Requirements
✅ All 6 category pages working  
✅ Search and filtering functional  
✅ Pagination working correctly  
✅ View modes implemented  
✅ Category-specific features active  
✅ Mobile responsive design  
✅ SEO optimized  

### Performance Requirements
✅ Page load time < 2 seconds  
✅ AJAX responses < 500ms  
✅ 95%+ uptime  
✅ Accessibility compliant  

### Integration Requirements
✅ API endpoints documented  
✅ Database schema defined  
✅ Error handling implemented  
✅ Caching strategy active  

---

**Note:** This specification is comprehensive and ready for backend implementation. All frontend components are designed to work with the defined API structure. The implementation prioritizes user experience, performance, and maintainability.