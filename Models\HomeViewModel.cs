namespace NewsSite.Models
{
    public class HomeViewModel
    {
        public IEnumerable<CategoryDto> Categories { get; set; } = new List<CategoryDto>();
        public IEnumerable<ArticleDto> FeaturedArticles { get; set; } = new List<ArticleDto>();
        public IEnumerable<ArticleDto> LatestArticles { get; set; } = new List<ArticleDto>();
        public IEnumerable<ArticleDto> TrendingArticles { get; set; } = new List<ArticleDto>();
    }

    public class ArticleDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public string? ImageUrl { get; set; }
        public string? ImageAlt { get; set; }
        public string? Author { get; set; }
        public DateTime PublishedDate { get; set; }
        public bool IsFeatured { get; set; }
        public bool IsBreaking { get; set; }
        public bool IsTrending { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public string CategorySlug { get; set; } = string.Empty;
        public string? CategoryColor { get; set; }
        public string? CategoryIcon { get; set; }
        public string SourceName { get; set; } = string.Empty;
        public int ViewCount { get; set; }
        public int ReadingTimeMinutes { get; set; }
        public string TimeAgo { get; set; } = string.Empty;
    }

    public class CategoryDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Color { get; set; }
        public string? Icon { get; set; }
        public int ArticleCount { get; set; }
        public int PublishedArticleCount { get; set; }
        public bool IsActive { get; set; }
        public int DisplayOrder { get; set; }
    }

    public class PaginatedArticleResult
    {
        public IEnumerable<ArticleDto> Articles { get; set; } = new List<ArticleDto>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => Page > 1;
        public bool HasNextPage => Page < TotalPages;
    }
}