{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\NewsSite\\NewsSite.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\NewsSite\\NewsSite.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\NewsSite.csproj", "projectName": "NewsSite", "projectPath": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\NewsSite.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Telerik Files 2025": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Hangfire.AspNetCore": {"target": "Package", "version": "[1.8.20, )"}, "Hangfire.SqlServer": {"target": "Package", "version": "[1.8.20, )"}, "HtmlAgilityPack": {"target": "Package", "version": "[1.12.2, )"}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.8, )"}, "System.ServiceModel.Syndication": {"target": "Package", "version": "[9.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}