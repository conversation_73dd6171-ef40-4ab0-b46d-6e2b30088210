using NewsSite.Models;

namespace NewsSite.Areas.Admin.Models
{
    public class AdminDashboardViewModel
    {
        public DashboardStats Stats { get; set; } = new();
        public List<SourceHealthStatus> SourceHealth { get; set; } = new();
        public List<RecentActivity> RecentActivities { get; set; } = new();
        public SystemHealth SystemHealth { get; set; } = new();
        public List<ContentMetrics> ContentMetrics { get; set; } = new();
    }

    public class DashboardStats
    {
        public int TotalArticles { get; set; }
        public int TotalSources { get; set; }
        public int ActiveSources { get; set; }
        public int FailedSyncs { get; set; }
        public int ArticlesToday { get; set; }
        public int ArticlesThisWeek { get; set; }
        public int ArticlesThisMonth { get; set; }
        public int TotalCategories { get; set; }
        public int TotalTags { get; set; }
        public int UsersCount { get; set; }
        public DateTime LastSync { get; set; }
    }

    public class SourceHealthStatus
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public SourceType Type { get; set; }
        public string Status { get; set; } = string.Empty; // "Healthy", "Warning", "Error"
        public DateTime? LastSync { get; set; }
        public DateTime? LastSuccessfulSync { get; set; }
        public int ConsecutiveFailures { get; set; }
        public string? LastError { get; set; }
        public int ArticlesThisWeek { get; set; }
        public bool IsActive { get; set; }
    }

    public class RecentActivity
    {
        public DateTime Timestamp { get; set; }
        public string Activity { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // "Info", "Success", "Warning", "Error"
        public string? UserId { get; set; }
        public string? UserName { get; set; }
    }

    public class SystemHealth
    {
        public bool IsHealthy { get; set; }
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public long DatabaseSize { get; set; }
        public int ActiveJobs { get; set; }
        public int FailedJobs { get; set; }
        public DateTime LastHealthCheck { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    public class ContentMetrics
    {
        public string CategoryName { get; set; } = string.Empty;
        public int ArticleCount { get; set; }
        public int ViewCount { get; set; }
        public double EngagementRate { get; set; }
        public DateTime Date { get; set; }
    }
}