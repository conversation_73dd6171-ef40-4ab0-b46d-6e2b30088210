{"GlobalPropertiesHash": "Ut3cmtI7jLfKcRv2iltWIKDPQDZHGHIIwD4roUTe2FQ=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["IaqQaS9VN7k4f64iYMAYkWmCKUfPt1RiwQQqCaLYnEA=", "z4w1iHmXnbNtey17m92QvTaCzrbCf65BMiJaqjFxjxU=", "OrEenUDKJejD5u1CRh34So92MatQdXZdzJW0iPL3RY0=", "c4PQqoOhl2Fh4HvYv3H2SV4n1cFWTO9CZcvqU+t1Z7Y=", "Ct3x9ePyJCKz3yz5gBri0o2TF1J7/CPQj1RUg0UQi5s=", "jhTQ7xRmBiJyfi7qltyKFgrg9aVOYblwKbaXp9wE57w=", "2U9ku+Q9VsUWx4/GK6qwmQCjtp3vyM6dAmUgaPIiZnc=", "d9xv754LFm6YeTYVUkhJ+i3bTAf25vTmfT2J3MVq47g=", "hLIhyccp1U4gPhG+7ZSRZQKJ6U6Y3pidxJmVi3LVGjw=", "WwI/aGsiI40fyhUhvoA+pzHs0vSNFtHo8xgETkOIqAg=", "d901mtNmm4T/SScsJYGWEOquZpIhDdcp5q255oI02ng=", "+KxgB0iKaU3LXUkkJQib72tvgqrPvZ1R6gedDhcqw/Y=", "g6XLAiDZIRmIjVsMqbZ3G97etNcgb4iXuVMWxk16Pgs=", "hmeWXelHHqH9o5c3/lwZ1ySK3GXfg8vAXn8+qQctuCk=", "SZjdyCosLAmKiywOCWkQ1A8uTxIcBWmX+ALwEZTGJSA=", "8qcmcYSpDNeIVmyXyZhsciHzCkswrJaYc2XuJWYPgMc=", "87C69H+Cp3R4EeW5Kw1Y/VYNe8CckXZ3kfr0hUJqdkE=", "uBeCwXzLu/wKDzc2guYE7wByYyZg416Os44rS5rnFvY=", "kzSsnlAYBK8SGNVT1+MNEE02BtgOXI4vA79tRjFJVOo=", "MeyNSNQm8eXVg9S8fZkC6TiBqpaTW/zbkn6AFIwD5Xw=", "J8Zr0TdESiZWH4awvUihEauGKJV17AGDcXgbrEDC3+E=", "Bcz8aNM+BiU0O9CiR8Let0bsENc+hGIY/p7mRR55wlc=", "IwQqBNvtbPUr3MznVFG5GJTXHjS11UGiiEclCgnfKq4=", "hRasy8ePkQo7s3agAihgO4WcReT0I9yT9O/+Xy64DWM=", "6GJHot1oapWYl1ZYLFwmPvbGlsfUjHC8w9EO+J+VyOQ=", "saON5Mq/wtfO/i+H4g3QlwBH6gD0PTEPxcTGQwsC8jw=", "5OMstaICMls+P9jDkHIMvdMG0VMW5+sj4whP2hPXK/E=", "jT19KxpXB7V29u6T/vrzDi0PYZuE8mIqBuJxZEW66H4=", "jEhYymWiS6/XIOSQSbT06XBQ0GpAPO69crrsc5tW9m8=", "8nhXfSFndbz2caxfmFt4euCCsCcFWfD0d7LFLEU4z/0=", "DMrii2wH0tk0WB/gJHXwPCbRRD0ZE5XsZEBOs8kZDTY=", "prgy7YGvUPqAuzzvLY2LegmT4RcNw74yxojpSfxqX38=", "6ZoiN/ClH6Nkuonp8JnSb+Dpk4VzABo1t+2XZEtJ/sE=", "AUu7r3UTXiqvQoMjtpiVFqG7dQ/KyarjO+dyqIkYeZI=", "L7YIr8AybQyDxh1U9bhZ3lD4gns0ZykhithMYm74QdI=", "zlUP+IOYBN27boVQRUi5HzgzAfOUPGa+BA/6YGEMx08=", "N9QyuKwjUIAzdGSSQiVtnUpd86UW4g1BB8qMx604T+U=", "DueAGo+DeK8KjRMvBCMNc+/7zzUfnyLyAbUeZJaQ9dw=", "p14XMH1lnrvsSmja+HI/uz49hMIdH7klI+tJSG7uXnQ=", "Cpc92UubIfC9ENF19/CZv9zsz5s6HwRL2ld+akR+fr4=", "1lpGHfwwOS28h7Tr7ePAk51Ie1CLTQ7e4S5UVgzMDJY=", "ry1JSWZgzHZUm5JY7swH39Rmn1TB9aQR78LAjr/eV8A=", "JoO3S1lXS9Mxm1VXX74z4f8BH2/duVbs6yhaWTLeeWI=", "MrKBNyVt01H/dGKYxSmUnmBIYIRL42Pc2/hfBZ3RmFY=", "70OqrF9o8sLiahoWV2VuAAwXzRpRlQw1yFhMt/pE7dA=", "oN810j8WQKC3breG22M8sAmddFsR2OFHyKSOXAm6DYg=", "Jv7z3uml2v5A87lS6zCwqsvdYazjbA/4ZEhYCQh7/P4=", "Ku9Zva6D3ABwkIcKz34pOWwpRzpmCTUcurENIlJJ5M0=", "WUKOdK1dTHNwhec9nK30Ppw8b93DeiF6EOcQNdTy0eI=", "1wfWWzLa/Lu3nSumOvyAMoufYJ8JglUzAOflDjEJAEY=", "rF2XuF8A26PcBbuwIYiKQiA4etJGyEbr0sX+ZDeqvsI=", "W/mwtGSkBs3LXiSp6/1pdhDuJOlXLnz87MNrVMOxhU8=", "zyVWPdA4C9Yd3dX1YPOF2Eyps+f+is/lNOYyv/B105A=", "Fl3Ltg8gA857mz8wVSSe+uAckcV48crLM+vk6bFXc+A=", "6fquagxqc+rqocg4o5k6KV4Gjy2ZCRp7PS5r1j7BNZU=", "d1s64oifHv3blmmfm+9tHx//E1p8HMCBfmqZUCieaow=", "xKVYAMP8NB0PR18sogpvBifrV67znYroyXYsDqriu4I=", "dNro9/3hRXdsEuLR19UA4SheD9U1ujCFumEy+md43go=", "p9xznHKKZL0bL2afyp8GU1WLQEvSBvYWi9MV+28TdoU=", "6VUBRvru0S0xocW4hb9FMTjf8UPw9kXIhP0bmgMF2Lk=", "1KoH6uNASZYaX1hw2+58rtJnd3s2cXiwTHiUJFvXuG0=", "KSolfLhMwhrg1eCZ6C4xmdTO2xAq8/AUX0aU3gaveYU=", "HED3xzz0HHhegT0wHi3bTFKPKoFImNoL9lrT53HHapY=", "FzXZnSEHlgO9463+bq7bZgzvc53gC8/cem4nfahbsu0=", "Fbq0nl5hmhnVPvOYNIVdTeCGQDuCC42JbYkxPOngeAo=", "GaIv1r8J15j+fBZ8uaXpWujWpv7+VK4ybZfY7RR+EhY=", "WQbgUqcyvWprLZc6gjMnEc6TCc9+Axt46uUtM0daKcg=", "oZj8d2QEbW5LcNi9KVATyneU6I/ZYzWEQmoYZqamZ1E=", "U15HvH3qIZgqNDhyM78Z6cfy0EB+BLg56HTSQS6RLS4=", "EWN7QY2y9zuneMHihROfI538LJCCRHtMmMWlbQpESSc=", "w/MtB469YnEFIh7cGFUqqnhI6EkBxR3wU3NLTvrUiJg="], "CachedAssets": {"6VUBRvru0S0xocW4hb9FMTjf8UPw9kXIhP0bmgMF2Lk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-08-05T01:45:05.4945541+00:00"}, "p9xznHKKZL0bL2afyp8GU1WLQEvSBvYWi9MV+28TdoU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-08-05T01:45:05.3550481+00:00"}, "dNro9/3hRXdsEuLR19UA4SheD9U1ujCFumEy+md43go=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-08-05T01:45:05.3540469+00:00"}, "xKVYAMP8NB0PR18sogpvBifrV67znYroyXYsDqriu4I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-08-05T01:45:05.3530476+00:00"}, "d1s64oifHv3blmmfm+9tHx//E1p8HMCBfmqZUCieaow=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-08-05T01:45:05.4955539+00:00"}, "6fquagxqc+rqocg4o5k6KV4Gjy2ZCRp7PS5r1j7BNZU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-08-05T01:45:05.3584311+00:00"}, "Fl3Ltg8gA857mz8wVSSe+uAckcV48crLM+vk6bFXc+A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-08-05T01:45:05.357422+00:00"}, "zyVWPdA4C9Yd3dX1YPOF2Eyps+f+is/lNOYyv/B105A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-08-05T01:45:05.3560466+00:00"}, "W/mwtGSkBs3LXiSp6/1pdhDuJOlXLnz87MNrVMOxhU8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-08-05T01:45:05.3560466+00:00"}, "rF2XuF8A26PcBbuwIYiKQiA4etJGyEbr0sX+ZDeqvsI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-08-05T01:45:05.4971338+00:00"}, "1wfWWzLa/Lu3nSumOvyAMoufYJ8JglUzAOflDjEJAEY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-08-05T01:45:05.4961282+00:00"}, "WUKOdK1dTHNwhec9nK30Ppw8b93DeiF6EOcQNdTy0eI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-08-05T01:45:05.4961282+00:00"}, "Ku9Zva6D3ABwkIcKz34pOWwpRzpmCTUcurENIlJJ5M0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-08-05T01:45:05.493554+00:00"}, "Jv7z3uml2v5A87lS6zCwqsvdYazjbA/4ZEhYCQh7/P4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-08-05T01:45:05.4843421+00:00"}, "oN810j8WQKC3breG22M8sAmddFsR2OFHyKSOXAm6DYg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-08-05T01:45:05.4833425+00:00"}, "70OqrF9o8sLiahoWV2VuAAwXzRpRlQw1yFhMt/pE7dA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-08-05T01:45:05.4833425+00:00"}, "MrKBNyVt01H/dGKYxSmUnmBIYIRL42Pc2/hfBZ3RmFY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-08-05T01:45:05.4823425+00:00"}, "JoO3S1lXS9Mxm1VXX74z4f8BH2/duVbs6yhaWTLeeWI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-08-05T01:45:05.4813434+00:00"}, "ry1JSWZgzHZUm5JY7swH39Rmn1TB9aQR78LAjr/eV8A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-08-05T01:45:05.4803426+00:00"}, "1lpGHfwwOS28h7Tr7ePAk51Ie1CLTQ7e4S5UVgzMDJY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-08-05T01:45:05.479342+00:00"}, "Cpc92UubIfC9ENF19/CZv9zsz5s6HwRL2ld+akR+fr4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-08-05T01:45:05.4783419+00:00"}, "p14XMH1lnrvsSmja+HI/uz49hMIdH7klI+tJSG7uXnQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-08-05T01:45:05.4773427+00:00"}, "DueAGo+DeK8KjRMvBCMNc+/7zzUfnyLyAbUeZJaQ9dw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-08-05T01:45:05.4763368+00:00"}, "N9QyuKwjUIAzdGSSQiVtnUpd86UW4g1BB8qMx604T+U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-08-05T01:45:05.4756326+00:00"}, "zlUP+IOYBN27boVQRUi5HzgzAfOUPGa+BA/6YGEMx08=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-08-05T01:45:05.4736319+00:00"}, "L7YIr8AybQyDxh1U9bhZ3lD4gns0ZykhithMYm74QdI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-08-05T01:45:05.4726327+00:00"}, "AUu7r3UTXiqvQoMjtpiVFqG7dQ/KyarjO+dyqIkYeZI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-08-05T01:45:05.4716325+00:00"}, "6ZoiN/ClH6Nkuonp8JnSb+Dpk4VzABo1t+2XZEtJ/sE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-08-05T01:45:05.4706328+00:00"}, "prgy7YGvUPqAuzzvLY2LegmT4RcNw74yxojpSfxqX38=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-08-05T01:45:05.4686324+00:00"}, "DMrii2wH0tk0WB/gJHXwPCbRRD0ZE5XsZEBOs8kZDTY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-08-05T01:45:05.4686324+00:00"}, "8nhXfSFndbz2caxfmFt4euCCsCcFWfD0d7LFLEU4z/0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-08-05T01:45:05.4666321+00:00"}, "jEhYymWiS6/XIOSQSbT06XBQ0GpAPO69crrsc5tW9m8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-08-05T01:45:05.4656324+00:00"}, "jT19KxpXB7V29u6T/vrzDi0PYZuE8mIqBuJxZEW66H4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-08-05T01:45:05.4646328+00:00"}, "5OMstaICMls+P9jDkHIMvdMG0VMW5+sj4whP2hPXK/E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-08-05T01:45:05.4636327+00:00"}, "saON5Mq/wtfO/i+H4g3QlwBH6gD0PTEPxcTGQwsC8jw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-08-05T01:45:05.4626332+00:00"}, "6GJHot1oapWYl1ZYLFwmPvbGlsfUjHC8w9EO+J+VyOQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-08-05T01:45:05.4626332+00:00"}, "hRasy8ePkQo7s3agAihgO4WcReT0I9yT9O/+Xy64DWM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-08-05T01:45:05.4616326+00:00"}, "IwQqBNvtbPUr3MznVFG5GJTXHjS11UGiiEclCgnfKq4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-08-05T01:45:05.4606329+00:00"}, "Bcz8aNM+BiU0O9CiR8Let0bsENc+hGIY/p7mRR55wlc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-08-05T01:45:05.4606329+00:00"}, "J8Zr0TdESiZWH4awvUihEauGKJV17AGDcXgbrEDC3+E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-08-05T01:45:05.4596324+00:00"}, "MeyNSNQm8eXVg9S8fZkC6TiBqpaTW/zbkn6AFIwD5Xw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-08-05T01:45:05.4586332+00:00"}, "kzSsnlAYBK8SGNVT1+MNEE02BtgOXI4vA79tRjFJVOo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-08-05T01:45:05.4586332+00:00"}, "uBeCwXzLu/wKDzc2guYE7wByYyZg416Os44rS5rnFvY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-08-05T01:45:05.4576331+00:00"}, "87C69H+Cp3R4EeW5Kw1Y/VYNe8CckXZ3kfr0hUJqdkE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-08-05T01:45:05.4576331+00:00"}, "8qcmcYSpDNeIVmyXyZhsciHzCkswrJaYc2XuJWYPgMc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-08-05T01:45:05.4566328+00:00"}, "SZjdyCosLAmKiywOCWkQ1A8uTxIcBWmX+ALwEZTGJSA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-08-05T01:45:05.455633+00:00"}, "hmeWXelHHqH9o5c3/lwZ1ySK3GXfg8vAXn8+qQctuCk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-08-05T01:45:05.455633+00:00"}, "g6XLAiDZIRmIjVsMqbZ3G97etNcgb4iXuVMWxk16Pgs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-08-05T01:45:05.4546327+00:00"}, "+KxgB0iKaU3LXUkkJQib72tvgqrPvZ1R6gedDhcqw/Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-08-05T01:45:05.4546327+00:00"}, "d901mtNmm4T/SScsJYGWEOquZpIhDdcp5q255oI02ng=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-08-05T01:45:05.453633+00:00"}, "WwI/aGsiI40fyhUhvoA+pzHs0vSNFtHo8xgETkOIqAg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-08-05T01:45:05.4526332+00:00"}, "hLIhyccp1U4gPhG+7ZSRZQKJ6U6Y3pidxJmVi3LVGjw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-08-05T01:45:05.4526332+00:00"}, "d9xv754LFm6YeTYVUkhJ+i3bTAf25vTmfT2J3MVq47g=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-08-05T01:45:05.4516333+00:00"}, "2U9ku+Q9VsUWx4/GK6qwmQCjtp3vyM6dAmUgaPIiZnc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-08-05T01:45:05.4506323+00:00"}, "jhTQ7xRmBiJyfi7qltyKFgrg9aVOYblwKbaXp9wE57w=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-08-05T01:45:05.4506323+00:00"}, "Ct3x9ePyJCKz3yz5gBri0o2TF1J7/CPQj1RUg0UQi5s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-08-05T01:45:05.4496331+00:00"}, "c4PQqoOhl2Fh4HvYv3H2SV4n1cFWTO9CZcvqU+t1Z7Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-08-05T01:45:05.4486329+00:00"}, "OrEenUDKJejD5u1CRh34So92MatQdXZdzJW0iPL3RY0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\js\\site.js", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-08-05T01:45:05.3510474+00:00"}, "z4w1iHmXnbNtey17m92QvTaCzrbCf65BMiJaqjFxjxU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\favicon.ico", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-08-05T01:45:05.4945541+00:00"}, "IaqQaS9VN7k4f64iYMAYkWmCKUfPt1RiwQQqCaLYnEA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\css\\site.css", "SourceId": "NewsSite", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\NewsSite\\wwwroot\\", "BasePath": "_content/NewsSite", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2tiyv64ts", "Integrity": "pAGv4ietcJNk/EwsQZ5BN9+K4MuNYS2a9wl4Jw+q9D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 362, "LastWriteTime": "2025-08-05T01:45:05.3500475+00:00"}}, "CachedCopyCandidates": {}}