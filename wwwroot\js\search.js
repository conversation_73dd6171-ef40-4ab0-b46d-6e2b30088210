/**
 * AI Frontiers Search Functionality
 * Provides autocomplete, filtering, and real-time search capabilities
 */

// Main Search namespace
window.Search = (function() {
    'use strict';

    let config = {
        searchInput: '#main-search',
        suggestionsContainer: '#search-suggestions',
        apiBaseUrl: '/api/',
        debounceDelay: 300,
        minQueryLength: 2,
        maxSuggestions: 8
    };

    let debounceTimer = null;
    let currentRequest = null;

    /**
     * Initialize search functionality
     */
    function init(options) {
        config = Object.assign(config, options || {});
        bindEvents();
        initializeNavbarSearch();
    }

    /**
     * Bind search event handlers
     */
    function bindEvents() {
        // Main search input
        $(document).on('input', config.searchInput, handleSearchInput);
        $(document).on('keydown', config.searchInput, handleSearchKeydown);
        $(document).on('focus', config.searchInput, handleSearchFocus);
        $(document).on('blur', config.searchInput, handleSearchBlur);

        // Suggestion interactions
        $(document).on('click', '.search-suggestion-item', handleSuggestionClick);
        $(document).on('mouseenter', '.search-suggestion-item', handleSuggestionHover);

        // Clear search
        $(document).on('click', '.search-clear', clearSearch);

        // Close suggestions when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.search-input-wrapper').length) {
                hideSuggestions();
            }
        });
    }

    /**
     * Initialize navbar search functionality
     */
    function initializeNavbarSearch() {
        const navbarSearch = $('#navbar-search');
        const navbarSuggestions = $('#navbar-search-suggestions');

        navbarSearch.on('input', function() {
            handleSearchInput.call(this, null, navbarSuggestions);
        });

        navbarSearch.on('keydown', function(e) {
            handleSearchKeydown.call(this, e, navbarSuggestions);
        });

        navbarSearch.on('focus', function() {
            handleSearchFocus.call(this, null, navbarSuggestions);
        });

        navbarSearch.on('blur', function() {
            setTimeout(() => navbarSuggestions.addClass('d-none'), 150);
        });
    }

    /**
     * Handle search input changes with debouncing
     */
    function handleSearchInput(event, customSuggestions) {
        const query = $(this).val().trim();
        const suggestionsContainer = customSuggestions || $(config.suggestionsContainer);

        // Clear previous debounce timer
        if (debounceTimer) {
            clearTimeout(debounceTimer);
        }

        // Cancel previous request
        if (currentRequest) {
            currentRequest.abort();
            currentRequest = null;
        }

        if (query.length < config.minQueryLength) {
            hideSuggestions(suggestionsContainer);
            return;
        }

        // Debounce the search
        debounceTimer = setTimeout(() => {
            fetchSuggestions(query, suggestionsContainer);
        }, config.debounceDelay);
    }

    /**
     * Handle keyboard navigation in search
     */
    function handleSearchKeydown(event, customSuggestions) {
        const suggestionsContainer = customSuggestions || $(config.suggestionsContainer);
        const suggestions = suggestionsContainer.find('.search-suggestion-item');
        const activeSuggestion = suggestions.filter('.active');

        switch (event.keyCode) {
            case 13: // Enter
                event.preventDefault();
                if (activeSuggestion.length) {
                    activeSuggestion.click();
                } else {
                    // Submit the form
                    $(this).closest('form').submit();
                }
                break;

            case 27: // Escape
                hideSuggestions(suggestionsContainer);
                $(this).blur();
                break;

            case 38: // Up arrow
                event.preventDefault();
                if (activeSuggestion.length) {
                    const prev = activeSuggestion.prev('.search-suggestion-item');
                    if (prev.length) {
                        activeSuggestion.removeClass('active');
                        prev.addClass('active');
                    }
                } else if (suggestions.length) {
                    suggestions.last().addClass('active');
                }
                break;

            case 40: // Down arrow
                event.preventDefault();
                if (activeSuggestion.length) {
                    const next = activeSuggestion.next('.search-suggestion-item');
                    if (next.length) {
                        activeSuggestion.removeClass('active');
                        next.addClass('active');
                    }
                } else if (suggestions.length) {
                    suggestions.first().addClass('active');
                }
                break;
        }
    }

    /**
     * Handle search input focus
     */
    function handleSearchFocus(event, customSuggestions) {
        const query = $(this).val().trim();
        const suggestionsContainer = customSuggestions || $(config.suggestionsContainer);

        if (query.length >= config.minQueryLength) {
            fetchSuggestions(query, suggestionsContainer);
        } else {
            showRecentSearches(suggestionsContainer);
        }
    }

    /**
     * Handle search input blur
     */
    function handleSearchBlur(event, customSuggestions) {
        const suggestionsContainer = customSuggestions || $(config.suggestionsContainer);
        // Delay hiding to allow for suggestion clicks
        setTimeout(() => {
            hideSuggestions(suggestionsContainer);
        }, 150);
    }

    /**
     * Handle suggestion item clicks
     */
    function handleSuggestionClick(event) {
        event.preventDefault();
        const suggestion = $(this);
        const query = suggestion.data('query') || suggestion.text().trim();
        const type = suggestion.data('type');
        const url = suggestion.data('url');

        // Update search input
        const searchInput = suggestion.closest('.search-input-wrapper').find('input');
        searchInput.val(query);

        // Navigate to URL or submit search
        if (url) {
            window.location.href = url;
        } else {
            searchInput.closest('form').submit();
        }

        // Hide suggestions
        hideSuggestions();
    }

    /**
     * Handle suggestion hover
     */
    function handleSuggestionHover() {
        $(this).siblings('.search-suggestion-item').removeClass('active');
        $(this).addClass('active');
    }

    /**
     * Fetch search suggestions from API
     */
    function fetchSuggestions(query, suggestionsContainer) {
        const url = `${config.apiBaseUrl}search/suggestions?q=${encodeURIComponent(query)}&limit=${config.maxSuggestions}`;

        // Show loading state
        showLoadingSuggestions(suggestionsContainer);

        currentRequest = $.ajax({
            url: url,
            method: 'GET',
            timeout: 5000
        })
        .done(function(data) {
            displaySuggestions(data.suggestions || [], query, suggestionsContainer);
        })
        .fail(function(xhr) {
            if (xhr.statusText !== 'abort') {
                console.error('Failed to fetch suggestions:', xhr);
                hideSuggestions(suggestionsContainer);
            }
        })
        .always(function() {
            currentRequest = null;
        });
    }

    /**
     * Display search suggestions
     */
    function displaySuggestions(suggestions, query, suggestionsContainer) {
        if (!suggestions || suggestions.length === 0) {
            showNoSuggestions(query, suggestionsContainer);
            return;
        }

        const html = suggestions.map(suggestion => {
            const icon = getSuggestionIcon(suggestion.type);
            const count = suggestion.count > 0 ? `<span class="suggestion-count">${formatNumber(suggestion.count)}</span>` : '';
            
            return `
                <div class="search-suggestion-item" 
                     data-query="${escapeHtml(suggestion.text)}" 
                     data-type="${suggestion.type}">
                    <div class="suggestion-content">
                        <i class="${icon}"></i>
                        <span class="suggestion-text">${highlightQuery(suggestion.text, query)}</span>
                        ${count}
                    </div>
                    <div class="suggestion-type">${formatSuggestionType(suggestion.type)}</div>
                </div>
            `;
        }).join('');

        suggestionsContainer.html(html).removeClass('d-none');
    }

    /**
     * Show loading state for suggestions
     */
    function showLoadingSuggestions(suggestionsContainer) {
        const html = `
            <div class="search-suggestion-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Searching...</span>
            </div>
        `;
        suggestionsContainer.html(html).removeClass('d-none');
    }

    /**
     * Show no suggestions message
     */
    function showNoSuggestions(query, suggestionsContainer) {
        const html = `
            <div class="search-suggestion-empty">
                <i class="fas fa-search"></i>
                <span>Press Enter to search for "${escapeHtml(query)}"</span>
            </div>
        `;
        suggestionsContainer.html(html).removeClass('d-none');
    }

    /**
     * Show recent searches
     */
    function showRecentSearches(suggestionsContainer) {
        const recentSearches = getRecentSearches();
        if (recentSearches.length === 0) return;

        const html = recentSearches.map(search => `
            <div class="search-suggestion-item recent-search" data-query="${escapeHtml(search)}">
                <div class="suggestion-content">
                    <i class="fas fa-history"></i>
                    <span class="suggestion-text">${escapeHtml(search)}</span>
                </div>
                <div class="suggestion-type">Recent</div>
            </div>
        `).join('');

        suggestionsContainer.html(html).removeClass('d-none');
    }

    /**
     * Hide suggestions
     */
    function hideSuggestions(suggestionsContainer) {
        const container = suggestionsContainer || $(config.suggestionsContainer);
        container.addClass('d-none');
    }

    /**
     * Clear search input
     */
    function clearSearch() {
        const searchInput = $(this).siblings('input');
        searchInput.val('').focus();
        hideSuggestions();
    }

    /**
     * Get icon for suggestion type
     */
    function getSuggestionIcon(type) {
        const icons = {
            'tag': 'fas fa-tag',
            'category': 'fas fa-folder',
            'source': 'fas fa-rss',
            'author': 'fas fa-user',
            'query': 'fas fa-search',
            'popular': 'fas fa-fire',
            'trending': 'fas fa-trending-up',
            'related': 'fas fa-link'
        };
        return icons[type] || 'fas fa-search';
    }

    /**
     * Format suggestion type for display
     */
    function formatSuggestionType(type) {
        const types = {
            'tag': 'Tag',
            'category': 'Category',
            'source': 'Source',
            'author': 'Author',
            'query': 'Search',
            'popular': 'Popular',
            'trending': 'Trending',
            'related': 'Related'
        };
        return types[type] || 'Search';
    }

    /**
     * Highlight query in suggestion text
     */
    function highlightQuery(text, query) {
        if (!query) return escapeHtml(text);
        
        const escapedText = escapeHtml(text);
        const escapedQuery = escapeHtml(query);
        const regex = new RegExp(`(${escapedQuery})`, 'gi');
        
        return escapedText.replace(regex, '<mark>$1</mark>');
    }

    /**
     * Format number for display
     */
    function formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    /**
     * Get recent searches from localStorage
     */
    function getRecentSearches() {
        try {
            const recent = localStorage.getItem('ai_frontiers_recent_searches');
            return recent ? JSON.parse(recent) : [];
        } catch (e) {
            return [];
        }
    }

    /**
     * Save search to recent searches
     */
    function saveRecentSearch(query) {
        if (!query || query.length < 2) return;

        try {
            let recent = getRecentSearches();
            
            // Remove if already exists
            recent = recent.filter(search => search.toLowerCase() !== query.toLowerCase());
            
            // Add to front
            recent.unshift(query);
            
            // Keep only last 10
            recent = recent.slice(0, 10);
            
            localStorage.setItem('ai_frontiers_recent_searches', JSON.stringify(recent));
        } catch (e) {
            console.warn('Failed to save recent search:', e);
        }
    }

    /**
     * Escape HTML to prevent XSS
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public API
    return {
        init: init,
        fetchSuggestions: fetchSuggestions,
        saveRecentSearch: saveRecentSearch
    };
})();

// Search Results functionality
window.SearchResults = (function() {
    'use strict';

    let config = {
        apiBaseUrl: '/api/',
        currentQuery: ''
    };

    /**
     * Initialize search results functionality
     */
    function init(options) {
        config = Object.assign(config, options || {});
        bindEvents();
        initializeFilters();
        initializeViewToggle();
    }

    /**
     * Bind event handlers
     */
    function bindEvents() {
        // Sort options
        $('input[name="sort"]').on('change', handleSortChange);
        
        // Filter checkboxes
        $('input[name="categories"], input[name="sources"]').on('change', handleFilterChange);
        
        // Show more filters
        $('.show-more-filters').on('click', handleShowMoreFilters);
        
        // View toggle
        $('.view-btn').on('click', handleViewToggle);

        // Load more results (infinite scroll)
        $(window).on('scroll', handleScroll);
    }

    /**
     * Initialize filters
     */
    function initializeFilters() {
        // Restore filter state from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        
        // Restore sort selection
        const sort = urlParams.get('sort');
        if (sort) {
            $(`input[name="sort"][value="${sort}"]`).prop('checked', true);
        }
        
        // Restore category filters
        const categories = urlParams.getAll('categories');
        categories.forEach(category => {
            $(`input[name="categories"][value="${category}"]`).prop('checked', true);
        });
        
        // Restore source filters
        const sources = urlParams.getAll('sources');
        sources.forEach(source => {
            $(`input[name="sources"][value="${source}"]`).prop('checked', true);
        });
    }

    /**
     * Initialize view toggle
     */
    function initializeViewToggle() {
        // Restore view preference
        const savedView = localStorage.getItem('ai_frontiers_search_view') || 'list';
        setResultsView(savedView);
        $(`.view-btn[data-view="${savedView}"]`).addClass('active').siblings().removeClass('active');
    }

    /**
     * Handle sort option changes
     */
    function handleSortChange() {
        const sortValue = $(this).val();
        updateUrlAndReload({ sort: sortValue, page: 1 });
    }

    /**
     * Handle filter changes
     */
    function handleFilterChange() {
        const filterType = $(this).attr('name');
        const checkedValues = $(`input[name="${filterType}"]:checked`).map(function() {
            return $(this).val();
        }).get();

        const params = {};
        params[filterType] = checkedValues;
        params.page = 1; // Reset to first page
        
        updateUrlAndReload(params);
    }

    /**
     * Handle show more filters
     */
    function handleShowMoreFilters() {
        const target = $(this).data('target');
        const filterSection = $(this).closest('.filter-section');
        const hiddenItems = filterSection.find('.filter-item:hidden');
        
        if (hiddenItems.length > 0) {
            hiddenItems.show();
            $(this).text('Show less');
        } else {
            filterSection.find('.filter-item:gt(7)').hide();
            $(this).text(`Show ${filterSection.find('.filter-item:hidden').length} more...`);
        }
    }

    /**
     * Handle view toggle
     */
    function handleViewToggle() {
        const view = $(this).data('view');
        setResultsView(view);
        $(this).addClass('active').siblings().removeClass('active');
        
        // Save preference
        localStorage.setItem('ai_frontiers_search_view', view);
    }

    /**
     * Handle infinite scroll
     */
    function handleScroll() {
        if ($(window).scrollTop() + $(window).height() > $(document).height() - 100) {
            loadMoreResults();
        }
    }

    /**
     * Set results view
     */
    function setResultsView(view) {
        const resultsContainer = $('.results-container');
        resultsContainer.removeClass('view-list view-grid view-compact').addClass(`view-${view}`);
        resultsContainer.attr('data-view', view);
    }

    /**
     * Load more results
     */
    function loadMoreResults() {
        // Implementation for infinite scroll loading
        console.log('Loading more results...');
    }

    /**
     * Update URL parameters and reload
     */
    function updateUrlAndReload(params) {
        const url = new URL(window.location);
        
        Object.keys(params).forEach(key => {
            if (Array.isArray(params[key])) {
                url.searchParams.delete(key);
                params[key].forEach(value => {
                    url.searchParams.append(key, value);
                });
            } else {
                url.searchParams.set(key, params[key]);
            }
        });
        
        window.location.href = url.toString();
    }

    // Public API
    return {
        init: init
    };
})();

// Advanced Search functionality
window.AdvancedSearch = (function() {
    'use strict';

    let config = {
        apiBaseUrl: '/api/'
    };

    /**
     * Initialize advanced search functionality
     */
    function init(options) {
        config = Object.assign(config, options || {});
        bindEvents();
        initializeDatePickers();
        initializeSliders();
    }

    /**
     * Bind event handlers
     */
    function bindEvents() {
        // Toggle sections
        $('.section-header h3').on('click', toggleSection);
        
        // Form validation
        $('#advanced-search-form').on('submit', validateForm);
        
        // Reset form
        $('.reset-form').on('click', resetForm);
        
        // Tag selection
        $('.tag-checkbox input').on('change', handleTagSelection);
        
        // Category selection
        $('.category-checkboxes input').on('change', handleCategorySelection);
    }

    /**
     * Initialize date pickers
     */
    function initializeDatePickers() {
        // Set max date to today
        const today = new Date().toISOString().split('T')[0];
        $('#FromDate, #ToDate').attr('max', today);
        
        // Validate date range
        $('#FromDate, #ToDate').on('change', function() {
            const fromDate = $('#FromDate').val();
            const toDate = $('#ToDate').val();
            
            if (fromDate && toDate && fromDate > toDate) {
                alert('From date cannot be later than To date');
                $(this).val('');
            }
        });
    }

    /**
     * Initialize range sliders
     */
    function initializeSliders() {
        // Reading time range
        $('#MinReadingTime, #MaxReadingTime').on('input', function() {
            const min = parseInt($('#MinReadingTime').val()) || 0;
            const max = parseInt($('#MaxReadingTime').val()) || 60;
            
            if (min > max) {
                $(this).val($(this).attr('id') === 'MinReadingTime' ? max : min);
            }
        });
    }

    /**
     * Toggle section visibility
     */
    function toggleSection() {
        const section = $(this).closest('.search-section');
        const content = section.find('.row, .filter-group').first();
        
        content.slideToggle();
        $(this).find('i').toggleClass('fa-chevron-down fa-chevron-up');
    }

    /**
     * Validate form before submission
     */
    function validateForm(event) {
        let isValid = true;
        const errors = [];
        
        // Check if at least one search criteria is provided
        const query = $('#Query').val().trim();
        const hasFilters = $('input[type="checkbox"]:checked, input[type="date"][value!=""], input[type="number"][value!=""], select option:selected:not([value=""])').length > 0;
        
        if (!query && !hasFilters) {
            errors.push('Please enter a search query or select at least one filter.');
            isValid = false;
        }
        
        // Validate date range
        const fromDate = $('#FromDate').val();
        const toDate = $('#ToDate').val();
        if (fromDate && toDate && fromDate > toDate) {
            errors.push('From date cannot be later than To date.');
            isValid = false;
        }
        
        // Validate reading time range
        const minTime = parseInt($('#MinReadingTime').val()) || 0;
        const maxTime = parseInt($('#MaxReadingTime').val()) || 1000;
        if (minTime > maxTime) {
            errors.push('Minimum reading time cannot be greater than maximum reading time.');
            isValid = false;
        }
        
        if (!isValid) {
            event.preventDefault();
            alert('Please fix the following errors:\n\n' + errors.join('\n'));
        }
        
        return isValid;
    }

    /**
     * Reset form to defaults
     */
    function resetForm() {
        $('#advanced-search-form')[0].reset();
        $('.custom-date-range').hide();
    }

    /**
     * Handle tag selection
     */
    function handleTagSelection() {
        const selectedTags = $('.tag-checkbox input:checked').map(function() {
            return $(this).siblings('.tag-label').text();
        }).get();
        
        updateSelectedTagsDisplay(selectedTags);
    }

    /**
     * Handle category selection
     */
    function handleCategorySelection() {
        const selectedCategories = $('.category-checkboxes input:checked').map(function() {
            return $(this).siblings('.checkbox-label').text().trim();
        }).get();
        
        updateSelectedCategoriesDisplay(selectedCategories);
    }

    /**
     * Update selected tags display
     */
    function updateSelectedTagsDisplay(tags) {
        if (tags.length === 0) return;
        
        // Could show selected tags in a summary area
        console.log('Selected tags:', tags);
    }

    /**
     * Update selected categories display
     */
    function updateSelectedCategoriesDisplay(categories) {
        if (categories.length === 0) return;
        
        // Could show selected categories in a summary area
        console.log('Selected categories:', categories);
    }

    // Public API
    return {
        init: init
    };
})();

// Initialize search functionality when DOM is ready
$(document).ready(function() {
    // Auto-initialize search on search pages
    if ($('[data-toggle="search-suggestions"]').length > 0) {
        Search.init();
    }
});