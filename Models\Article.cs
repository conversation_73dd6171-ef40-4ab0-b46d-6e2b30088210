using System.ComponentModel.DataAnnotations;

namespace NewsSite.Models
{
    public enum ArticleStatus
    {
        Draft = 1,
        Published = 2,
        Archived = 3,
        Hidden = 4,
        PendingReview = 5
    }

    public class Article
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(500)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string Slug { get; set; } = string.Empty;

        [Required]
        [MaxLength(1000)]
        public string Summary { get; set; } = string.Empty; // 1-2 paragraphs max

        // Full content for analysis and processing
        public string Content { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        public string Url { get; set; } = string.Empty; // Link to full content
        
        [MaxLength(2000)]
        public string OriginalUrl { get; set; } = string.Empty; // Original source URL if different

        [MaxLength(2000)]
        public string? ImageUrl { get; set; }

        [MaxLength(500)]
        public string? ImageAlt { get; set; }

        [MaxLength(200)]
        public string? Author { get; set; }

        [Required]
        public DateTime PublishedDate { get; set; }

        public DateTime? OriginalPublishedDate { get; set; } // Original publish date from source

        [Required]
        public ArticleStatus Status { get; set; } = ArticleStatus.Published;

        [Required]
        public int CategoryId { get; set; }

        [Required]
        public int SourceId { get; set; }

        // Analytics and engagement
        public int ViewCount { get; set; } = 0;
        public DateTime? LastViewedDate { get; set; }
        public decimal TrendingScore { get; set; } = 0;
        
        // Content classification
        public bool IsFeatured { get; set; } = false;
        public bool IsBreaking { get; set; } = false;
        public bool IsTrending { get; set; } = false;
        public bool IsDuplicate { get; set; } = false;

        // Soft delete
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedDate { get; set; }
        public string? DeletedBy { get; set; }

        // Priority for homepage display (1-10, higher = more important)
        public int Priority { get; set; } = 5;

        // External identifiers
        [MaxLength(200)]
        public string? ExternalId { get; set; } // ID from the source system

        [MaxLength(500)]
        public string? ExternalData { get; set; } // JSON for additional source-specific data
        
        // Content hash for duplicate detection
        [MaxLength(100)]
        public string? ContentHash { get; set; }

        // SEO fields
        [MaxLength(200)]
        public string? MetaTitle { get; set; }

        [MaxLength(500)]
        public string? MetaDescription { get; set; }

        // Audit fields
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public string CreatedBy { get; set; } = "System";
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }

        // Navigation properties
        public virtual Category Category { get; set; } = null!;
        public virtual Source Source { get; set; } = null!;
        public virtual ContentMetadata? Metadata { get; set; }
        public virtual ICollection<ArticleTag> ArticleTags { get; set; } = new List<ArticleTag>();
    }
}