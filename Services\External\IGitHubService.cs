using NewsSite.Models;

namespace NewsSite.Services.External
{
    public interface IGitHubService
    {
        /// <summary>
        /// Fetches trending repositories based on source configuration
        /// </summary>
        Task<List<Article>> FetchTrendingRepositoriesAsync(SourceConfiguration configuration);

        /// <summary>
        /// Searches GitHub repositories
        /// </summary>
        Task<List<Article>> SearchRepositoriesAsync(string query, string language = "", string sort = "stars", int maxResults = 25);

        /// <summary>
        /// Gets detailed information about a repository
        /// </summary>
        Task<GitHubRepositoryInfo?> GetRepositoryInfoAsync(string owner, string repo);

        /// <summary>
        /// Fetches latest releases from a repository
        /// </summary>
        Task<List<Article>> FetchRepositoryReleasesAsync(string owner, string repo, int maxResults = 10);

        /// <summary>
        /// Fetches trending repositories by topic
        /// </summary>
        Task<List<Article>> FetchTrendingByTopicAsync(string topic, int maxResults = 25);
    }

    public class GitHubRepositoryInfo
    {
        public string Name { get; set; } = "";
        public string FullName { get; set; } = "";
        public string Description { get; set; } = "";
        public string HtmlUrl { get; set; } = "";
        public string Language { get; set; } = "";
        public int StargazersCount { get; set; }
        public int ForksCount { get; set; }
        public int OpenIssuesCount { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime PushedAt { get; set; }
        public string OwnerLogin { get; set; } = "";
        public string OwnerAvatarUrl { get; set; } = "";
        public List<string> Topics { get; set; } = new();
        public string License { get; set; } = "";
        public bool HasIssues { get; set; }
        public bool HasWiki { get; set; }
        public bool HasPages { get; set; }
    }
}