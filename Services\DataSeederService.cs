using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using NewsSite.Data;
using NewsSite.Models;

namespace NewsSite.Services
{
    public class DataSeederService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<IdentityUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ILogger<DataSeederService> _logger;

        public DataSeederService(
            ApplicationDbContext context,
            UserManager<IdentityUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ILogger<DataSeederService> logger)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _logger = logger;
        }

        public async Task SeedCategoriesAsync()
        {
            try
            {
                var categories = new[]
                {
                    new Category
                    {
                        Name = "Breaking AI News",
                        Slug = "breaking-ai-news",
                        Description = "Stay updated with the latest breaking news and developments in artificial intelligence.",
                        Color = "#ff4444",
                        Icon = "fas fa-bolt",
                        DisplayOrder = 1,
                        IsActive = true
                    },
                    new Category
                    {
                        Name = "YouTube AI Discoveries",
                        Slug = "youtube-ai-discoveries",
                        Description = "Discover the best AI content from YouTube creators and channels.",
                        Color = "#ff0000",
                        Icon = "fab fa-youtube",
                        DisplayOrder = 2,
                        IsActive = true
                    },
                    new Category
                    {
                        Name = "Research & Papers",
                        Slug = "research-papers",
                        Description = "Latest peer-reviewed research papers and academic publications in AI.",
                        Color = "#4CAF50",
                        Icon = "fas fa-flask",
                        DisplayOrder = 3,
                        IsActive = true
                    },
                    new Category
                    {
                        Name = "Agentic Platforms",
                        Slug = "agentic-platforms",
                        Description = "Autonomous AI systems and intelligent agent platforms.",
                        Color = "#2196F3",
                        Icon = "fas fa-robot",
                        DisplayOrder = 4,
                        IsActive = true
                    },
                    new Category
                    {
                        Name = "AI Development Tools",
                        Slug = "ai-development-tools",
                        Description = "Essential tools and frameworks for AI developers.",
                        Color = "#9C27B0",
                        Icon = "fas fa-tools",
                        DisplayOrder = 5,
                        IsActive = true
                    },
                    new Category
                    {
                        Name = "Trending Open Source",
                        Slug = "trending-open-source",
                        Description = "Discover trending open-source AI projects and repositories.",
                        Color = "#FF9800",
                        Icon = "fab fa-github",
                        DisplayOrder = 6,
                        IsActive = true
                    }
                };

                foreach (var category in categories)
                {
                    var existingCategory = await _context.Categories
                        .FirstOrDefaultAsync(c => c.Slug == category.Slug);

                    if (existingCategory == null)
                    {
                        category.CreatedDate = DateTime.UtcNow;
                        _context.Categories.Add(category);
                        _logger.LogInformation("Added category: {CategoryName}", category.Name);
                    }
                    else
                    {
                        // Update existing category properties if needed
                        existingCategory.Name = category.Name;
                        existingCategory.Description = category.Description;
                        existingCategory.Color = category.Color;
                        existingCategory.Icon = category.Icon;
                        existingCategory.DisplayOrder = category.DisplayOrder;
                        existingCategory.IsActive = category.IsActive;
                        existingCategory.ModifiedDate = DateTime.UtcNow;
                        
                        _logger.LogInformation("Updated existing category: {CategoryName}", category.Name);
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("Category seeding completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while seeding categories");
                throw;
            }
        }

        public async Task SeedRolesAndAdminUserAsync()
        {
            try
            {
                // Create Admin role if it doesn't exist
                const string adminRole = "Admin";
                if (!await _roleManager.RoleExistsAsync(adminRole))
                {
                    var role = new IdentityRole(adminRole);
                    var result = await _roleManager.CreateAsync(role);
                    
                    if (result.Succeeded)
                    {
                        _logger.LogInformation("Created Admin role successfully");
                    }
                    else
                    {
                        _logger.LogError("Failed to create Admin role: {Errors}", 
                            string.Join(", ", result.Errors.Select(e => e.Description)));
                        return;
                    }
                }

                // Create default admin user if none exists
                const string adminEmail = "<EMAIL>";
                const string adminPassword = "Admin123!";

                var adminUser = await _userManager.FindByEmailAsync(adminEmail);
                if (adminUser == null)
                {
                    adminUser = new IdentityUser
                    {
                        UserName = adminEmail,
                        Email = adminEmail,
                        EmailConfirmed = true // Skip email confirmation for admin
                    };

                    var createResult = await _userManager.CreateAsync(adminUser, adminPassword);
                    if (createResult.Succeeded)
                    {
                        _logger.LogInformation("Created admin user: {Email}", adminEmail);
                        
                        // Add admin user to Admin role
                        var roleResult = await _userManager.AddToRoleAsync(adminUser, adminRole);
                        if (roleResult.Succeeded)
                        {
                            _logger.LogInformation("Added admin user to Admin role");
                        }
                        else
                        {
                            _logger.LogError("Failed to add admin user to Admin role: {Errors}", 
                                string.Join(", ", roleResult.Errors.Select(e => e.Description)));
                        }
                    }
                    else
                    {
                        _logger.LogError("Failed to create admin user: {Errors}", 
                            string.Join(", ", createResult.Errors.Select(e => e.Description)));
                    }
                }
                else
                {
                    // Ensure existing admin user is in Admin role
                    if (!await _userManager.IsInRoleAsync(adminUser, adminRole))
                    {
                        var roleResult = await _userManager.AddToRoleAsync(adminUser, adminRole);
                        if (roleResult.Succeeded)
                        {
                            _logger.LogInformation("Added existing admin user to Admin role");
                        }
                    }
                }

                _logger.LogInformation("Admin role and user seeding completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while seeding admin role and user");
                throw;
            }
        }

        public async Task SeedSampleArticlesAsync()
        {
            try
            {
                // Check if we already have articles
                var existingArticlesCount = await _context.Articles.CountAsync();
                if (existingArticlesCount > 0)
                {
                    _logger.LogInformation("Articles already exist, skipping sample article seeding");
                    return;
                }

                // Get categories
                var categories = await _context.Categories.ToListAsync();
                if (!categories.Any())
                {
                    _logger.LogWarning("No categories found for sample article seeding");
                    return;
                }

                // Create a default source if none exists
                var source = await _context.Sources.FirstOrDefaultAsync();
                if (source == null)
                {
                    source = new Source
                    {
                        Name = "AI Frontiers",
                        Url = "https://ai-frontiers.com",
                        Description = "AI Frontiers aggregated content",
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow
                    };
                    _context.Sources.Add(source);
                    await _context.SaveChangesAsync();
                }

                var sampleArticles = new List<Article>();
                var random = new Random();

                foreach (var category in categories)
                {
                    for (int i = 1; i <= 5; i++)
                    {
                        var publishedDate = DateTime.UtcNow.AddDays(-random.Next(1, 30));
                        var article = new Article
                        {
                            Title = GetSampleTitle(category.Name, i),
                            Slug = GetSampleSlug(category.Slug, i),
                            Summary = GetSampleSummary(category.Name),
                            Content = GetSampleContent(category.Name),
                            Author = GetSampleAuthor(),
                            PublishedDate = publishedDate,
                            Status = ArticleStatus.Published,
                            CategoryId = category.Id,
                            SourceId = source.Id,
                            IsFeatured = i <= 2, // First 2 articles are featured
                            IsBreaking = category.Slug == "breaking-ai-news" && i == 1,
                            IsTrending = random.Next(1, 4) == 1, // 25% chance of trending
                            ViewCount = random.Next(100, 5000),
                            Priority = random.Next(1, 10),
                            CreatedDate = publishedDate,
                            ImageUrl = GetSampleImageUrl(category.Slug),
                            ImageAlt = $"{category.Name} illustration"
                        };

                        sampleArticles.Add(article);
                    }
                }

                _context.Articles.AddRange(sampleArticles);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Added {Count} sample articles", sampleArticles.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while seeding sample articles");
                throw;
            }
        }

        private static string GetSampleTitle(string categoryName, int index)
        {
            var titles = categoryName switch
            {
                "Breaking AI News" => new[]
                {
                    "OpenAI Announces Revolutionary GPT-5 with Advanced Reasoning",
                    "Google DeepMind Achieves Breakthrough in Protein Folding",
                    "Microsoft Copilot Gets Major AI Enhancement Update",
                    "Meta Releases Open Source Multimodal AI Model",
                    "NVIDIA Unveils Next-Generation AI Chip Architecture"
                },
                "YouTube AI Discoveries" => new[]
                {
                    "Top AI Researcher Explains Transformer Architecture",
                    "Build Your First Neural Network - Complete Tutorial",
                    "AI Art Generation: From Concept to Creation",
                    "Machine Learning Engineering Best Practices",
                    "The Future of AI: Expert Panel Discussion"
                },
                "Research & Papers" => new[]
                {
                    "Attention Is All You Need: Revisiting Transformers",
                    "Novel Approach to Few-Shot Learning in NLP",
                    "Quantum Machine Learning: Current State and Future",
                    "Ethical AI: Framework for Responsible Development",
                    "Federated Learning in Healthcare Applications"
                },
                "Agentic Platforms" => new[]
                {
                    "AutoGPT: The Future of Autonomous AI Agents",
                    "Microsoft Autonomous Agent Platform Overview",
                    "Building Multi-Agent Systems for Enterprise",
                    "LangChain: Orchestrating AI Agent Workflows",
                    "Crew AI: Collaborative AI Agent Framework"
                },
                "AI Development Tools" => new[]
                {
                    "TensorFlow 3.0: What's New for Developers",
                    "PyTorch Lightning: Simplifying ML Development",
                    "Hugging Face Transformers: Complete Guide",
                    "MLOps Best Practices with Kubernetes",
                    "Docker for Machine Learning Workflows"
                },
                "Trending Open Source" => new[]
                {
                    "Llama 3: Meta's Open Source Language Model",
                    "Stable Diffusion 3.0 Released on GitHub",
                    "Whisper: OpenAI's Speech Recognition Goes Open",
                    "YOLO v8: Real-Time Object Detection",
                    "Apache Airflow for ML Pipeline Management"
                },
                _ => new[] { $"Sample Article {index} for {categoryName}" }
            };

            return titles[Math.Min(index - 1, titles.Length - 1)];
        }

        private static string GetSampleSlug(string categorySlug, int index)
        {
            return $"{categorySlug}-sample-article-{index}-{DateTime.Now:yyyyMMdd}";
        }

        private static string GetSampleSummary(string categoryName)
        {
            return categoryName switch
            {
                "Breaking AI News" => "Latest developments in artificial intelligence that are shaping the future of technology and society.",
                "YouTube AI Discoveries" => "Educational content and tutorials from leading AI creators and researchers on YouTube.",
                "Research & Papers" => "Peer-reviewed research papers and academic publications advancing the field of artificial intelligence.",
                "Agentic Platforms" => "Platforms and frameworks enabling the development of autonomous AI agents and systems.",
                "AI Development Tools" => "Tools, libraries, and frameworks that make AI development more accessible and efficient.",
                "Trending Open Source" => "Open source AI projects gaining traction in the developer community.",
                _ => "Sample article summary for AI-related content."
            };
        }

        private static string GetSampleContent(string categoryName)
        {
            return $@"
# {categoryName} Article Content

This is a comprehensive article about {categoryName.ToLower()} that covers the latest developments and insights in this exciting field.

## Introduction

Artificial Intelligence continues to evolve at a rapid pace, with new breakthroughs and innovations happening regularly. This article explores the current state and future possibilities in {categoryName.ToLower()}.

## Key Points

- **Innovation**: Cutting-edge developments in AI technology
- **Impact**: How these changes affect the industry and society
- **Future**: What we can expect in the coming months and years

## Technical Details

The technical aspects of {categoryName.ToLower()} involve complex algorithms and methodologies that are pushing the boundaries of what's possible with artificial intelligence.

## Conclusion

As AI continues to advance, staying informed about developments in {categoryName.ToLower()} is crucial for anyone interested in the future of technology.

*This is sample content generated for demonstration purposes.*
";
        }

        private static string GetSampleAuthor()
        {
            var authors = new[]
            {
                "Dr. Sarah Chen",
                "Michael Rodriguez",
                "Prof. Emily Watson",
                "David Kim",
                "Dr. Alex Thompson",
                "Jessica Liu",
                "Dr. Robert Johnson",
                "Maria Garcia"
            };

            return authors[new Random().Next(authors.Length)];
        }

        private static string GetSampleImageUrl(string categorySlug)
        {
            // Using placeholder images for demonstration
            return categorySlug switch
            {
                "breaking-ai-news" => "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=200&fit=crop",
                "youtube-ai-discoveries" => "https://images.unsplash.com/photo-1611162616305-c69b3fa7fbe0?w=400&h=200&fit=crop",
                "research-papers" => "https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=400&h=200&fit=crop",
                "agentic-platforms" => "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=200&fit=crop",
                "ai-development-tools" => "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=200&fit=crop",
                "trending-open-source" => "https://images.unsplash.com/photo-1556075798-4825dfaaf498?w=400&h=200&fit=crop",
                _ => "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=200&fit=crop"
            };
        }
    }
}