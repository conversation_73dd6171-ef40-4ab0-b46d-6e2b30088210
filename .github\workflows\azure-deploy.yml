name: Deploy AI Frontiers to Azure

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  AZURE_WEBAPP_NAME: aifrontiers-web-prod
  AZURE_WEBAPP_PACKAGE_PATH: '.'
  DOTNET_VERSION: '8.0.x'
  RESOURCE_GROUP: rg-aifrontiers-prod
  SQL_SERVER_NAME: aifrontiers-sql-prod.database.windows.net
  SQL_DATABASE_NAME: aifrontiers-db-prod

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build application
      run: dotnet build --configuration Release --no-restore
    
    - name: Run tests
      run: dotnet test --configuration Release --no-build --verbosity normal --collect:"XPlat Code Coverage"
    
    - name: Publish application
      run: dotnet publish --configuration Release --output ./publish --no-build
    
    - name: Upload artifact for deployment job
      uses: actions/upload-artifact@v3
      with:
        name: .net-app
        path: ./publish

  deploy-infrastructure:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: build
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    
    - name: Deploy ARM Template
      uses: azure/arm-deploy@v1
      with:
        subscriptionId: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
        resourceGroupName: ${{ env.RESOURCE_GROUP }}
        template: ./azure-infrastructure.json
        parameters: ./azure-infrastructure.parameters.json sqlAdminPassword=${{ secrets.SQL_ADMIN_PASSWORD }}
        deploymentName: 'aifrontiers-infrastructure-${{ github.run_number }}'

  deploy-database:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [build, deploy-infrastructure]
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
    
    - name: Install EF Core tools
      run: dotnet tool install --global dotnet-ef
    
    - name: Update database
      run: |
        dotnet ef database update --connection "${{ secrets.AZURE_SQL_CONNECTION_STRING }}" --verbose
      env:
        ASPNETCORE_ENVIRONMENT: Production

  deploy-app:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [build, deploy-infrastructure, deploy-database]
    environment: production
    
    steps:
    - name: Download artifact from build job
      uses: actions/download-artifact@v3
      with:
        name: .net-app
        path: ./publish
    
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    
    - name: Deploy to Azure Web App
      uses: azure/webapps-deploy@v2
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        package: ./publish
    
    - name: Configure App Settings
      uses: azure/appservice-settings@v1
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        app-settings-json: |
          [
            {
              "name": "ASPNETCORE_ENVIRONMENT",
              "value": "Production"
            },
            {
              "name": "YouTube__ApiKey",
              "value": "${{ secrets.YOUTUBE_API_KEY }}"
            },
            {
              "name": "GitHub__ApiToken",
              "value": "${{ secrets.GITHUB_API_TOKEN }}"
            },
            {
              "name": "ApplicationInsights__ConnectionString",
              "value": "${{ secrets.APPLICATION_INSIGHTS_CONNECTION_STRING }}"
            }
          ]

  configure-keyvault:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [deploy-infrastructure]
    environment: production
    
    steps:
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}
    
    - name: Set KeyVault Secrets
      run: |
        # Store API keys in Key Vault
        az keyvault secret set --vault-name "aifrontiers-kv-prod" --name "YouTube-ApiKey" --value "${{ secrets.YOUTUBE_API_KEY }}"
        az keyvault secret set --vault-name "aifrontiers-kv-prod" --name "GitHub-ApiToken" --value "${{ secrets.GITHUB_API_TOKEN }}"
        az keyvault secret set --vault-name "aifrontiers-kv-prod" --name "ApplicationInsights-ConnectionString" --value "${{ secrets.APPLICATION_INSIGHTS_CONNECTION_STRING }}"
        
        # Grant access to the web app managed identity
        WEBAPP_PRINCIPAL_ID=$(az webapp identity show --name ${{ env.AZURE_WEBAPP_NAME }} --resource-group ${{ env.RESOURCE_GROUP }} --query principalId --output tsv)
        az keyvault set-policy --name "aifrontiers-kv-prod" --object-id $WEBAPP_PRINCIPAL_ID --secret-permissions get list

  health-check:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [deploy-app, configure-keyvault]
    
    steps:
    - name: Health Check
      run: |
        echo "Waiting for application to start..."
        sleep 60
        
        # Check health endpoint
        response=$(curl -s -o /dev/null -w "%{http_code}" https://${{ env.AZURE_WEBAPP_NAME }}.azurewebsites.net/health)
        if [ $response -eq 200 ]; then
          echo "✅ Health check passed"
        else
          echo "❌ Health check failed with status code: $response"
          exit 1
        fi
        
        # Check main page
        response=$(curl -s -o /dev/null -w "%{http_code}" https://${{ env.AZURE_WEBAPP_NAME }}.azurewebsites.net)
        if [ $response -eq 200 ]; then
          echo "✅ Main page accessible"
        else
          echo "❌ Main page check failed with status code: $response"
          exit 1
        fi

  notify:
    runs-on: ubuntu-latest
    if: always() && github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [deploy-app, health-check]
    
    steps:
    - name: Deployment Status
      run: |
        if [ "${{ needs.deploy-app.result }}" == "success" ] && [ "${{ needs.health-check.result }}" == "success" ]; then
          echo "🚀 Deployment successful! Application is live at https://${{ env.AZURE_WEBAPP_NAME }}.azurewebsites.net"
        else
          echo "💥 Deployment failed. Check the logs for details."
          exit 1
        fi