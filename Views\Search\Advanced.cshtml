@model AdvancedSearchViewModel
@{
    ViewData["Title"] = "Advanced Search - AI Frontiers";
    ViewData["Description"] = "Use advanced search to find exactly what you're looking for with powerful filters and options";
}

<div class="advanced-search-page">
    <!-- Header -->
    <div class="advanced-search-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="fas fa-sliders-h"></i>
                            Advanced Search
                        </h1>
                        <p class="page-subtitle">Fine-tune your search with powerful filters and options</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Search Form -->
    <div class="advanced-search-content">
        <div class="container">
            <form method="post" action="@Url.Action("Advanced")" id="advanced-search-form">
                <div class="search-form-sections">
                    
                    <!-- Query Section -->
                    <div class="search-section query-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-search"></i>
                                Search Query
                            </h3>
                            <p class="section-description">Enter your search terms and specify how to match them</p>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="Query" class="form-label">Search Terms</label>
                                    <div class="search-input-wrapper">
                                        <input type="text" 
                                               id="Query" 
                                               name="Query" 
                                               class="form-control search-input" 
                                               value="@(Model.SearchRequest?.Query ?? "")"
                                               placeholder="Enter your search terms..."
                                               autocomplete="off"
                                               data-toggle="search-suggestions">
                                        <div id="search-suggestions" class="search-suggestions d-none"></div>
                                    </div>
                                    <small class="form-text text-muted">
                                        Use quotes for exact phrases, + for required terms, - to exclude terms
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="query-examples">
                                    <h6>Example Queries:</h6>
                                    <div class="example-list">
                                        <button type="button" class="btn btn-link example-btn" data-query='"machine learning"'>
                                            "machine learning"
                                        </button>
                                        <button type="button" class="btn btn-link example-btn" data-query="AI +neural -fiction">
                                            AI +neural -fiction
                                        </button>
                                        <button type="button" class="btn btn-link example-btn" data-query="algorithm*">
                                            algorithm*
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Filters -->
                    <div class="search-section filters-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-filter"></i>
                                Content Filters
                            </h3>
                            <p class="section-description">Narrow your search by content type, category, and source</p>
                        </div>

                        <div class="row">
                            <!-- Categories -->
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="filter-group">
                                    <label class="form-label">Categories</label>
                                    <div class="category-checkboxes">
                                        @foreach (var category in Model.Categories)
                                        {
                                            <label class="custom-checkbox">
                                                <input type="checkbox" 
                                                       name="CategoryIds" 
                                                       value="@category.Id"
                                                       @(Model.SearchRequest?.CategoryIds?.Contains(category.Id) == true ? "checked" : "")>
                                                <span class="checkbox-label">
                                                    <i class="@category.IconClass"></i>
                                                    @category.Name
                                                    <span class="item-count">@category.ArticleCount</span>
                                                </span>
                                            </label>
                                        }
                                    </div>
                                </div>
                            </div>

                            <!-- Sources -->
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="filter-group">
                                    <label class="form-label">Sources</label>
                                    <div class="source-checkboxes max-height-scroll">
                                        @foreach (var source in Model.Sources.Take(10))
                                        {
                                            <label class="custom-checkbox">
                                                <input type="checkbox" 
                                                       name="SourceIds" 
                                                       value="@source.Id"
                                                       @(Model.SearchRequest?.SourceIds?.Contains(source.Id) == true ? "checked" : "")>
                                                <span class="checkbox-label">
                                                    @source.Name
                                                    <span class="item-count">@source.ArticleCount</span>
                                                </span>
                                            </label>
                                        }
                                    </div>
                                </div>
                            </div>

                            <!-- Tags -->
                            <div class="col-lg-4 col-md-12 mb-4">
                                <div class="filter-group">
                                    <label class="form-label">Popular Tags</label>
                                    <div class="tag-cloud-select">
                                        @foreach (var tag in Model.PopularTags.Take(20))
                                        {
                                            <label class="tag-checkbox">
                                                <input type="checkbox" 
                                                       name="TagIds" 
                                                       value="@tag.Id"
                                                       @(Model.SearchRequest?.TagIds?.Contains(tag.Id) == true ? "checked" : "")>
                                                <span class="tag-label">@tag.Name</span>
                                            </label>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Author & Date Filters -->
                    <div class="search-section metadata-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-calendar-alt"></i>
                                Author & Date Filters
                            </h3>
                            <p class="section-description">Filter by author, publication date, and time range</p>
                        </div>

                        <div class="row">
                            <!-- Author -->
                            <div class="col-lg-6 mb-4">
                                <div class="filter-group">
                                    <label for="Author" class="form-label">Author</label>
                                    <input type="text" 
                                           id="Author" 
                                           name="Author" 
                                           class="form-control" 
                                           value="@(Model.SearchRequest?.Author ?? "")"
                                           placeholder="Enter author name..."
                                           list="author-suggestions">
                                    <datalist id="author-suggestions">
                                        @foreach (var author in Model.Authors.Take(20))
                                        {
                                            <option value="@author">
                                        }
                                    </datalist>
                                </div>
                            </div>

                            <!-- Date Range -->
                            <div class="col-lg-6 mb-4">
                                <div class="filter-group">
                                    <label class="form-label">Date Range</label>
                                    <div class="date-range-selector">
                                        <select class="form-control date-range-preset" onchange="handleDateRangeChange(this)">
                                            <option value="">Select range...</option>
                                            @foreach (var option in Model.DateRangeOptions)
                                            {
                                                <option value="@option.Value" data-days="@option.Days">@option.Label</option>
                                            }
                                        </select>
                                        
                                        <div class="custom-date-range mt-2" style="display: none;">
                                            <div class="row">
                                                <div class="col-6">
                                                    <label for="FromDate" class="form-label small">From</label>
                                                    <input type="date" 
                                                           id="FromDate" 
                                                           name="FromDate" 
                                                           class="form-control"
                                                           value="@(Model.SearchRequest?.FromDate?.ToString("yyyy-MM-dd") ?? "")">
                                                </div>
                                                <div class="col-6">
                                                    <label for="ToDate" class="form-label small">To</label>
                                                    <input type="date" 
                                                           id="ToDate" 
                                                           name="ToDate" 
                                                           class="form-control"
                                                           value="@(Model.SearchRequest?.ToDate?.ToString("yyyy-MM-dd") ?? "")">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Properties -->
                    <div class="search-section properties-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-cog"></i>
                                Content Properties
                            </h3>
                            <p class="section-description">Filter by content characteristics and quality</p>
                        </div>

                        <div class="row">
                            <!-- Content Status -->
                            <div class="col-lg-4 mb-4">
                                <div class="filter-group">
                                    <label class="form-label">Content Status</label>
                                    <div class="status-checkboxes">
                                        <label class="custom-checkbox">
                                            <input type="checkbox" 
                                                   name="IsFeatured" 
                                                   value="true"
                                                   @(Model.SearchRequest?.IsFeatured == true ? "checked" : "")>
                                            <span class="checkbox-label">
                                                <i class="fas fa-star text-warning"></i>
                                                Featured Content
                                            </span>
                                        </label>
                                        <label class="custom-checkbox">
                                            <input type="checkbox" 
                                                   name="IsBreaking" 
                                                   value="true"
                                                   @(Model.SearchRequest?.IsBreaking == true ? "checked" : "")>
                                            <span class="checkbox-label">
                                                <i class="fas fa-exclamation-circle text-danger"></i>
                                                Breaking News
                                            </span>
                                        </label>
                                        <label class="custom-checkbox">
                                            <input type="checkbox" 
                                                   name="IsTrending" 
                                                   value="true"
                                                   @(Model.SearchRequest?.IsTrending == true ? "checked" : "")>
                                            <span class="checkbox-label">
                                                <i class="fas fa-trending-up text-success"></i>
                                                Trending
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Media Content -->
                            <div class="col-lg-4 mb-4">
                                <div class="filter-group">
                                    <label class="form-label">Media Content</label>
                                    <div class="media-checkboxes">
                                        <label class="custom-checkbox">
                                            <input type="checkbox" 
                                                   name="HasImages" 
                                                   value="true"
                                                   @(Model.SearchRequest?.HasImages == true ? "checked" : "")>
                                            <span class="checkbox-label">
                                                <i class="fas fa-image text-info"></i>
                                                Has Images
                                            </span>
                                        </label>
                                        <label class="custom-checkbox">
                                            <input type="checkbox" 
                                                   name="HasVideo" 
                                                   value="true"
                                                   @(Model.SearchRequest?.HasVideo == true ? "checked" : "")>
                                            <span class="checkbox-label">
                                                <i class="fas fa-video text-primary"></i>
                                                Has Video
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Reading Time -->
                            <div class="col-lg-4 mb-4">
                                <div class="filter-group">
                                    <label class="form-label">Reading Time (minutes)</label>
                                    <div class="reading-time-range">
                                        <div class="row">
                                            <div class="col-6">
                                                <input type="number" 
                                                       name="MinReadingTime" 
                                                       class="form-control" 
                                                       placeholder="Min"
                                                       min="0" 
                                                       max="60"
                                                       value="@(Model.SearchRequest?.MinReadingTime?.ToString() ?? "")">
                                            </div>
                                            <div class="col-6">
                                                <input type="number" 
                                                       name="MaxReadingTime" 
                                                       class="form-control" 
                                                       placeholder="Max"
                                                       min="0" 
                                                       max="60"
                                                       value="@(Model.SearchRequest?.MaxReadingTime?.ToString() ?? "")">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sort & Display Options -->
                    <div class="search-section sort-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-sort"></i>
                                Sort & Display Options
                            </h3>
                            <p class="section-description">Control how results are sorted and displayed</p>
                        </div>

                        <div class="row">
                            <!-- Sort Options -->
                            <div class="col-lg-6 mb-4">
                                <div class="filter-group">
                                    <label for="SortBy" class="form-label">Sort Results By</label>
                                    <select id="SortBy" name="SortBy" class="form-control">
                                        @foreach (var option in Model.SortOptions)
                                        {
                                            <option value="@option.Value" 
                                                    @(Model.SearchRequest?.SortBy == option.Value || (Model.SearchRequest?.SortBy == null && option.IsDefault) ? "selected" : "")>
                                                @option.Label
                                            </option>
                                        }
                                    </select>
                                    
                                    <div class="mt-2">
                                        <label class="custom-checkbox">
                                            <input type="checkbox" 
                                                   name="SortDescending" 
                                                   value="true"
                                                   @(Model.SearchRequest?.SortDescending != false ? "checked" : "")>
                                            <span class="checkbox-label">Descending order</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Boost Options -->
                            <div class="col-lg-6 mb-4">
                                <div class="filter-group">
                                    <label class="form-label">Ranking Boosts</label>
                                    <div class="boost-checkboxes">
                                        <label class="custom-checkbox">
                                            <input type="checkbox" 
                                                   name="BoostFeatured" 
                                                   value="true"
                                                   @(Model.SearchRequest?.BoostFeatured != false ? "checked" : "")>
                                            <span class="checkbox-label">Boost featured content</span>
                                        </label>
                                        <label class="custom-checkbox">
                                            <input type="checkbox" 
                                                   name="BoostRecent" 
                                                   value="true"
                                                   @(Model.SearchRequest?.BoostRecent != false ? "checked" : "")>
                                            <span class="checkbox-label">Boost recent content</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Results Per Page -->
                            <div class="col-lg-6 mb-4">
                                <div class="filter-group">
                                    <label for="PageSize" class="form-label">Results Per Page</label>
                                    <select id="PageSize" name="PageSize" class="form-control">
                                        <option value="10" @(Model.SearchRequest?.PageSize == 10 ? "selected" : "")>10 results</option>
                                        <option value="20" @(Model.SearchRequest?.PageSize == 20 || Model.SearchRequest?.PageSize == null ? "selected" : "")>20 results</option>
                                        <option value="50" @(Model.SearchRequest?.PageSize == 50 ? "selected" : "")>50 results</option>
                                        <option value="100" @(Model.SearchRequest?.PageSize == 100 ? "selected" : "")>100 results</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Quality Score -->
                            <div class="col-lg-6 mb-4">
                                <div class="filter-group">
                                    <label for="MinQualityScore" class="form-label">Minimum Quality Score</label>
                                    <select id="MinQualityScore" name="MinQualityScore" class="form-control">
                                        <option value="">Any quality</option>
                                        <option value="0.3" @(Model.SearchRequest?.MinQualityScore == 0.3m ? "selected" : "")>30% and above</option>
                                        <option value="0.5" @(Model.SearchRequest?.MinQualityScore == 0.5m ? "selected" : "")>50% and above</option>
                                        <option value="0.7" @(Model.SearchRequest?.MinQualityScore == 0.7m ? "selected" : "")>70% and above</option>
                                        <option value="0.9" @(Model.SearchRequest?.MinQualityScore == 0.9m ? "selected" : "")>90% and above</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="search-actions">
                    <div class="row">
                        <div class="col-12">
                            <div class="action-buttons">
                                <button type="submit" class="btn btn-primary btn-lg search-btn">
                                    <i class="fas fa-search"></i>
                                    Search with Filters
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                    <i class="fas fa-undo"></i>
                                    Reset Filters
                                </button>
                                <a href="@Url.Action("Index")" class="btn btn-outline-primary">
                                    <i class="fas fa-arrow-left"></i>
                                    Basic Search
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/search.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize advanced search
            AdvancedSearch.init({
                apiBaseUrl: '@Url.Content("~/api/")'
            });

            // Example query buttons
            $('.example-btn').click(function() {
                $('#Query').val($(this).data('query'));
            });
        });

        function handleDateRangeChange(select) {
            var customRange = $('.custom-date-range');
            
            if (select.value === 'custom') {
                customRange.show();
            } else {
                customRange.hide();
                
                if (select.value && $(select.selectedOptions[0]).data('days')) {
                    var days = $(select.selectedOptions[0]).data('days');
                    var toDate = new Date();
                    var fromDate = new Date();
                    fromDate.setDate(fromDate.getDate() - days);
                    
                    $('#FromDate').val(fromDate.toISOString().split('T')[0]);
                    $('#ToDate').val(toDate.toISOString().split('T')[0]);
                } else {
                    $('#FromDate').val('');
                    $('#ToDate').val('');
                }
            }
        }

        function resetForm() {
            if (confirm('Reset all filters and start over?')) {
                document.getElementById('advanced-search-form').reset();
                $('.custom-date-range').hide();
            }
        }
    </script>
}