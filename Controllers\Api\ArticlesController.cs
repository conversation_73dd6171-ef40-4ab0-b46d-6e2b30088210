using Microsoft.AspNetCore.Mvc;
using NewsSite.Models;
using NewsSite.Services;

namespace NewsSite.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    public class ArticlesController : ControllerBase
    {
        private readonly IContentService _contentService;
        private readonly ILogger<ArticlesController> _logger;

        public ArticlesController(IContentService contentService, ILogger<ArticlesController> logger)
        {
            _contentService = contentService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetArticles(
            [FromQuery] string? category = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string sortBy = "PublishedDate",
            [FromQuery] bool sortDesc = true,
            [FromQuery] string? keywords = null)
        {
            try
            {
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 20;

                var criteria = new ArticleSearchCriteria
                {
                    Keywords = keywords,
                    Status = ArticleStatus.Published,
                    Page = page,
                    PageSize = pageSize,
                    SortBy = sortBy,
                    SortDescending = sortDesc
                };

                // Handle category filtering
                if (!string.IsNullOrEmpty(category) && category != "all")
                {
                    // You might want to add a category service method to get ID by slug
                    // For now, we'll handle special categories
                    switch (category.ToLower())
                    {
                        case "breaking":
                            criteria.IsBreaking = true;
                            break;
                        case "featured":
                            criteria.IsFeatured = true;
                            break;
                        case "trending":
                            criteria.IsTrending = true;
                            break;
                        default:
                            // Try to parse as category ID or handle as slug
                            if (int.TryParse(category, out int categoryId))
                            {
                                criteria.CategoryId = categoryId;
                            }
                            break;
                    }
                }

                var result = await _contentService.GetArticlesAsync(criteria);
                var response = new PaginatedArticleResult
                {
                    Articles = MapToDto(result.Items),
                    TotalCount = result.TotalCount,
                    Page = result.Page,
                    PageSize = result.PageSize
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving articles");
                return StatusCode(500, new { error = "Unable to retrieve articles" });
            }
        }

        [HttpGet("featured")]
        public async Task<IActionResult> GetFeatured([FromQuery] int count = 10)
        {
            try
            {
                if (count < 1 || count > 50) count = 10;

                var articles = await _contentService.GetFeaturedArticlesAsync(count);
                var response = MapToDto(articles);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured articles");
                return StatusCode(500, new { error = "Unable to retrieve featured articles" });
            }
        }

        [HttpGet("latest")]
        public async Task<IActionResult> GetLatest([FromQuery] int count = 20)
        {
            try
            {
                if (count < 1 || count > 100) count = 20;

                var articles = await _contentService.GetLatestArticlesAsync(count);
                var response = MapToDto(articles);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving latest articles");
                return StatusCode(500, new { error = "Unable to retrieve latest articles" });
            }
        }

        [HttpGet("trending")]
        public async Task<IActionResult> GetTrending([FromQuery] int count = 10)
        {
            try
            {
                if (count < 1 || count > 50) count = 10;

                var articles = await _contentService.GetTrendingArticlesAsync(count);
                var response = MapToDto(articles);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving trending articles");
                return StatusCode(500, new { error = "Unable to retrieve trending articles" });
            }
        }

        [HttpGet("breaking")]
        public async Task<IActionResult> GetBreaking([FromQuery] int count = 5)
        {
            try
            {
                if (count < 1 || count > 20) count = 5;

                var articles = await _contentService.GetBreakingNewsAsync(count);
                var response = MapToDto(articles);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving breaking news");
                return StatusCode(500, new { error = "Unable to retrieve breaking news" });
            }
        }

        [HttpGet("popular")]
        public async Task<IActionResult> GetPopular([FromQuery] int days = 7, [FromQuery] int count = 10)
        {
            try
            {
                if (days < 1 || days > 365) days = 7;
                if (count < 1 || count > 50) count = 10;

                var articles = await _contentService.GetPopularArticlesAsync(days, count);
                var response = MapToDto(articles);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving popular articles");
                return StatusCode(500, new { error = "Unable to retrieve popular articles" });
            }
        }

        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var article = await _contentService.GetByIdAsync(id);
                if (article == null)
                {
                    return NotFound(new { error = "Article not found" });
                }

                var response = MapToDto(new[] { article }).First();
                
                // Increment view count asynchronously
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _contentService.IncrementViewCountAsync(id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to increment view count for article {ArticleId}", id);
                    }
                });

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving article {ArticleId}", id);
                return StatusCode(500, new { error = "Unable to retrieve article" });
            }
        }

        [HttpGet("{slug}")]
        public async Task<IActionResult> GetBySlug(string slug)
        {
            try
            {
                var article = await _contentService.GetBySlugAsync(slug);
                if (article == null)
                {
                    return NotFound(new { error = "Article not found" });
                }

                var response = MapToDto(new[] { article }).First();
                
                // Increment view count asynchronously
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _contentService.IncrementViewCountAsync(article.Id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to increment view count for article {ArticleId}", article.Id);
                    }
                });

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving article by slug {Slug}", slug);
                return StatusCode(500, new { error = "Unable to retrieve article" });
            }
        }

        [HttpGet("{id:int}/related")]
        public async Task<IActionResult> GetRelated(int id, [FromQuery] int count = 5)
        {
            try
            {
                if (count < 1 || count > 20) count = 5;

                var articles = await _contentService.GetRelatedArticlesAsync(id, count);
                var response = MapToDto(articles);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving related articles for {ArticleId}", id);
                return StatusCode(500, new { error = "Unable to retrieve related articles" });
            }
        }

        [HttpPost("{id:int}/view")]
        public async Task<IActionResult> IncrementView(int id)
        {
            try
            {
                var success = await _contentService.IncrementViewCountAsync(id);
                if (!success)
                {
                    return NotFound(new { error = "Article not found" });
                }

                return Ok(new { message = "View count incremented" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing view count for article {ArticleId}", id);
                return StatusCode(500, new { error = "Unable to increment view count" });
            }
        }

        [HttpPost("{id:int}/click")]
        public async Task<IActionResult> IncrementClickThrough(int id)
        {
            try
            {
                var success = await _contentService.IncrementClickThroughCountAsync(id);
                if (!success)
                {
                    return NotFound(new { error = "Article not found" });
                }

                return Ok(new { message = "Click-through count incremented" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing click-through count for article {ArticleId}", id);
                return StatusCode(500, new { error = "Unable to increment click-through count" });
            }
        }

        private static IEnumerable<ArticleDto> MapToDto(IEnumerable<Article> articles)
        {
            return articles.Select(a => new ArticleDto
            {
                Id = a.Id,
                Title = a.Title,
                Slug = a.Slug,
                Summary = a.Summary,
                ImageUrl = a.ImageUrl,
                ImageAlt = a.ImageAlt,
                Author = a.Author,
                PublishedDate = a.PublishedDate,
                IsFeatured = a.IsFeatured,
                IsBreaking = a.IsBreaking,
                IsTrending = a.IsTrending,
                CategoryName = a.Category?.Name ?? "Uncategorized",
                CategorySlug = a.Category?.Slug ?? "uncategorized",
                CategoryColor = a.Category?.Color,
                CategoryIcon = a.Category?.Icon,
                SourceName = a.Source?.Name ?? "Unknown Source",
                ViewCount = a.ViewCount,
                ReadingTimeMinutes = a.Metadata?.ReadingTimeMinutes ?? CalculateReadingTime(a.Summary),
                TimeAgo = GetTimeAgo(a.PublishedDate)
            });
        }

        private static int CalculateReadingTime(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return 1;

            var wordCount = text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
            const int wordsPerMinute = 200; // Average reading speed
            return Math.Max(1, (int)Math.Ceiling((double)wordCount / wordsPerMinute));
        }

        private static string GetTimeAgo(DateTime publishedDate)
        {
            var timeSpan = DateTime.UtcNow - publishedDate;

            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} minutes ago";
            
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} hours ago";
            
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} days ago";
            
            if (timeSpan.TotalDays < 30)
                return $"{(int)(timeSpan.TotalDays / 7)} weeks ago";
            
            if (timeSpan.TotalDays < 365)
                return $"{(int)(timeSpan.TotalDays / 30)} months ago";
            
            return $"{(int)(timeSpan.TotalDays / 365)} years ago";
        }
    }
}