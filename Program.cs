using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using NewsSite.Data;
using NewsSite.Services;
using NewsSite.Services.BackgroundServices;
using NewsSite.Services.External;
using NewsSite.Services.Utilities;
using Hangfire;
using Hangfire.SqlServer;
using Hangfire.Dashboard;
using Azure.Identity;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.ResponseCompression;
using AspNetCoreRateLimit;
using System.IO.Compression;

var builder = WebApplication.CreateBuilder(args);

// Configure Azure Key Vault for production
if (builder.Environment.IsProduction())
{
    var keyVaultUri = builder.Configuration["KeyVault:VaultUri"];
    if (!string.IsNullOrEmpty(keyVaultUri))
    {
        builder.Configuration.AddAzureKeyVault(
            new Uri(keyVaultUri),
            new DefaultAzureCredential());
    }
}

// Add Application Insights
builder.Services.AddApplicationInsightsTelemetry(options =>
{
    options.ConnectionString = builder.Configuration["ApplicationInsights:ConnectionString"];
});

// Configure Response Compression
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
    options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(new[]
    {
        "application/json",
        "text/css",
        "text/javascript",
        "application/javascript",
        "text/xml",
        "application/xml"
    });
});

builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Optimal;
});

builder.Services.Configure<GzipCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Optimal;
});

// Configure Rate Limiting
builder.Services.AddMemoryCache();
builder.Services.Configure<IpRateLimitOptions>(builder.Configuration.GetSection("IpRateLimiting"));
builder.Services.Configure<IpRateLimitPolicies>(builder.Configuration.GetSection("IpRateLimitPolicies"));
builder.Services.AddInMemoryRateLimiting();
builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();

// Add services to the container.
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseSqlServer(connectionString, sqlOptions =>
    {
        sqlOptions.CommandTimeout(builder.Configuration.GetValue<int>("Database:CommandTimeout", 60));
        sqlOptions.EnableRetryOnFailure(
            maxRetryCount: builder.Configuration.GetValue<int>("Database:MaxRetryCount", 3),
            maxRetryDelay: TimeSpan.Parse(builder.Configuration["Database:MaxRetryDelay"] ?? "00:00:30"),
            errorNumbersToAdd: null);
    });

    if (builder.Environment.IsProduction())
    {
        options.EnableSensitiveDataLogging(false);
        options.EnableDetailedErrors(false);
    }
});

builder.Services.AddDatabaseDeveloperPageExceptionFilter();

builder.Services.AddDefaultIdentity<IdentityUser>(options => 
{
    options.SignIn.RequireConfirmedAccount = !builder.Environment.IsDevelopment();
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequiredLength = 8;
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.User.RequireUniqueEmail = true;
})
    .AddRoles<IdentityRole>()
    .AddEntityFrameworkStores<ApplicationDbContext>();

// Configure Data Protection for Azure
if (builder.Environment.IsProduction())
{
    var keyVaultUri = builder.Configuration["KeyVault:VaultUri"];
    var storageConnectionString = builder.Configuration.GetConnectionString("DataProtectionStorage");
    
    if (!string.IsNullOrEmpty(keyVaultUri) && !string.IsNullOrEmpty(storageConnectionString))
    {
        builder.Services.AddDataProtection()
            .PersistKeysToAzureBlobStorage(storageConnectionString, "dataprotection", "keys.xml")
            .ProtectKeysWithAzureKeyVault(new Uri($"{keyVaultUri}keys/dataprotection"), new DefaultAzureCredential())
            .SetApplicationName("AIFrontiers");
    }
}

// Add memory cache for service caching
builder.Services.AddMemoryCache(options =>
{
    if (builder.Environment.IsProduction())
    {
        options.SizeLimit = builder.Configuration.GetValue<int>("Performance:MemoryCacheSizeLimitMB", 100) * 1024 * 1024;
    }
});

// Configure Hangfire
builder.Services.AddHangfire(configuration => configuration
    .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UseSqlServerStorage(connectionString, new SqlServerStorageOptions
    {
        CommandBatchMaxTimeout = TimeSpan.FromMinutes(5),
        SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
        QueuePollInterval = TimeSpan.Zero,
        UseRecommendedIsolationLevel = true,
        DisableGlobalLocks = true
    }));

builder.Services.AddHangfireServer();

// Add HTTP client services
builder.Services.AddHttpClient();

// Register business services
builder.Services.AddScoped<IContentService, ContentService>();
builder.Services.AddScoped<ICategoryService, CategoryService>();
builder.Services.AddScoped<ISourceService, SourceService>();
builder.Services.AddScoped<ITagService, TagService>();
builder.Services.AddScoped<ISearchService, SearchService>();

// Register utility services
builder.Services.AddScoped<IHttpService, HttpService>();
builder.Services.AddScoped<IContentParsingService, ContentParsingService>();
builder.Services.AddScoped<ISlugService, SlugService>();

// Register external API services
builder.Services.AddScoped<IRssService, RssService>();
builder.Services.AddScoped<IYouTubeService, YouTubeService>();
builder.Services.AddScoped<IGitHubService, GitHubService>();
builder.Services.AddScoped<IArxivService, ArxivService>();

// Register background services
builder.Services.AddScoped<IContentAggregationService, ContentAggregationService>();
builder.Services.AddScoped<IContentProcessor, ContentProcessor>();
builder.Services.AddScoped<IDuplicateDetectionService, DuplicateDetectionService>();
builder.Services.AddScoped<ContentSyncJob>();

// Register data seeder service
builder.Services.AddScoped<DataSeederService>();

// Add Health Checks
builder.Services.AddHealthChecks()
    .AddDbContext<ApplicationDbContext>()
    .AddSqlServer(connectionString, name: "database")
    .AddHangfire(options => options.MinimumAvailableServers = 1, name: "hangfire");

// Add Response Caching
builder.Services.AddResponseCaching();

builder.Services.AddControllersWithViews(options =>
{
    if (builder.Environment.IsProduction())
    {
        options.Filters.Add(new Microsoft.AspNetCore.Mvc.RequireHttpsAttribute());
    }
});

var app = builder.Build();

// Seed initial data
using (var scope = app.Services.CreateScope())
{
    var seeder = scope.ServiceProvider.GetRequiredService<DataSeederService>();
    await seeder.SeedRolesAndAdminUserAsync();
    await seeder.SeedCategoriesAsync();
    await seeder.SeedSampleArticlesAsync();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseMigrationsEndPoint();
}
else
{
    app.UseExceptionHandler("/Home/Error");
    
    // Configure HSTS for production
    app.UseHsts();
    
    // Security headers
    app.Use(async (context, next) =>
    {
        context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
        context.Response.Headers.Append("X-Frame-Options", "DENY");
        context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
        context.Response.Headers.Append("Referrer-Policy", "strict-origin-when-cross-origin");
        context.Response.Headers.Append("Content-Security-Policy", 
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com; " +
            "style-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com fonts.googleapis.com; " +
            "font-src 'self' fonts.googleapis.com fonts.gstatic.com; " +
            "img-src 'self' data: https:; " +
            "connect-src 'self'");
        
        await next();
    });
}

// Enable response compression
app.UseResponseCompression();

// Enable rate limiting
app.UseIpRateLimiting();

app.UseHttpsRedirection();

// Configure static files with caching
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        if (app.Environment.IsProduction())
        {
            ctx.Context.Response.Headers.Append("Cache-Control", 
                $"public,max-age={60 * 60 * 24 * 30}"); // 30 days
        }
    }
});

// Enable response caching
app.UseResponseCaching();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

// Configure Hangfire Dashboard
app.UseHangfireDashboard("/hangfire", new DashboardOptions
{
    Authorization = new[] { new HangfireAuthorizationFilter() }
});

// Custom route for category pages (must come before default route)
app.MapControllerRoute(
    name: "category",
    pattern: "{slug}",
    defaults: new { controller = "Category", action = "Index" },
    constraints: new { slug = @"^(breaking-ai-news|youtube-ai-discoveries|research-papers|agentic-platforms|ai-development-tools|trending-open-source)$" });

// Admin area route
app.MapControllerRoute(
    name: "admin",
    pattern: "admin/{controller=Dashboard}/{action=Index}/{id?}",
    defaults: new { area = "Admin" });

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");
app.MapRazorPages();

// Add Health Check endpoint
app.MapHealthChecks("/health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    ResponseWriter = async (context, report) =>
    {
        context.Response.ContentType = "application/json";
        var response = new
        {
            status = report.Status.ToString(),
            checks = report.Entries.Select(x => new
            {
                name = x.Key,
                status = x.Value.Status.ToString(),
                exception = x.Value.Exception?.Message,
                duration = x.Value.Duration.ToString()
            }),
            duration = report.TotalDuration.ToString()
        };
        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }
});

// Setup recurring jobs
using (var scope = app.Services.CreateScope())
{
    var recurringJobManager = scope.ServiceProvider.GetRequiredService<IRecurringJobManager>();
    
    // Hourly content sync
    recurringJobManager.AddOrUpdate<ContentSyncJob>(
        "content-sync-hourly",
        job => job.ExecuteAsync(),
        Cron.Hourly);
    
    // Daily cleanup jobs
    recurringJobManager.AddOrUpdate<ContentSyncJob>(
        "content-cleanup-daily",
        job => job.CleanupOldContentAsync(),
        Cron.Daily);
    
    // Weekly analytics calculation
    recurringJobManager.AddOrUpdate<ContentSyncJob>(
        "analytics-weekly",
        job => job.CalculateAnalyticsAsync(),
        Cron.Weekly);
}

app.Run();

// Hangfire Authorization Filter
public class HangfireAuthorizationFilter : IDashboardAuthorizationFilter
{
    public bool Authorize(DashboardContext context)
    {
        var httpContext = context.GetHttpContext();
        
        // Allow access only to authenticated admin users
        return httpContext.User.Identity?.IsAuthenticated == true && 
               httpContext.User.IsInRole("Admin");
    }
}
