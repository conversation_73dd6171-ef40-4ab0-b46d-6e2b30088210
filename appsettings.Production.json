{
  "ConnectionStrings": {
    "DefaultConnection": ""
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning",
      "Hangfire": "Warning",
      "NewsSite": "Information"
    },
    "ApplicationInsights": {
      "LogLevel": {
        "Default": "Information",
        "Microsoft.AspNetCore": "Warning"
      }
    }
  },
  "AllowedHosts": "*.azurewebsites.net;aifrontiers.com;www.aifrontiers.com",
  
  "ApplicationInsights": {
    "ConnectionString": ""
  },
  
  "KeyVault": {
    "VaultUri": "https://aifrontiers-kv.vault.azure.net/"
  },
  
  // External API Configuration - Keys stored in Key Vault
  "YouTube": {
    "ApiKey": ""
  },
  "GitHub": {
    "ApiToken": ""
  },
  
  // Background Job Configuration - Production Settings
  "Hangfire": {
    "DashboardPath": "/admin/jobs",
    "ServerName": "AIFrontiers-Production",
    "WorkerCount": 4,
    "MaxDegreeOfParallelism": 2,
    "SchedulePollingInterval": "00:01:00"
  },
  
  // Content Aggregation Settings - Production Optimized
  "ContentAggregation": {
    "DefaultUpdateIntervalMinutes": 30,
    "MaxConcurrentRequests": 5,
    "RequestTimeoutSeconds": 60,
    "MaxRetryAttempts": 3,
    "RateLimitDelayMs": 500,
    "MaxArticlesPerSource": 50,
    "EnableContentCaching": true,
    "CacheDurationMinutes": 15
  },
  
  // Security Configuration
  "Security": {
    "RequireHttps": true,
    "EnableHsts": true,
    "HstsMaxAge": 31536000,
    "EnableContentSecurityPolicy": true,
    "EnableRateLimiting": true,
    "MaxRequestsPerMinute": 60
  },
  
  // Performance Configuration
  "Performance": {
    "EnableResponseCompression": true,
    "EnableResponseCaching": true,
    "StaticFilesCacheMaxAge": 2592000,
    "EnableMemoryCache": true,
    "MemoryCacheSizeLimitMB": 100
  },
  
  // Database Configuration
  "Database": {
    "CommandTimeout": 60,
    "MaxRetryCount": 3,
    "MaxRetryDelay": "00:00:30",
    "EnableSensitiveDataLogging": false,
    "EnableDetailedErrors": false,
    "ConnectionPoolSize": 100
  },
  
  // Email Configuration (for Identity)
  "Email": {
    "SmtpServer": "",
    "SmtpPort": 587,
    "SmtpUsername": "",
    "SmtpPassword": "",
    "FromEmail": "<EMAIL>",
    "FromName": "AI Frontiers"
  },
  
  // CDN Configuration
  "CDN": {
    "BaseUrl": "https://aifrontiers.azureedge.net",
    "EnableCDN": true,
    "StaticAssetsPath": "/static"
  },
  
  // Monitoring and Health Checks
  "HealthChecks": {
    "EnableDetailedErrors": false,
    "Timeout": "00:00:30"
  },
  
  // Rate Limiting Configuration
  "IpRateLimiting": {
    "EnableEndpointRateLimiting": false,
    "StackBlockedRequests": false,
    "RealIpHeader": "X-Real-IP",
    "ClientIdHeader": "X-ClientId",
    "HttpStatusCode": 429,
    "GeneralRules": [
      {
        "Endpoint": "*",
        "Period": "1m",
        "Limit": 60
      },
      {
        "Endpoint": "*",
        "Period": "15m",
        "Limit": 300
      },
      {
        "Endpoint": "*",
        "Period": "12h",
        "Limit": 5000
      },
      {
        "Endpoint": "*",
        "Period": "7d",
        "Limit": 50000
      }
    ]
  },
  
  "IpRateLimitPolicies": {
    "IpRules": [
      {
        "Ip": "127.0.0.1",
        "Rules": [
          {
            "Endpoint": "*",
            "Period": "1m",
            "Limit": 1000
          }
        ]
      }
    ]
  }
}