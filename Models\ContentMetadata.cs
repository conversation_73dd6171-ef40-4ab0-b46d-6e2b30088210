using System.ComponentModel.DataAnnotations;

namespace NewsSite.Models
{
    public class ContentMetadata
    {
        public int Id { get; set; }

        [Required]
        public int ArticleId { get; set; }

        // Duplicate detection
        [Required]
        [MaxLength(64)]
        public string ContentHash { get; set; } = string.Empty; // SHA-256 hash of title + summary

        [MaxLength(64)]
        public string? UrlHash { get; set; } // Hash of the original URL

        // Engagement metrics
        public int ViewCount { get; set; } = 0;
        public int ShareCount { get; set; } = 0;
        public int LikeCount { get; set; } = 0;
        public int CommentCount { get; set; } = 0;

        // Click tracking
        public int ClickThroughCount { get; set; } = 0;
        public DateTime? LastClickDate { get; set; }

        // Content analysis
        public int WordCount { get; set; } = 0;
        public int ReadingTimeMinutes { get; set; } = 0;

        [MaxLength(10)]
        public string? Language { get; set; } // ISO language code

        // Quality metrics
        public decimal QualityScore { get; set; } = 0; // 0-100 quality rating
        public bool HasImages { get; set; } = false;
        public bool HasVideo { get; set; } = false;

        // SEO data
        [MaxLength(1000)]
        public string? MetaKeywords { get; set; }

        [MaxLength(500)]
        public string? MetaDescription { get; set; }

        // Social media metrics (if available from source)
        public int? ExternalLikes { get; set; }
        public int? ExternalShares { get; set; }
        public int? ExternalComments { get; set; }

        // Trending calculation
        public decimal TrendingScore { get; set; } = 0;
        public DateTime? TrendingCalculatedDate { get; set; }

        // Duplicate detection results
        public bool IsDuplicate { get; set; } = false;
        public int? OriginalArticleId { get; set; } // Reference to original if this is a duplicate

        // Audit fields
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime? ModifiedDate { get; set; }

        // Navigation properties
        public virtual Article Article { get; set; } = null!;
        public virtual Article? OriginalArticle { get; set; }
    }
}