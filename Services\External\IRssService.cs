using NewsSite.Models;

namespace NewsSite.Services.External
{
    public interface IRssService
    {
        /// <summary>
        /// Fetches articles from an RSS feed URL
        /// </summary>
        Task<List<Article>> FetchArticlesAsync(string rssUrl);

        /// <summary>
        /// Validates if a URL is a valid RSS feed
        /// </summary>
        Task<bool> ValidateRssFeedAsync(string rssUrl);

        /// <summary>
        /// Gets RSS feed metadata (title, description, etc.)
        /// </summary>
        Task<RssFeedInfo?> GetFeedInfoAsync(string rssUrl);

        /// <summary>
        /// Fetches articles from multiple RSS feeds concurrently
        /// </summary>
        Task<List<Article>> FetchFromMultipleFeedsAsync(List<string> rssUrls);
    }

    public class RssFeedInfo
    {
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Link { get; set; } = "";
        public DateTime LastBuildDate { get; set; }
        public string Language { get; set; } = "";
        public string Generator { get; set; } = "";
        public int ItemCount { get; set; }
    }
}