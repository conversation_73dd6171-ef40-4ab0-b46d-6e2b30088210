using System.Text;
using System.Text.Json;
using System.Net;

namespace NewsSite.Services.Utilities
{
    public class HttpService : IHttpService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<HttpService> _logger;
        private readonly SemaphoreSlim _rateLimitSemaphore;
        
        // Rate limiting settings
        private const int MAX_CONCURRENT_REQUESTS = 10;
        private const int RATE_LIMIT_DELAY_MS = 100; // Minimum delay between requests
        private DateTime _lastRequestTime = DateTime.MinValue;
        private readonly object _rateLimitLock = new object();

        // Retry settings
        private const int MAX_RETRY_ATTEMPTS = 3;
        private const int BASE_RETRY_DELAY_MS = 1000;

        public HttpService(HttpClient httpClient, ILogger<HttpService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _rateLimitSemaphore = new SemaphoreSlim(MAX_CONCURRENT_REQUESTS, MAX_CONCURRENT_REQUESTS);

            // Configure HttpClient defaults
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "NewsSite-ContentAggregator/1.0 (compatible; .NET/8.0)");
        }

        public async Task<string> GetStringAsync(string url, Dictionary<string, string>? headers = null)
        {
            await _rateLimitSemaphore.WaitAsync();
            
            try
            {
                await ApplyRateLimit();
                
                return await ExecuteWithRetryAsync(async () =>
                {
                    using var request = new HttpRequestMessage(HttpMethod.Get, url);
                    AddHeaders(request, headers);
                    
                    _logger.LogDebug("Sending GET request to: {Url}", url);
                    
                    using var response = await _httpClient.SendAsync(request);
                    
                    if (!response.IsSuccessStatusCode)
                    {
                        _logger.LogWarning("HTTP request failed: {StatusCode} - {Url}", 
                            response.StatusCode, url);
                        
                        if (response.StatusCode == HttpStatusCode.TooManyRequests)
                        {
                            // Respect rate limiting
                            var retryAfter = response.Headers.RetryAfter?.Delta ?? TimeSpan.FromSeconds(60);
                            _logger.LogInformation("Rate limited, waiting {Seconds} seconds", retryAfter.TotalSeconds);
                            await Task.Delay(retryAfter);
                        }
                        
                        response.EnsureSuccessStatusCode();
                    }
                    
                    var content = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("Successfully received {Length} characters from: {Url}", 
                        content.Length, url);
                    
                    return content;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing HTTP GET request to: {Url}", url);
                throw;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        public async Task<byte[]> GetByteArrayAsync(string url, Dictionary<string, string>? headers = null)
        {
            await _rateLimitSemaphore.WaitAsync();
            
            try
            {
                await ApplyRateLimit();
                
                return await ExecuteWithRetryAsync(async () =>
                {
                    using var request = new HttpRequestMessage(HttpMethod.Get, url);
                    AddHeaders(request, headers);
                    
                    _logger.LogDebug("Sending GET request for binary data to: {Url}", url);
                    
                    using var response = await _httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();
                    
                    var content = await response.Content.ReadAsByteArrayAsync();
                    _logger.LogDebug("Successfully received {Length} bytes from: {Url}", 
                        content.Length, url);
                    
                    return content;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing HTTP GET request for binary data to: {Url}", url);
                throw;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        public async Task<string> PostAsync(string url, string content, Dictionary<string, string>? headers = null)
        {
            await _rateLimitSemaphore.WaitAsync();
            
            try
            {
                await ApplyRateLimit();
                
                return await ExecuteWithRetryAsync(async () =>
                {
                    using var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Content = new StringContent(content, Encoding.UTF8, "application/x-www-form-urlencoded");
                    AddHeaders(request, headers);
                    
                    _logger.LogDebug("Sending POST request to: {Url}", url);
                    
                    using var response = await _httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();
                    
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("Successfully received POST response from: {Url}", url);
                    
                    return responseContent;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing HTTP POST request to: {Url}", url);
                throw;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        public async Task<string> PostJsonAsync(string url, object data, Dictionary<string, string>? headers = null)
        {
            var jsonContent = JsonSerializer.Serialize(data);
            
            await _rateLimitSemaphore.WaitAsync();
            
            try
            {
                await ApplyRateLimit();
                
                return await ExecuteWithRetryAsync(async () =>
                {
                    using var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                    AddHeaders(request, headers);
                    
                    _logger.LogDebug("Sending POST JSON request to: {Url}", url);
                    
                    using var response = await _httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();
                    
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogDebug("Successfully received POST JSON response from: {Url}", url);
                    
                    return responseContent;
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing HTTP POST JSON request to: {Url}", url);
                throw;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        public async Task<bool> IsUrlAccessibleAsync(string url)
        {
            try
            {
                await _rateLimitSemaphore.WaitAsync(TimeSpan.FromSeconds(5));
                
                try
                {
                    await ApplyRateLimit();
                    
                    using var request = new HttpRequestMessage(HttpMethod.Head, url);
                    using var response = await _httpClient.SendAsync(request);
                    
                    _logger.LogDebug("URL accessibility check for {Url}: {StatusCode}", 
                        url, response.StatusCode);
                    
                    return response.IsSuccessStatusCode;
                }
                finally
                {
                    _rateLimitSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "URL not accessible: {Url}", url);
                return false;
            }
        }

        public async Task<Dictionary<string, string>> GetResponseHeadersAsync(string url)
        {
            var headers = new Dictionary<string, string>();
            
            try
            {
                await _rateLimitSemaphore.WaitAsync();
                
                try
                {
                    await ApplyRateLimit();
                    
                    using var request = new HttpRequestMessage(HttpMethod.Head, url);
                    using var response = await _httpClient.SendAsync(request);
                    
                    foreach (var header in response.Headers)
                    {
                        headers[header.Key] = string.Join(", ", header.Value);
                    }
                    
                    foreach (var header in response.Content.Headers)
                    {
                        headers[header.Key] = string.Join(", ", header.Value);
                    }
                    
                    _logger.LogDebug("Retrieved {Count} headers from: {Url}", headers.Count, url);
                }
                finally
                {
                    _rateLimitSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting response headers from: {Url}", url);
            }
            
            return headers;
        }

        public async Task<byte[]> DownloadWithProgressAsync(string url, IProgress<double>? progress = null)
        {
            await _rateLimitSemaphore.WaitAsync();
            
            try
            {
                await ApplyRateLimit();
                
                using var request = new HttpRequestMessage(HttpMethod.Get, url);
                using var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                
                response.EnsureSuccessStatusCode();
                
                var totalBytes = response.Content.Headers.ContentLength ?? -1;
                var downloadedBytes = 0L;
                
                using var contentStream = await response.Content.ReadAsStreamAsync();
                using var memoryStream = new MemoryStream();
                
                var buffer = new byte[8192];
                int bytesRead;
                
                while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    await memoryStream.WriteAsync(buffer, 0, bytesRead);
                    downloadedBytes += bytesRead;
                    
                    if (totalBytes > 0 && progress != null)
                    {
                        var progressPercentage = (double)downloadedBytes / totalBytes * 100;
                        progress.Report(progressPercentage);
                    }
                }
                
                _logger.LogDebug("Downloaded {Size} bytes from: {Url}", downloadedBytes, url);
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading with progress from: {Url}", url);
                throw;
            }
            finally
            {
                _rateLimitSemaphore.Release();
            }
        }

        private async Task ApplyRateLimit()
        {
            lock (_rateLimitLock)
            {
                var timeSinceLastRequest = DateTime.UtcNow - _lastRequestTime;
                if (timeSinceLastRequest.TotalMilliseconds < RATE_LIMIT_DELAY_MS)
                {
                    var delayTime = RATE_LIMIT_DELAY_MS - (int)timeSinceLastRequest.TotalMilliseconds;
                    if (delayTime > 0)
                    {
                        Thread.Sleep(delayTime);
                    }
                }
                _lastRequestTime = DateTime.UtcNow;
            }
        }

        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation)
        {
            var attempt = 0;
            
            while (true)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (attempt < MAX_RETRY_ATTEMPTS - 1 && IsRetriableException(ex))
                {
                    attempt++;
                    var delay = TimeSpan.FromMilliseconds(BASE_RETRY_DELAY_MS * Math.Pow(2, attempt - 1));
                    
                    _logger.LogWarning(ex, "Request failed (attempt {Attempt}/{MaxAttempts}), retrying in {Delay}ms", 
                        attempt, MAX_RETRY_ATTEMPTS, delay.TotalMilliseconds);
                    
                    await Task.Delay(delay);
                }
            }
        }

        private bool IsRetriableException(Exception ex)
        {
            return ex is HttpRequestException ||
                   ex is TaskCanceledException ||
                   (ex is HttpRequestException httpEx && 
                    httpEx.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase));
        }

        private void AddHeaders(HttpRequestMessage request, Dictionary<string, string>? headers)
        {
            if (headers == null) return;
            
            foreach (var header in headers)
            {
                try
                {
                    if (header.Key.Equals("content-type", StringComparison.OrdinalIgnoreCase))
                    {
                        // Content-Type is handled by the content object
                        continue;
                    }
                    
                    request.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to add header {HeaderName}: {HeaderValue}", 
                        header.Key, header.Value);
                }
            }
        }

        public void Dispose()
        {
            _rateLimitSemaphore?.Dispose();
        }
    }
}