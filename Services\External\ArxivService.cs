using NewsSite.Models;
using NewsSite.Services.Utilities;
using System.Text.RegularExpressions;
using System.Xml;
using System.Web;

namespace NewsSite.Services.External
{
    public class ArxivService : IArxivService
    {
        private readonly IHttpService _httpService;
        private readonly ILogger<ArxivService> _logger;

        private const string ARXIV_API_BASE_URL = "http://export.arxiv.org/api/query";
        
        // Common arXiv categories for AI/ML and Computer Science
        private static readonly Dictionary<string, string> PopularCategories = new()
        {
            ["cs.AI"] = "Artificial Intelligence",
            ["cs.LG"] = "Machine Learning",
            ["cs.CV"] = "Computer Vision and Pattern Recognition",
            ["cs.CL"] = "Computation and Language",
            ["cs.NE"] = "Neural and Evolutionary Computing",
            ["stat.ML"] = "Machine Learning (Statistics)",
            ["cs.RO"] = "Robotics",
            ["cs.IR"] = "Information Retrieval",
            ["cs.CR"] = "Cryptography and Security",
            ["cs.SE"] = "Software Engineering",
            ["cs.DB"] = "Databases",
            ["cs.DS"] = "Data Structures and Algorithms"
        };

        public ArxivService(
            IHttpService httpService,
            ILogger<ArxivService> logger)
        {
            _httpService = httpService;
            _logger = logger;
        }

        public async Task<List<Article>> FetchPapersAsync(SourceConfiguration configuration)
        {
            var articles = new List<Article>();

            try
            {
                if (configuration.ApiSettings == null)
                {
                    _logger.LogWarning("arXiv source configuration missing API settings");
                    return articles;
                }

                var sourceType = configuration.ApiSettings.GetValueOrDefault("type", "category");
                
                switch (sourceType.ToLower())
                {
                    case "category":
                        var category = configuration.ApiSettings.GetValueOrDefault("category", "cs.AI");
                        articles = await FetchByCategoryAsync(category, configuration.MaxItems ?? 25);
                        break;
                        
                    case "search":
                        var query = configuration.ApiSettings.GetValueOrDefault("query", "");
                        if (!string.IsNullOrEmpty(query))
                        {
                            articles = await SearchPapersAsync(query, configuration.MaxItems ?? 25);
                        }
                        break;
                        
                    case "recent":
                        // Fetch recent papers from multiple AI/ML categories
                        var categories = configuration.ApiSettings.GetValueOrDefault("categories", "cs.AI,cs.LG,cs.CV")
                            .Split(',', StringSplitOptions.RemoveEmptyEntries);
                        
                        foreach (var cat in categories.Take(3)) // Limit to avoid too many requests
                        {
                            var categoryArticles = await FetchByCategoryAsync(cat.Trim(), (configuration.MaxItems ?? 25) / categories.Length);
                            articles.AddRange(categoryArticles);
                        }
                        break;
                        
                    default:
                        _logger.LogWarning("Unsupported arXiv source type: {SourceType}", sourceType);
                        break;
                }

                _logger.LogInformation("Fetched {Count} papers from arXiv source", articles.Count);
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching papers from arXiv");
                return articles;
            }
        }

        public async Task<List<Article>> SearchPapersAsync(string query, int maxResults = 25)
        {
            var articles = new List<Article>();

            try
            {
                _logger.LogDebug("Searching arXiv papers: {Query}", query);

                var searchQuery = $"search_query=all:{HttpUtility.UrlEncode(query)}" +
                                $"&start=0" +
                                $"&max_results={Math.Min(maxResults, 1000)}" +
                                $"&sortBy=submittedDate" +
                                $"&sortOrder=descending";

                var url = $"{ARXIV_API_BASE_URL}?{searchQuery}";
                var response = await _httpService.GetStringAsync(url);
                
                if (string.IsNullOrEmpty(response))
                {
                    _logger.LogWarning("Empty response from arXiv search API");
                    return articles;
                }

                articles = ParseArxivXmlResponse(response);
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching arXiv papers: {Query}", query);
                return articles;
            }
        }

        public async Task<List<Article>> FetchByCategoryAsync(string category, int maxResults = 25)
        {
            var articles = new List<Article>();

            try
            {
                _logger.LogDebug("Fetching arXiv papers by category: {Category}", category);

                var searchQuery = $"search_query=cat:{category}" +
                                $"&start=0" +
                                $"&max_results={Math.Min(maxResults, 1000)}" +
                                $"&sortBy=submittedDate" +
                                $"&sortOrder=descending";

                var url = $"{ARXIV_API_BASE_URL}?{searchQuery}";
                var response = await _httpService.GetStringAsync(url);
                
                if (string.IsNullOrEmpty(response))
                {
                    _logger.LogWarning("Empty response from arXiv category API");
                    return articles;
                }

                articles = ParseArxivXmlResponse(response);
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching arXiv papers by category: {Category}", category);
                return articles;
            }
        }

        public async Task<ArxivPaperInfo?> GetPaperInfoAsync(string arxivId)
        {
            try
            {
                _logger.LogDebug("Getting arXiv paper info: {ArxivId}", arxivId);

                if (!ValidateArxivId(arxivId))
                {
                    _logger.LogWarning("Invalid arXiv ID format: {ArxivId}", arxivId);
                    return null;
                }

                var searchQuery = $"search_query=id:{arxivId}";
                var url = $"{ARXIV_API_BASE_URL}?{searchQuery}";
                var response = await _httpService.GetStringAsync(url);
                
                if (string.IsNullOrEmpty(response))
                {
                    return null;
                }

                var papers = ParseArxivXmlResponse(response);
                if (papers.Any())
                {
                    var paper = papers.First();
                    return ConvertArticleToArxivInfo(paper, arxivId);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting arXiv paper info: {ArxivId}", arxivId);
                return null;
            }
        }

        public bool ValidateArxivId(string arxivId)
        {
            if (string.IsNullOrEmpty(arxivId))
                return false;

            // arXiv ID formats:
            // Old format: subject-class/YYMMnnn (e.g., hep-th/9901001)
            // New format: YYMM.nnnn (e.g., 1501.00001) or YYMMnnnnn (e.g., 150100001)
            var oldFormatPattern = @"^[a-z-]+/\d{7}$";
            var newFormatPattern = @"^\d{4}\.\d{4,5}(v\d+)?$";

            return Regex.IsMatch(arxivId, oldFormatPattern, RegexOptions.IgnoreCase) ||
                   Regex.IsMatch(arxivId, newFormatPattern);
        }

        private List<Article> ParseArxivXmlResponse(string xmlContent)
        {
            var articles = new List<Article>();

            try
            {
                var doc = new XmlDocument();
                doc.LoadXml(xmlContent);

                var namespaceManager = new XmlNamespaceManager(doc.NameTable);
                namespaceManager.AddNamespace("atom", "http://www.w3.org/2005/Atom");
                namespaceManager.AddNamespace("arxiv", "http://arxiv.org/schemas/atom");

                var entries = doc.SelectNodes("//atom:entry", namespaceManager);
                if (entries == null)
                {
                    return articles;
                }

                foreach (XmlNode entry in entries)
                {
                    try
                    {
                        var article = ParseArxivEntry(entry, namespaceManager);
                        if (article != null)
                        {
                            articles.Add(article);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error parsing arXiv entry");
                    }
                }

                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing arXiv XML response");
                return articles;
            }
        }

        private Article? ParseArxivEntry(XmlNode entry, XmlNamespaceManager namespaceManager)
        {
            try
            {
                var id = entry.SelectSingleNode("atom:id", namespaceManager)?.InnerText;
                var title = entry.SelectSingleNode("atom:title", namespaceManager)?.InnerText;
                var summary = entry.SelectSingleNode("atom:summary", namespaceManager)?.InnerText;
                var published = entry.SelectSingleNode("atom:published", namespaceManager)?.InnerText;
                var updated = entry.SelectSingleNode("atom:updated", namespaceManager)?.InnerText;

                if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(title))
                {
                    return null;
                }

                // Extract arXiv ID from the full URL
                var arxivId = ExtractArxivIdFromUrl(id);
                
                // Get authors
                var authors = new List<string>();
                var authorNodes = entry.SelectNodes("atom:author/atom:name", namespaceManager);
                if (authorNodes != null)
                {
                    foreach (XmlNode authorNode in authorNodes)
                    {
                        if (!string.IsNullOrEmpty(authorNode.InnerText))
                        {
                            authors.Add(authorNode.InnerText);
                        }
                    }
                }

                // Get categories
                var categories = new List<string>();
                var categoryNodes = entry.SelectNodes("atom:category", namespaceManager);
                if (categoryNodes != null)
                {
                    foreach (XmlNode categoryNode in categoryNodes)
                    {
                        var term = categoryNode.Attributes?["term"]?.Value;
                        if (!string.IsNullOrEmpty(term))
                        {
                            categories.Add(term);
                        }
                    }
                }

                // Get PDF link
                var pdfUrl = "";
                var linkNodes = entry.SelectNodes("atom:link", namespaceManager);
                if (linkNodes != null)
                {
                    foreach (XmlNode linkNode in linkNodes)
                    {
                        var type = linkNode.Attributes?["type"]?.Value;
                        var href = linkNode.Attributes?["href"]?.Value;
                        
                        if (type == "application/pdf" && !string.IsNullOrEmpty(href))
                        {
                            pdfUrl = href;
                            break;
                        }
                    }
                }

                // Parse dates
                var publishedDate = DateTime.TryParse(published, out var pubDate) ? pubDate : DateTime.UtcNow;
                var updatedDate = DateTime.TryParse(updated, out var updDate) ? updDate : publishedDate;

                // Create content
                var content = BuildPaperContent(title, authors, categories, summary, arxivId, pdfUrl);

                return new Article
                {
                    Title = CleanTitle(title),
                    Content = content,
                    Summary = CleanSummary(summary, 300),
                    Url = $"https://arxiv.org/abs/{arxivId}",
                    ImageUrl = null, // arXiv papers typically don't have thumbnails
                    Author = authors.Any() ? string.Join(", ", authors.Take(3)) : "Unknown",
                    PublishedDate = publishedDate,
                    CreatedDate = DateTime.UtcNow,
                    ViewCount = 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing individual arXiv entry");
                return null;
            }
        }

        private string ExtractArxivIdFromUrl(string url)
        {
            // Extract ID from URLs like "http://arxiv.org/abs/2301.00001v1"
            var match = Regex.Match(url, @"arxiv\.org/abs/([^v\s]+)");
            return match.Success ? match.Groups[1].Value : url;
        }

        private string BuildPaperContent(string title, List<string> authors, List<string> categories, 
            string? summary, string arxivId, string pdfUrl)
        {
            var content = $"📄 Research Paper: {title}\n\n";
            
            if (authors.Any())
            {
                content += $"👥 Authors: <AUTHORS>
            }
            
            if (categories.Any())
            {
                var categoryNames = categories.Select(c => PopularCategories.GetValueOrDefault(c, c));
                content += $"🏷️ Categories: {string.Join(", ", categoryNames)}\n\n";
            }
            
            content += $"🆔 arXiv ID: {arxivId}\n";
            
            if (!string.IsNullOrEmpty(pdfUrl))
            {
                content += $"📥 PDF: {pdfUrl}\n";
            }
            
            content += $"🌐 Abstract: https://arxiv.org/abs/{arxivId}\n\n";
            
            if (!string.IsNullOrEmpty(summary))
            {
                content += $"📋 Abstract:\n{summary.Trim()}";
            }

            return content;
        }

        private string CleanTitle(string? title)
        {
            if (string.IsNullOrEmpty(title))
                return "";

            return title.Trim()
                .Replace("\n", " ")
                .Replace("\r", " ")
                .Replace("  ", " ");
        }

        private string CleanSummary(string? summary, int maxLength)
        {
            if (string.IsNullOrEmpty(summary))
                return "";

            var cleaned = summary.Trim()
                .Replace("\n", " ")
                .Replace("\r", " ")
                .Replace("  ", " ");

            if (cleaned.Length > maxLength)
            {
                cleaned = cleaned.Substring(0, maxLength - 3) + "...";
            }

            return cleaned;
        }

        private ArxivPaperInfo ConvertArticleToArxivInfo(Article article, string arxivId)
        {
            return new ArxivPaperInfo
            {
                Id = arxivId,
                Title = article.Title,
                Summary = article.Summary ?? "",
                Authors = article.Author?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(a => a.Trim()).ToList() ?? new List<string>(),
                Categories = new List<string>(), // Would need to extract from content
                Published = article.PublishedDate,
                Updated = article.PublishedDate,
                PdfUrl = $"https://arxiv.org/pdf/{arxivId}.pdf",
                AbstractUrl = article.Url,
                Comment = "",
                JournalRef = "",
                DOI = "",
                Links = new List<string> { article.Url }
            };
        }
    }
}