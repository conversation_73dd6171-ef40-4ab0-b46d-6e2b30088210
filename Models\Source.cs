using System.ComponentModel.DataAnnotations;

namespace NewsSite.Models
{
    public enum SourceType
    {
        RssFeed = 1,
        YouTubeChannel = 2,
        GitHubRepository = 3,
        ArXivCategory = 4,
        TwitterAccount = 5,
        Website = 6
    }

    public class Source
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public SourceType Type { get; set; }
        
        // String representation of source type for services
        public string SourceType => Type.ToString();

        [Required]
        [MaxLength(1000)]
        public string Url { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(200)]
        public string? ApiKey { get; set; } // For sources requiring API keys

        [MaxLength(200)]
        public string? ApiSecret { get; set; } // For OAuth sources

        public bool IsActive { get; set; } = true;

        public bool RequiresAuthentication { get; set; } = false;

        // Rate limiting
        public int MaxRequestsPerHour { get; set; } = 60;

        // Content filtering
        public bool EnableContentFiltering { get; set; } = true;

        [MaxLength(1000)]
        public string? ContentFilterKeywords { get; set; } // JSON array of keywords

        // Last sync information
        public DateTime? LastSyncDate { get; set; }
        public DateTime? LastSuccessfulSyncDate { get; set; }
        public int ConsecutiveFailures { get; set; } = 0;

        [MaxLength(1000)]
        public string? LastErrorMessage { get; set; }

        // Audit fields
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }

        // Navigation properties
        public virtual SourceConfiguration? Configuration { get; set; }
        public virtual ICollection<Article> Articles { get; set; } = new List<Article>();
    }
}