using HtmlAgilityPack;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace NewsSite.Services.Utilities
{
    public class ContentParsingService : IContentParsingService
    {
        private readonly ILogger<ContentParsingService> _logger;

        // HTML tags that should be completely removed (including content)
        private static readonly string[] DangerousTags = 
        {
            "script", "style", "iframe", "object", "embed", "applet", "form", 
            "input", "button", "select", "textarea", "link", "meta", "base"
        };

        // HTML tags that are safe to keep but should be stripped of attributes
        private static readonly string[] SafeTags = 
        {
            "p", "br", "div", "span", "h1", "h2", "h3", "h4", "h5", "h6",
            "strong", "b", "em", "i", "u", "ul", "ol", "li", "blockquote",
            "pre", "code", "a", "img"
        };

        // Attributes that are safe to keep
        private static readonly string[] SafeAttributes = 
        {
            "href", "src", "alt", "title", "target"
        };

        public ContentParsingService(ILogger<ContentParsingService> logger)
        {
            _logger = logger;
        }

        public async Task<string> ExtractTextFromHtmlAsync(string htmlContent)
        {
            try
            {
                if (string.IsNullOrEmpty(htmlContent))
                    return "";

                var doc = new HtmlDocument();
                doc.LoadHtml(htmlContent);

                // Remove script, style, and other unwanted elements
                var unwantedNodes = doc.DocumentNode
                    .SelectNodes($"//{string.Join(" | //", DangerousTags)}")
                    ?.ToList();

                if (unwantedNodes != null)
                {
                    foreach (var node in unwantedNodes)
                    {
                        node.Remove();
                    }
                }

                // Extract text content
                var textContent = doc.DocumentNode.InnerText;
                
                // Clean up the text
                textContent = HttpUtility.HtmlDecode(textContent);
                textContent = await NormalizeTextAsync(textContent);

                return textContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting text from HTML");
                return "";
            }
        }

        public async Task<string> SanitizeHtmlAsync(string htmlContent)
        {
            try
            {
                if (string.IsNullOrEmpty(htmlContent))
                    return "";

                var doc = new HtmlDocument();
                doc.LoadHtml(htmlContent);

                // Remove dangerous elements completely
                var dangerousNodes = doc.DocumentNode
                    .SelectNodes($"//{string.Join(" | //", DangerousTags)}")
                    ?.ToList();

                if (dangerousNodes != null)
                {
                    foreach (var node in dangerousNodes)
                    {
                        node.Remove();
                    }
                }

                // Process all remaining nodes
                var allNodes = doc.DocumentNode.SelectNodes("//*)").ToList();
                foreach (var node in allNodes)
                {
                    // Remove nodes that aren't in the safe list
                    if (!SafeTags.Contains(node.Name.ToLower()))
                    {
                        // Replace with its inner text/HTML
                        var parent = node.ParentNode;
                        var textNode = doc.CreateTextNode(node.InnerText);
                        parent.ReplaceChild(textNode, node);
                        continue;
                    }

                    // Remove unsafe attributes
                    var attributesToRemove = node.Attributes
                        .Where(attr => !SafeAttributes.Contains(attr.Name.ToLower()))
                        .ToList();

                    foreach (var attr in attributesToRemove)
                    {
                        node.Attributes.Remove(attr);
                    }

                    // Validate URLs in href and src attributes
                    if (node.Attributes["href"] != null)
                    {
                        var href = node.Attributes["href"].Value;
                        if (!IsValidUrl(href))
                        {
                            node.Attributes.Remove("href");
                        }
                    }

                    if (node.Attributes["src"] != null)
                    {
                        var src = node.Attributes["src"].Value;
                        if (!IsValidUrl(src))
                        {
                            node.Attributes.Remove("src");
                        }
                    }
                }

                return doc.DocumentNode.OuterHtml;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sanitizing HTML");
                return "";
            }
        }

        public async Task<string> GenerateSummaryAsync(string content, int maxLength = 300)
        {
            try
            {
                if (string.IsNullOrEmpty(content))
                    return "";

                // Clean the content first
                var cleanContent = await NormalizeTextAsync(content);
                
                if (cleanContent.Length <= maxLength)
                    return cleanContent;

                // Find the best breaking point (sentence end)
                var sentences = cleanContent.Split('.', '!', '?');
                var summary = new StringBuilder();
                
                foreach (var sentence in sentences)
                {
                    var trimmedSentence = sentence.Trim();
                    if (string.IsNullOrEmpty(trimmedSentence))
                        continue;

                    var potentialSummary = summary.ToString() + trimmedSentence + ". ";
                    
                    if (potentialSummary.Length > maxLength - 3) // Reserve space for "..."
                    {
                        break;
                    }
                    
                    summary.Append(trimmedSentence).Append(". ");
                }

                var result = summary.ToString().Trim();
                
                // If we couldn't find good sentence breaks, just truncate
                if (string.IsNullOrEmpty(result))
                {
                    result = cleanContent.Substring(0, Math.Min(maxLength - 3, cleanContent.Length));
                }
                
                // Add ellipsis if truncated
                if (result.Length < cleanContent.Length)
                {
                    result += "...";
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating summary");
                return content.Length > maxLength ? content.Substring(0, maxLength - 3) + "..." : content;
            }
        }

        public async Task<HtmlMetaInfo> ExtractMetaInfoAsync(string htmlContent)
        {
            var metaInfo = new HtmlMetaInfo();

            try
            {
                if (string.IsNullOrEmpty(htmlContent))
                    return metaInfo;

                var doc = new HtmlDocument();
                doc.LoadHtml(htmlContent);

                // Extract title
                var titleNode = doc.DocumentNode.SelectSingleNode("//title");
                metaInfo.Title = titleNode?.InnerText?.Trim() ?? "";

                // Extract meta tags
                var metaTags = doc.DocumentNode.SelectNodes("//meta");
                if (metaTags != null)
                {
                    foreach (var meta in metaTags)
                    {
                        var name = meta.GetAttributeValue("name", "").ToLower();
                        var property = meta.GetAttributeValue("property", "").ToLower();
                        var content = meta.GetAttributeValue("content", "");

                        switch (name)
                        {
                            case "description":
                                metaInfo.Description = content;
                                break;
                            case "keywords":
                                metaInfo.Keywords = content;
                                break;
                            case "author":
                                metaInfo.Author = content;
                                break;
                        }

                        switch (property)
                        {
                            case "og:title":
                                metaInfo.OgTitle = content;
                                break;
                            case "og:description":
                                metaInfo.OgDescription = content;
                                break;
                            case "og:image":
                                metaInfo.OgImage = content;
                                break;
                            case "article:published_time":
                                if (DateTime.TryParse(content, out var publishedTime))
                                    metaInfo.PublishedTime = publishedTime;
                                break;
                            case "article:modified_time":
                                if (DateTime.TryParse(content, out var modifiedTime))
                                    metaInfo.ModifiedTime = modifiedTime;
                                break;
                        }

                        // Twitter meta tags
                        if (name.StartsWith("twitter:"))
                        {
                            switch (name)
                            {
                                case "twitter:title":
                                    metaInfo.TwitterTitle = content;
                                    break;
                                case "twitter:description":
                                    metaInfo.TwitterDescription = content;
                                    break;
                                case "twitter:image":
                                    metaInfo.TwitterImage = content;
                                    break;
                            }
                        }
                    }
                }

                // Extract canonical URL
                var canonicalNode = doc.DocumentNode.SelectSingleNode("//link[@rel='canonical']");
                metaInfo.Canonical = canonicalNode?.GetAttributeValue("href", "") ?? "";

                // Extract language
                var htmlNode = doc.DocumentNode.SelectSingleNode("//html");
                metaInfo.Language = htmlNode?.GetAttributeValue("lang", "") ?? "";

                return metaInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting meta information from HTML");
                return metaInfo;
            }
        }

        public async Task<List<string>> ExtractImagesAsync(string htmlContent)
        {
            var images = new List<string>();

            try
            {
                if (string.IsNullOrEmpty(htmlContent))
                    return images;

                var doc = new HtmlDocument();
                doc.LoadHtml(htmlContent);

                var imgNodes = doc.DocumentNode.SelectNodes("//img[@src]");
                if (imgNodes != null)
                {
                    foreach (var img in imgNodes)
                    {
                        var src = img.GetAttributeValue("src", "");
                        if (!string.IsNullOrEmpty(src) && IsValidUrl(src))
                        {
                            images.Add(src);
                        }
                    }
                }

                // Also check for images in CSS background-image
                var elementsWithStyle = doc.DocumentNode.SelectNodes("//*[@style]");
                if (elementsWithStyle != null)
                {
                    var backgroundImageRegex = new Regex(@"background-image:\s*url\(['""](.*?)['""]\)", RegexOptions.IgnoreCase);
                    
                    foreach (var element in elementsWithStyle)
                    {
                        var style = element.GetAttributeValue("style", "");
                        var matches = backgroundImageRegex.Matches(style);
                        
                        foreach (Match match in matches)
                        {
                            var imageUrl = match.Groups[1].Value;
                            if (IsValidUrl(imageUrl))
                            {
                                images.Add(imageUrl);
                            }
                        }
                    }
                }

                return images.Distinct().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting images from HTML");
                return images;
            }
        }

        public async Task<List<string>> ExtractLinksAsync(string htmlContent)
        {
            var links = new List<string>();

            try
            {
                if (string.IsNullOrEmpty(htmlContent))
                    return links;

                var doc = new HtmlDocument();
                doc.LoadHtml(htmlContent);

                var linkNodes = doc.DocumentNode.SelectNodes("//a[@href]");
                if (linkNodes != null)
                {
                    foreach (var link in linkNodes)
                    {
                        var href = link.GetAttributeValue("href", "");
                        if (!string.IsNullOrEmpty(href) && IsValidUrl(href))
                        {
                            links.Add(href);
                        }
                    }
                }

                return links.Distinct().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting links from HTML");
                return links;
            }
        }

        public async Task<string> ConvertRelativeUrlsAsync(string htmlContent, string baseUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(htmlContent) || string.IsNullOrEmpty(baseUrl))
                    return htmlContent;

                if (!Uri.TryCreate(baseUrl, UriKind.Absolute, out var baseUri))
                {
                    _logger.LogWarning("Invalid base URL: {BaseUrl}", baseUrl);
                    return htmlContent;
                }

                var doc = new HtmlDocument();
                doc.LoadHtml(htmlContent);

                // Convert relative URLs in href attributes
                var linkNodes = doc.DocumentNode.SelectNodes("//a[@href]");
                if (linkNodes != null)
                {
                    foreach (var link in linkNodes)
                    {
                        var href = link.GetAttributeValue("href", "");
                        if (!string.IsNullOrEmpty(href) && !Uri.IsWellFormedUriString(href, UriKind.Absolute))
                        {
                            if (Uri.TryCreate(baseUri, href, out var absoluteUri))
                            {
                                link.SetAttributeValue("href", absoluteUri.ToString());
                            }
                        }
                    }
                }

                // Convert relative URLs in src attributes
                var imgNodes = doc.DocumentNode.SelectNodes("//img[@src]");
                if (imgNodes != null)
                {
                    foreach (var img in imgNodes)
                    {
                        var src = img.GetAttributeValue("src", "");
                        if (!string.IsNullOrEmpty(src) && !Uri.IsWellFormedUriString(src, UriKind.Absolute))
                        {
                            if (Uri.TryCreate(baseUri, src, out var absoluteUri))
                            {
                                img.SetAttributeValue("src", absoluteUri.ToString());
                            }
                        }
                    }
                }

                return doc.DocumentNode.OuterHtml;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting relative URLs");
                return htmlContent;
            }
        }

        public async Task<string> NormalizeTextAsync(string text)
        {
            try
            {
                if (string.IsNullOrEmpty(text))
                    return "";

                // Remove excessive whitespace
                text = Regex.Replace(text, @"\s+", " ");
                
                // Remove leading/trailing whitespace
                text = text.Trim();
                
                // Remove common control characters
                text = Regex.Replace(text, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "");
                
                // Normalize line endings
                text = text.Replace("\r\n", "\n").Replace("\r", "\n");
                
                // Remove excessive line breaks
                text = Regex.Replace(text, @"\n{3,}", "\n\n");

                return text;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error normalizing text");
                return text;
            }
        }

        private bool IsValidUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            // Check for dangerous protocols
            var dangerousProtocols = new[] { "javascript:", "data:", "vbscript:", "file:" };
            if (dangerousProtocols.Any(protocol => url.StartsWith(protocol, StringComparison.OrdinalIgnoreCase)))
            {
                return false;
            }

            // Allow relative URLs or absolute HTTP/HTTPS URLs
            return url.StartsWith("/") || 
                   url.StartsWith("./") || 
                   url.StartsWith("../") ||
                   Uri.IsWellFormedUriString(url, UriKind.Absolute);
        }
    }
}