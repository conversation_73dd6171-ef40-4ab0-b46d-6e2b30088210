using NewsSite.Models;
using NewsSite.Data;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace NewsSite.Services.BackgroundServices
{
    public class DuplicateDetectionService : IDuplicateDetectionService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DuplicateDetectionService> _logger;

        // Thresholds for duplicate detection
        private const double SIMILARITY_THRESHOLD = 0.85;
        private const double URL_SIMILARITY_THRESHOLD = 0.95;
        private const double TITLE_SIMILARITY_THRESHOLD = 0.90;

        public DuplicateDetectionService(
            ApplicationDbContext context,
            ILogger<DuplicateDetectionService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<bool> IsDuplicateAsync(Article article)
        {
            try
            {
                // First check for exact URL matches
                var exactUrlMatch = await _context.Articles
                    .AnyAsync(a => a.Url == article.Url && a.Id != article.Id);

                if (exactUrlMatch)
                {
                    _logger.LogDebug("Found exact URL duplicate for: {Title}", article.Title);
                    return true;
                }

                // Generate content hash and check for exact content matches
                var contentHash = GenerateContentHash(article);
                var hashMatch = await _context.Articles
                    .AnyAsync(a => a.ContentHash == contentHash && a.Id != article.Id);

                if (hashMatch)
                {
                    _logger.LogDebug("Found content hash duplicate for: {Title}", article.Title);
                    return true;
                }

                // Check for similar articles using fuzzy matching
                var potentialDuplicates = await FindPotentialDuplicatesAsync(article);
                
                foreach (var duplicate in potentialDuplicates)
                {
                    var similarity = await CalculateSimilarityScoreAsync(article, duplicate);
                    if (similarity >= SIMILARITY_THRESHOLD)
                    {
                        _logger.LogDebug("Found similar duplicate for: {Title} (similarity: {Similarity:F2})", 
                            article.Title, similarity);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for duplicates for article: {Title}", article.Title);
                // In case of error, assume it's not a duplicate to avoid losing content
                return false;
            }
        }

        public async Task<List<Article>> FindPotentialDuplicatesAsync(Article article)
        {
            var potentialDuplicates = new List<Article>();

            try
            {
                // Look for articles with similar titles (published within last 30 days)
                var cutoffDate = DateTime.UtcNow.AddDays(-30);
                var normalizedTitle = NormalizeText(article.Title);

                // Get articles with similar titles or from same source
                var candidates = await _context.Articles
                    .Where(a => a.Id != article.Id && 
                               a.PublishedDate > cutoffDate &&
                               (a.SourceId == article.SourceId || 
                                EF.Functions.Like(a.Title, $"%{normalizedTitle.Substring(0, Math.Min(20, normalizedTitle.Length))}%")))
                    .Take(50) // Limit to avoid performance issues
                    .ToListAsync();

                foreach (var candidate in candidates)
                {
                    // Quick title similarity check
                    var titleSimilarity = CalculateJaccardSimilarity(
                        NormalizeText(article.Title),
                        NormalizeText(candidate.Title));

                    if (titleSimilarity >= 0.6) // Lower threshold for initial filtering
                    {
                        potentialDuplicates.Add(candidate);
                    }
                }

                return potentialDuplicates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding potential duplicates for article: {Title}", article.Title);
                return potentialDuplicates;
            }
        }

        public async Task<double> CalculateSimilarityScoreAsync(Article article1, Article article2)
        {
            try
            {
                var scores = new List<double>();

                // Title similarity (weight: 30%)
                var titleSimilarity = CalculateJaccardSimilarity(
                    NormalizeText(article1.Title),
                    NormalizeText(article2.Title));
                scores.Add(titleSimilarity * 0.3);

                // URL similarity (weight: 25%)
                var urlSimilarity = CalculateLevenshteinSimilarity(article1.Url, article2.Url);
                scores.Add(urlSimilarity * 0.25);

                // Content similarity (weight: 35%)
                var contentSimilarity = CalculateJaccardSimilarity(
                    NormalizeText(article1.Content ?? ""),
                    NormalizeText(article2.Content ?? ""));
                scores.Add(contentSimilarity * 0.35);

                // Author similarity (weight: 5%)
                var authorSimilarity = string.Equals(article1.Author, article2.Author, StringComparison.OrdinalIgnoreCase) ? 1.0 : 0.0;
                scores.Add(authorSimilarity * 0.05);

                // Publication date proximity (weight: 5%)
                var timeDiff = Math.Abs((article1.PublishedDate - article2.PublishedDate).TotalHours);
                var dateSimilarity = timeDiff <= 24 ? 1.0 : Math.Max(0, 1.0 - (timeDiff / (24 * 7))); // Decay over a week
                scores.Add(dateSimilarity * 0.05);

                return scores.Sum();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating similarity between articles");
                return 0;
            }
        }

        public string GenerateContentHash(Article article)
        {
            try
            {
                // Create a normalized version of the content for hashing
                var normalizedContent = new StringBuilder();
                
                // Add title (normalized)
                normalizedContent.AppendLine(NormalizeText(article.Title));
                
                // Add content (normalized, first 1000 chars to avoid huge variations)
                var content = NormalizeText(article.Content ?? "");
                if (content.Length > 1000)
                {
                    content = content.Substring(0, 1000);
                }
                normalizedContent.AppendLine(content);

                // Add author if available
                if (!string.IsNullOrEmpty(article.Author))
                {
                    normalizedContent.AppendLine(article.Author.ToLowerInvariant().Trim());
                }

                // Generate SHA256 hash
                using var sha256 = SHA256.Create();
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(normalizedContent.ToString()));
                return Convert.ToBase64String(hashBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating content hash for article: {Title}", article.Title);
                return Guid.NewGuid().ToString(); // Return unique value if hashing fails
            }
        }

        public async Task<int> DetectAndMarkDuplicatesAsync()
        {
            try
            {
                _logger.LogInformation("Starting duplicate detection process");

                var duplicatesFound = 0;
                var articlesProcessed = 0;
                
                // Process articles in batches to avoid memory issues
                const int batchSize = 100;
                var totalArticles = await _context.Articles.CountAsync();

                for (int skip = 0; skip < totalArticles; skip += batchSize)
                {
                    var articles = await _context.Articles
                        .OrderByDescending(a => a.PublishedDate)
                        .Skip(skip)
                        .Take(batchSize)
                        .ToListAsync();

                    foreach (var article in articles)
                    {
                        // Skip if already marked as duplicate
                        if (article.IsDuplicate)
                            continue;

                        var isDuplicate = await IsDuplicateAsync(article);
                        if (isDuplicate)
                        {
                            article.IsDuplicate = true;
                            duplicatesFound++;
                        }

                        // Update content hash if not set
                        if (string.IsNullOrEmpty(article.ContentHash))
                        {
                            article.ContentHash = GenerateContentHash(article);
                        }

                        articlesProcessed++;
                    }

                    // Save changes for this batch
                    await _context.SaveChangesAsync();

                    // Log progress
                    if (articlesProcessed % 500 == 0)
                    {
                        _logger.LogInformation("Processed {ProcessedCount}/{TotalCount} articles, found {DuplicateCount} duplicates", 
                            articlesProcessed, totalArticles, duplicatesFound);
                    }
                }

                _logger.LogInformation("Duplicate detection completed. Found {DuplicateCount} duplicates out of {ProcessedCount} articles", 
                    duplicatesFound, articlesProcessed);

                return duplicatesFound;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during duplicate detection process");
                throw;
            }
        }

        private string NormalizeText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            // Convert to lowercase
            text = text.ToLowerInvariant();

            // Remove HTML tags
            text = Regex.Replace(text, @"<[^>]*>", " ");

            // Remove extra whitespace and normalize
            text = Regex.Replace(text, @"\s+", " ");

            // Remove punctuation and special characters
            text = Regex.Replace(text, @"[^\w\s]", " ");

            // Remove common stop words
            var stopWords = new HashSet<string> { "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "an", "a", "is", "are", "was", "were", "be", "been", "being", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should" };
            var words = text.Split(' ', StringSplitOptions.RemoveEmptyEntries)
                .Where(word => !stopWords.Contains(word) && word.Length > 2)
                .ToArray();

            return string.Join(" ", words).Trim();
        }

        private double CalculateJaccardSimilarity(string text1, string text2)
        {
            if (string.IsNullOrEmpty(text1) && string.IsNullOrEmpty(text2))
                return 1.0;

            if (string.IsNullOrEmpty(text1) || string.IsNullOrEmpty(text2))
                return 0.0;

            var words1 = new HashSet<string>(text1.Split(' ', StringSplitOptions.RemoveEmptyEntries));
            var words2 = new HashSet<string>(text2.Split(' ', StringSplitOptions.RemoveEmptyEntries));

            var intersection = words1.Intersect(words2).Count();
            var union = words1.Union(words2).Count();

            return union == 0 ? 0 : (double)intersection / union;
        }

        private double CalculateLevenshteinSimilarity(string str1, string str2)
        {
            if (string.IsNullOrEmpty(str1) && string.IsNullOrEmpty(str2))
                return 1.0;

            if (string.IsNullOrEmpty(str1) || string.IsNullOrEmpty(str2))
                return 0.0;

            var distance = CalculateLevenshteinDistance(str1, str2);
            var maxLength = Math.Max(str1.Length, str2.Length);

            return 1.0 - (double)distance / maxLength;
        }

        private int CalculateLevenshteinDistance(string str1, string str2)
        {
            var matrix = new int[str1.Length + 1, str2.Length + 1];

            for (int i = 0; i <= str1.Length; i++)
                matrix[i, 0] = i;

            for (int j = 0; j <= str2.Length; j++)
                matrix[0, j] = j;

            for (int i = 1; i <= str1.Length; i++)
            {
                for (int j = 1; j <= str2.Length; j++)
                {
                    var cost = str1[i - 1] == str2[j - 1] ? 0 : 1;
                    matrix[i, j] = Math.Min(
                        Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost);
                }
            }

            return matrix[str1.Length, str2.Length];
        }
    }
}