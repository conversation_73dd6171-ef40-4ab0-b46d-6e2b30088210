using NewsSite.Models;
using NewsSite.Services.Utilities;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Web;

namespace NewsSite.Services.External
{
    public class YouTubeService : IYouTubeService
    {
        private readonly IHttpService _httpService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<YouTubeService> _logger;

        private const string YOUTUBE_API_BASE_URL = "https://www.googleapis.com/youtube/v3";
        private readonly string _apiKey;

        public YouTubeService(
            IHttpService httpService,
            IConfiguration configuration,
            ILogger<YouTubeService> logger)
        {
            _httpService = httpService;
            _configuration = configuration;
            _logger = logger;
            
            // Get API key from configuration (placeholder for now)
            _apiKey = _configuration["YouTube:ApiKey"] ?? "PLACEHOLDER_YOUTUBE_API_KEY";
            
            if (_apiKey == "PLACEHOLDER_YOUTUBE_API_KEY")
            {
                _logger.LogWarning("YouTube API key not configured. Using placeholder - API calls will fail until real key is provided.");
            }
        }

        public async Task<List<Article>> FetchVideosAsync(SourceConfiguration configuration)
        {
            var articles = new List<Article>();

            try
            {
                if (configuration.ApiSettings == null)
                {
                    _logger.LogWarning("YouTube source configuration missing API settings");
                    return articles;
                }

                // Support different YouTube source types
                var sourceType = configuration.ApiSettings.GetValueOrDefault("type", "channel");
                
                switch (sourceType.ToLower())
                {
                    case "channel":
                        var channelId = configuration.ApiSettings.GetValueOrDefault("channelId", "");
                        if (!string.IsNullOrEmpty(channelId))
                        {
                            articles = await FetchChannelVideosAsync(channelId, configuration.MaxItems ?? 25);
                        }
                        break;
                        
                    case "search":
                        var query = configuration.ApiSettings.GetValueOrDefault("query", "");
                        if (!string.IsNullOrEmpty(query))
                        {
                            articles = await SearchVideosAsync(query, configuration.MaxItems ?? 25);
                        }
                        break;
                        
                    case "playlist":
                        var playlistId = configuration.ApiSettings.GetValueOrDefault("playlistId", "");
                        if (!string.IsNullOrEmpty(playlistId))
                        {
                            articles = await FetchPlaylistVideosAsync(playlistId, configuration.MaxItems ?? 25);
                        }
                        break;
                        
                    default:
                        _logger.LogWarning("Unsupported YouTube source type: {SourceType}", sourceType);
                        break;
                }

                _logger.LogInformation("Fetched {Count} videos from YouTube source", articles.Count);
                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching videos from YouTube");
                return articles;
            }
        }

        public async Task<List<Article>> FetchChannelVideosAsync(string channelId, int maxResults = 25)
        {
            var articles = new List<Article>();

            try
            {
                _logger.LogDebug("Fetching videos from YouTube channel: {ChannelId}", channelId);

                if (_apiKey == "PLACEHOLDER_YOUTUBE_API_KEY")
                {
                    _logger.LogWarning("Cannot fetch YouTube videos - API key not configured");
                    return articles;
                }

                // First, get the channel's uploads playlist ID
                var channelUrl = $"{YOUTUBE_API_BASE_URL}/channels" +
                    $"?part=contentDetails" +
                    $"&id={channelId}" +
                    $"&key={_apiKey}";

                var channelResponse = await _httpService.GetStringAsync(channelUrl);
                if (string.IsNullOrEmpty(channelResponse))
                {
                    _logger.LogWarning("Empty response from YouTube channel API");
                    return articles;
                }

                var channelData = JsonSerializer.Deserialize<YouTubeChannelResponse>(channelResponse);
                if (channelData?.Items?.Any() != true)
                {
                    _logger.LogWarning("No channel data found for: {ChannelId}", channelId);
                    return articles;
                }

                var uploadsPlaylistId = channelData.Items[0].ContentDetails?.RelatedPlaylists?.Uploads;
                if (string.IsNullOrEmpty(uploadsPlaylistId))
                {
                    _logger.LogWarning("No uploads playlist found for channel: {ChannelId}", channelId);
                    return articles;
                }

                // Fetch videos from the uploads playlist
                articles = await FetchPlaylistVideosAsync(uploadsPlaylistId, maxResults);

                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching channel videos: {ChannelId}", channelId);
                return articles;
            }
        }

        public async Task<List<Article>> SearchVideosAsync(string query, int maxResults = 25)
        {
            var articles = new List<Article>();

            try
            {
                _logger.LogDebug("Searching YouTube videos: {Query}", query);

                if (_apiKey == "PLACEHOLDER_YOUTUBE_API_KEY")
                {
                    _logger.LogWarning("Cannot search YouTube videos - API key not configured");
                    return articles;
                }

                var searchUrl = $"{YOUTUBE_API_BASE_URL}/search" +
                    $"?part=snippet" +
                    $"&q={HttpUtility.UrlEncode(query)}" +
                    $"&type=video" +
                    $"&order=relevance" +
                    $"&maxResults={Math.Min(maxResults, 50)}" +
                    $"&key={_apiKey}";

                var searchResponse = await _httpService.GetStringAsync(searchUrl);
                if (string.IsNullOrEmpty(searchResponse))
                {
                    _logger.LogWarning("Empty response from YouTube search API");
                    return articles;
                }

                var searchData = JsonSerializer.Deserialize<YouTubeSearchResponse>(searchResponse);
                if (searchData?.Items?.Any() != true)
                {
                    _logger.LogWarning("No search results found for: {Query}", query);
                    return articles;
                }

                foreach (var item in searchData.Items)
                {
                    var article = ConvertYouTubeItemToArticle(item);
                    if (article != null)
                    {
                        articles.Add(article);
                    }
                }

                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching YouTube videos: {Query}", query);
                return articles;
            }
        }

        public async Task<YouTubeVideoInfo?> GetVideoInfoAsync(string videoId)
        {
            try
            {
                _logger.LogDebug("Getting YouTube video info: {VideoId}", videoId);

                if (_apiKey == "PLACEHOLDER_YOUTUBE_API_KEY")
                {
                    _logger.LogWarning("Cannot get YouTube video info - API key not configured");
                    return null;
                }

                var videoUrl = $"{YOUTUBE_API_BASE_URL}/videos" +
                    $"?part=snippet,contentDetails,statistics" +
                    $"&id={videoId}" +
                    $"&key={_apiKey}";

                var videoResponse = await _httpService.GetStringAsync(videoUrl);
                if (string.IsNullOrEmpty(videoResponse))
                {
                    return null;
                }

                var videoData = JsonSerializer.Deserialize<YouTubeVideoResponse>(videoResponse);
                if (videoData?.Items?.Any() != true)
                {
                    return null;
                }

                var video = videoData.Items[0];
                return new YouTubeVideoInfo
                {
                    VideoId = video.Id ?? "",
                    Title = video.Snippet?.Title ?? "",
                    Description = video.Snippet?.Description ?? "",
                    ChannelTitle = video.Snippet?.ChannelTitle ?? "",
                    ChannelId = video.Snippet?.ChannelId ?? "",
                    PublishedAt = video.Snippet?.PublishedAt ?? DateTime.UtcNow,
                    ThumbnailUrl = video.Snippet?.Thumbnails?.High?.Url ?? "",
                    Duration = ParseDuration(video.ContentDetails?.Duration ?? ""),
                    ViewCount = long.TryParse(video.Statistics?.ViewCount, out var views) ? views : 0,
                    LikeCount = long.TryParse(video.Statistics?.LikeCount, out var likes) ? likes : 0,
                    Tags = video.Snippet?.Tags ?? new List<string>(),
                    CategoryName = video.Snippet?.CategoryId ?? ""
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting video info: {VideoId}", videoId);
                return null;
            }
        }

        public async Task<bool> ValidateChannelAsync(string channelId)
        {
            try
            {
                if (_apiKey == "PLACEHOLDER_YOUTUBE_API_KEY")
                {
                    _logger.LogWarning("Cannot validate YouTube channel - API key not configured");
                    return false;
                }

                var channelUrl = $"{YOUTUBE_API_BASE_URL}/channels" +
                    $"?part=id" +
                    $"&id={channelId}" +
                    $"&key={_apiKey}";

                var response = await _httpService.GetStringAsync(channelUrl);
                if (string.IsNullOrEmpty(response))
                {
                    return false;
                }

                var channelData = JsonSerializer.Deserialize<YouTubeChannelResponse>(response);
                return channelData?.Items?.Any() == true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating YouTube channel: {ChannelId}", channelId);
                return false;
            }
        }

        private async Task<List<Article>> FetchPlaylistVideosAsync(string playlistId, int maxResults)
        {
            var articles = new List<Article>();

            try
            {
                var playlistUrl = $"{YOUTUBE_API_BASE_URL}/playlistItems" +
                    $"?part=snippet" +
                    $"&playlistId={playlistId}" +
                    $"&maxResults={Math.Min(maxResults, 50)}" +
                    $"&key={_apiKey}";

                var playlistResponse = await _httpService.GetStringAsync(playlistUrl);
                if (string.IsNullOrEmpty(playlistResponse))
                {
                    return articles;
                }

                var playlistData = JsonSerializer.Deserialize<YouTubePlaylistResponse>(playlistResponse);
                if (playlistData?.Items?.Any() != true)
                {
                    return articles;
                }

                foreach (var item in playlistData.Items)
                {
                    var article = ConvertPlaylistItemToArticle(item);
                    if (article != null)
                    {
                        articles.Add(article);
                    }
                }

                return articles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching playlist videos: {PlaylistId}", playlistId);
                return articles;
            }
        }

        private Article? ConvertYouTubeItemToArticle(YouTubeSearchItem item)
        {
            try
            {
                if (item.Snippet == null || string.IsNullOrEmpty(item.Id?.VideoId))
                {
                    return null;
                }

                return new Article
                {
                    Title = item.Snippet.Title ?? "",
                    Content = item.Snippet.Description ?? "",
                    Summary = TruncateDescription(item.Snippet.Description ?? "", 200),
                    Url = $"https://www.youtube.com/watch?v={item.Id.VideoId}",
                    ImageUrl = item.Snippet.Thumbnails?.High?.Url ?? item.Snippet.Thumbnails?.Default?.Url,
                    Author = item.Snippet.ChannelTitle ?? "",
                    PublishedDate = item.Snippet.PublishedAt ?? DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    ViewCount = 0 // Will need separate API call to get statistics
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting YouTube search item to article");
                return null;
            }
        }

        private Article? ConvertPlaylistItemToArticle(YouTubePlaylistItem item)
        {
            try
            {
                if (item.Snippet == null || string.IsNullOrEmpty(item.Snippet.ResourceId?.VideoId))
                {
                    return null;
                }

                return new Article
                {
                    Title = item.Snippet.Title ?? "",
                    Content = item.Snippet.Description ?? "",
                    Summary = TruncateDescription(item.Snippet.Description ?? "", 200),
                    Url = $"https://www.youtube.com/watch?v={item.Snippet.ResourceId.VideoId}",
                    ImageUrl = item.Snippet.Thumbnails?.High?.Url ?? item.Snippet.Thumbnails?.Default?.Url,
                    Author = item.Snippet.ChannelTitle ?? "",
                    PublishedDate = item.Snippet.PublishedAt ?? DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    ViewCount = 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting YouTube playlist item to article");
                return null;
            }
        }

        private string TruncateDescription(string description, int maxLength)
        {
            if (string.IsNullOrEmpty(description) || description.Length <= maxLength)
            {
                return description;
            }

            return description.Substring(0, maxLength - 3) + "...";
        }

        private TimeSpan ParseDuration(string duration)
        {
            try
            {
                // YouTube uses ISO 8601 duration format (PT4M13S)
                if (string.IsNullOrEmpty(duration) || !duration.StartsWith("PT"))
                {
                    return TimeSpan.Zero;
                }

                var durationStr = duration.Substring(2); // Remove "PT"
                var hours = 0;
                var minutes = 0;
                var seconds = 0;

                if (durationStr.Contains("H"))
                {
                    var hIndex = durationStr.IndexOf("H");
                    int.TryParse(durationStr.Substring(0, hIndex), out hours);
                    durationStr = durationStr.Substring(hIndex + 1);
                }

                if (durationStr.Contains("M"))
                {
                    var mIndex = durationStr.IndexOf("M");
                    int.TryParse(durationStr.Substring(0, mIndex), out minutes);
                    durationStr = durationStr.Substring(mIndex + 1);
                }

                if (durationStr.Contains("S"))
                {
                    var sIndex = durationStr.IndexOf("S");
                    int.TryParse(durationStr.Substring(0, sIndex), out seconds);
                }

                return new TimeSpan(hours, minutes, seconds);
            }
            catch
            {
                return TimeSpan.Zero;
            }
        }
    }

    // YouTube API Response Models
    public class YouTubeChannelResponse
    {
        [JsonPropertyName("items")]
        public List<YouTubeChannel> Items { get; set; } = new();
    }

    public class YouTubeChannel
    {
        [JsonPropertyName("contentDetails")]
        public YouTubeChannelContentDetails? ContentDetails { get; set; }
    }

    public class YouTubeChannelContentDetails
    {
        [JsonPropertyName("relatedPlaylists")]
        public YouTubeRelatedPlaylists? RelatedPlaylists { get; set; }
    }

    public class YouTubeRelatedPlaylists
    {
        [JsonPropertyName("uploads")]
        public string? Uploads { get; set; }
    }

    public class YouTubeSearchResponse
    {
        [JsonPropertyName("items")]
        public List<YouTubeSearchItem> Items { get; set; } = new();
    }

    public class YouTubeSearchItem
    {
        [JsonPropertyName("id")]
        public YouTubeId? Id { get; set; }

        [JsonPropertyName("snippet")]
        public YouTubeSnippet? Snippet { get; set; }
    }

    public class YouTubeId
    {
        [JsonPropertyName("videoId")]
        public string? VideoId { get; set; }
    }

    public class YouTubePlaylistResponse
    {
        [JsonPropertyName("items")]
        public List<YouTubePlaylistItem> Items { get; set; } = new();
    }

    public class YouTubePlaylistItem
    {
        [JsonPropertyName("snippet")]
        public YouTubePlaylistSnippet? Snippet { get; set; }
    }

    public class YouTubePlaylistSnippet : YouTubeSnippet
    {
        [JsonPropertyName("resourceId")]
        public YouTubeResourceId? ResourceId { get; set; }
    }

    public class YouTubeResourceId
    {
        [JsonPropertyName("videoId")]
        public string? VideoId { get; set; }
    }

    public class YouTubeVideoResponse
    {
        [JsonPropertyName("items")]
        public List<YouTubeVideo> Items { get; set; } = new();
    }

    public class YouTubeVideo
    {
        [JsonPropertyName("id")]
        public string? Id { get; set; }

        [JsonPropertyName("snippet")]
        public YouTubeSnippet? Snippet { get; set; }

        [JsonPropertyName("contentDetails")]
        public YouTubeVideoContentDetails? ContentDetails { get; set; }

        [JsonPropertyName("statistics")]
        public YouTubeStatistics? Statistics { get; set; }
    }

    public class YouTubeSnippet
    {
        [JsonPropertyName("title")]
        public string? Title { get; set; }

        [JsonPropertyName("description")]
        public string? Description { get; set; }

        [JsonPropertyName("channelTitle")]
        public string? ChannelTitle { get; set; }

        [JsonPropertyName("channelId")]
        public string? ChannelId { get; set; }

        [JsonPropertyName("publishedAt")]
        public DateTime? PublishedAt { get; set; }

        [JsonPropertyName("thumbnails")]
        public YouTubeThumbnails? Thumbnails { get; set; }

        [JsonPropertyName("tags")]
        public List<string> Tags { get; set; } = new();

        [JsonPropertyName("categoryId")]
        public string? CategoryId { get; set; }
    }

    public class YouTubeThumbnails
    {
        [JsonPropertyName("default")]
        public YouTubeThumbnail? Default { get; set; }

        [JsonPropertyName("high")]
        public YouTubeThumbnail? High { get; set; }
    }

    public class YouTubeThumbnail
    {
        [JsonPropertyName("url")]
        public string? Url { get; set; }
    }

    public class YouTubeVideoContentDetails
    {
        [JsonPropertyName("duration")]
        public string? Duration { get; set; }
    }

    public class YouTubeStatistics
    {
        [JsonPropertyName("viewCount")]
        public string? ViewCount { get; set; }

        [JsonPropertyName("likeCount")]
        public string? LikeCount { get; set; }
    }
}